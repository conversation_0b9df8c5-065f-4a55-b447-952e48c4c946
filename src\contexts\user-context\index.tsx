"use client";
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { dataProviderInstance } from "@providers/data-provider";

interface UserContextType {
  role: string | null;
  userId: string | null;
  isLoading: boolean;
  refreshUserInfo: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [role, setRole] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUserInfo = async () => {
    try {
      // Check cache first
      const cachedRole = localStorage.getItem("userRole");
      const cachedUserId = localStorage.getItem("userId");
      const cacheTimestamp = localStorage.getItem("userRoleTimestamp");
      const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

      if (cachedRole && cachedUserId && cacheTimestamp) {
        const isExpired = Date.now() - parseInt(cacheTimestamp) > CACHE_DURATION;
        if (!isExpired) {
          setRole(cachedRole);
          setUserId(cachedUserId);
          setIsLoading(false);
          return;
        }
      }

      // Fetch from localStorage user data
      const userData = JSON.parse(localStorage.getItem("user") || "{}");
      const currentUserId = userData.id;

      if (!currentUserId) {
        setIsLoading(false);
        return;
      }

      setUserId(currentUserId);

      // Fetch role from API
      const response = await dataProviderInstance.getUserInfo(currentUserId);
      const userRole = response.data.data.party_type_key;

      // Cache the results
      localStorage.setItem("userRole", userRole);
      localStorage.setItem("userId", currentUserId);
      localStorage.setItem("userRoleTimestamp", Date.now().toString());

      setRole(userRole);
    } catch (error) {
      console.error("Error fetching user info:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUserInfo = async () => {
    // Clear cache and refetch
    localStorage.removeItem("userRole");
    localStorage.removeItem("userRoleTimestamp");
    setIsLoading(true);
    await fetchUserInfo();
  };

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const value: UserContextType = {
    role,
    userId,
    isLoading,
    refreshUserInfo,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
