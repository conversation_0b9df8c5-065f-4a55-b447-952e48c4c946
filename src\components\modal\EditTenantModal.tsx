import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Select,
  Upload,
  Row,
  Col,
  message,
  Typography,
} from "antd";
import { UploadOutlined, EditOutlined } from "@ant-design/icons";
import { EditTenantFormValues, EditTenantModalProps, Tenant } from "@types";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { dataProviderInstance } from "@providers/data-provider";
import { supabaseClient } from "@utils/supabase/client";

const { TextArea } = Input;
const { Text } = Typography;

const EditTenantModal: React.FC<EditTenantModalProps> = ({
  visible,
  onClose,
  tenant,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);

  useEffect(() => {
    if (visible && tenant) {
      prefillFormData(tenant);
    }
  }, [visible, tenant]);

  const prefillFormData = (tenantData: Tenant) => {
    setUploadedImageUrl(tenantData.avatar_url || "");

    const phoneParts = tenantData.phone?.split(" ") || [];
    const countryCode = phoneParts[0] || "+91";
    const phoneNumber = phoneParts.slice(1).join(" ") || "";

    form.setFieldsValue({
      name: tenantData.name,
      mobile: phoneNumber,
      countryCode,
      address: tenantData.address || "",
      tenant_email: tenantData.email || "",
      tenant_code: tenantData.tenantCode,
    });
  };

  const handleClose = () => {
    form.resetFields();
    setUploadedImageUrl("");
    setImageFile(null);
    onClose();
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  const handleFinish = async (values: EditTenantFormValues) => {
    if (!tenant) return;

    try {
      let imageUrl = uploadedImageUrl;

      if (imageFile) {
        const fileName = `tenant-avatars/${tenant.id}-${Date.now()}_${
          imageFile.name
        }`;
        const { error: uploadError } = await supabaseClient.storage
          .from("image-bucket")
          .upload(fileName, imageFile, {
            cacheControl: "3600",
            upsert: true,
          });

        if (uploadError) {
          message.error("Failed to upload image.");
          return;
        }

        const { data: signedUrlData, error: signedUrlError } =
          await supabaseClient.storage
            .from("image-bucket")
            .createSignedUrl(fileName, 60 * 60 * 157680000); // 5 years expiry

        if (signedUrlData && signedUrlData.signedUrl) {
          imageUrl = signedUrlData.signedUrl;
        } else {
          console.log(signedUrlError);
          throw new Error("Failed to retrieve signed URL for uploaded image.");
        }
      }

      const updatePayload = {
        party_id: String(tenant.id),
        name: values.name,
        phn_number: `${values.countryCode} ${values.mobile}`,
        address: values.address,
        email: values.tenant_email,
        image_url: imageUrl,
      };

      await dataProviderInstance.updateTenant(updatePayload);

      onSuccess();
      handleClose();
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error("Update failed", error.message);
        message.error(`Failed to update tenant: ${error.message}`);
      } else {
        console.error("Update failed", error);
        message.error("Failed to update tenant");
      }
    }
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={800}
      centered
      styles={{
        content: {
          borderRadius: "20px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          padding: "2px",
          border: "none",
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
        },
        header: {
          borderBottom: "none",
          padding: "0",
          background: "transparent",
        },
        body: {
          padding: "0",
          background: "white",
          borderRadius: "18px",
          margin: "0",
        },
      }}
    >
      <div
        style={{
          background: "white",
          borderRadius: "18px",
          overflow: "hidden",
          minHeight: "480px",
        }}
      >
        {/* Header */}
        <div
          style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            padding: "16px 24px",
            color: "white",
            display: "flex",
            alignItems: "center",
            gap: "12px",
          }}
        >
          <div
            style={{
              width: "36px",
              height: "36px",
              borderRadius: "10px",
              background: "rgba(255, 255, 255, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backdropFilter: "blur(10px)",
            }}
          >
            <EditOutlined style={{ fontSize: "18px", color: "white" }} />
          </div>
          <div>
            <Typography.Title
              level={3}
              style={{
                margin: 0,
                color: "white",
                fontWeight: 700,
                fontSize: "28px",
              }}
            >
              Edit Organization
            </Typography.Title>
            <Typography.Text
              style={{
                color: "rgba(255, 255, 255, 0.8)",
                fontSize: "16px",
              }}
            >
              Update organization details and configuration
            </Typography.Text>
          </div>
        </div>

        {/* Content */}
        <div style={{ padding: "20px" }}>
          <div
            style={{
              background: "rgba(255, 255, 255, 0.95)",
              borderRadius: "16px",
              padding: "24px",
              border: "1px solid rgba(226, 232, 240, 0.3)",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
            }}
          >
            <Form
              form={form}
              layout="vertical"
              style={{ width: "100%" }}
              onFinish={handleFinish}
            >
              <Row gutter={20}>
                <Col xs={24} md={15}>
                  <Form.Item
                    name="name"
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                      >
                        Organization Name
                        <span style={{ color: "#ef4444" }}>*</span>
                      </span>
                    }
                    style={{ marginBottom: 16 }}
                    rules={[{ required: true, message: "Name is required" }]}
                  >
                    <Input
                      placeholder="Enter organization name"
                      style={{
                        borderRadius: "8px",
                        height: "40px",
                        border: "2px solid #e5e7eb",
                        fontSize: "14px",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow =
                          "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="tenant_email"
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                      >
                        Email Address
                        <span style={{ color: "#ef4444" }}>*</span>
                      </span>
                    }
                    style={{ marginBottom: 16 }}
                    rules={[
                      { required: true, message: "Email is required" },
                      {
                        type: "email",
                        message: "Please enter a valid email address",
                      },
                    ]}
                  >
                    <Input
                      placeholder="Enter email address"
                      style={{
                        borderRadius: "8px",
                        height: "40px",
                        border: "2px solid #e5e7eb",
                        fontSize: "14px",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow =
                          "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="address"
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                      >
                        Address
                        <span style={{ color: "#ef4444" }}>*</span>
                      </span>
                    }
                    style={{ marginBottom: 16 }}
                    rules={[{ required: true, message: "Address is required" }]}
                  >
                    <TextArea
                      placeholder="Enter complete address"
                      rows={3}
                      style={{
                        borderRadius: "8px",
                        border: "2px solid #e5e7eb",
                        fontSize: "14px",
                        resize: "none",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow =
                          "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                </Col>

                <Col
                  xs={24}
                  md={8}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                    justifyContent: "space-between",
                  }}
                >
                  <div
                    style={{
                      background: "rgba(255, 255, 255, 0.8)",
                      borderRadius: "12px",
                      padding: "16px",
                      border: "2px dashed #e5e7eb",
                      textAlign: "center",
                      marginBottom: "12px",
                    }}
                  >
                    <div
                      style={{
                        width: "100%",
                        height: "160px",
                        borderRadius: "8px",
                        overflow: "hidden",
                        background: "#f8fafc",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginBottom: "12px",
                      }}
                    >
                      <img
                        src={uploadedImageUrl || "/assets/upload.png"}
                        alt="Organization Profile Preview"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: uploadedImageUrl ? "cover" : "contain",
                          borderRadius: "12px",
                        }}
                      />
                    </div>
                    <Form.Item
                      name="profileImage"
                      label={
                        <Text
                          style={{
                            color: "#374151",
                            fontWeight: 600,
                            fontSize: "14px",
                          }}
                        >
                          Organization Profile Image
                        </Text>
                      }
                      style={{ marginBottom: 0 }}
                    >
                      <Upload {...uploadProps}>
                        <Button
                          icon={<UploadOutlined />}
                          style={{
                            width: "100%",
                            height: "44px",
                            borderRadius: "12px",
                            border: "2px solid #667eea",
                            color: "#667eea",
                            fontWeight: 600,
                            background: "rgba(102, 126, 234, 0.05)",
                            transition: "all 0.3s ease",
                          }}
                          onMouseEnter={(e) => {
                            const target = e.currentTarget;
                            target.style.background = "#667eea";
                            target.style.color = "white";
                          }}
                          onMouseLeave={(e) => {
                            const target = e.currentTarget;
                            target.style.background =
                              "rgba(102, 126, 234, 0.05)";
                            target.style.color = "#667eea";
                          }}
                        >
                          Choose Image
                        </Button>
                      </Upload>
                    </Form.Item>
                  </div>
                </Col>

                <Col xs={24} md={15}>
                  <Form.Item
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                      >
                        Phone Number
                        <span style={{ color: "#ef4444" }}>*</span>
                      </span>
                    }
                    style={{ marginBottom: 16 }}
                    required
                  >
                    <div style={{ display: "flex" }}>
                      <Form.Item
                        name="countryCode"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: "Country code is required",
                          },
                        ]}
                        initialValue="+91"
                      >
                        <Select
                          size="large"
                          style={{
                            width: 120,
                            borderRadius: "12px",
                            fontSize: 16,
                          }}
                        >
                          <Select.Option value="+1">🇺🇸 +1</Select.Option>
                          <Select.Option value="+44">🇬🇧 +44</Select.Option>
                          <Select.Option value="+91">🇮🇳 +91</Select.Option>
                          <Select.Option value="+61">🇦🇺 +61</Select.Option>
                          <Select.Option value="+81">🇯🇵 +81</Select.Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        name="mobile"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: "Phone number is required",
                          },
                          {
                            pattern: /^\d{7,14}$/,
                            message: "Enter a valid phone number (7-14 digits)",
                          },
                        ]}
                      >
                        <Input
                          size="large"
                          style={{
                            width: "calc(100% - 120px)",
                            borderRadius: "12px",
                            border: "2px solid #e5e7eb",
                            transition: "all 0.3s ease",
                          }}
                          placeholder="Enter phone number"
                          maxLength={14}
                          minLength={7}
                          type="tel"
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </div>
                  </Form.Item>
                </Col>

                <Col xs={24} md={8}>
                  <Form.Item
                    name="tenant_code"
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                        }}
                      >
                        Organization Code
                      </Text>
                    }
                    style={{ marginBottom: 16 }}
                    rules={[
                      {
                        required: true,
                        message: "Organization Code is required",
                      },
                      {
                        pattern: /^TN-\d{4}$/,
                        message:
                          "Organization Code must be in the format TN-0001",
                      },
                    ]}
                  >
                    <Input
                      type="text"
                      disabled={true}
                      style={{
                        borderRadius: "8px",
                        height: "40px",
                        border: "2px solid #e5e7eb",
                        fontSize: "14px",
                        background: "rgba(248, 250, 252, 0.8)",
                        color: "#6b7280",
                        fontWeight: 600,
                      }}
                    />
                  </Form.Item>
                  <Text
                    style={{
                      color: "#6b7280",
                      fontSize: "12px",
                      fontStyle: "italic",
                    }}
                  >
                    Auto-generated organization code
                  </Text>
                </Col>
              </Row>

              {/* Submit Button */}
              <div
                style={{
                  textAlign: "center",
                  marginTop: 20,
                  padding: "16px",
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "12px",
                  border: "1px solid rgba(226, 232, 240, 0.3)",
                }}
              >
                <Row gutter={12} justify="center">
                  <Col xs={24} sm={12} md={8}>
                    <Button
                      style={{
                        width: "100%",
                        height: "38px",
                        borderRadius: "10px",
                        border: "2px solid #e5e7eb",
                        background: "white",
                        color: "#6b7280",
                        fontSize: "14px",
                        fontWeight: 600,
                        transition: "all 0.3s ease",
                      }}
                      onClick={handleClose}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.borderColor = "#ef4444";
                        target.style.color = "#ef4444";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.color = "#6b7280";
                      }}
                    >
                      Cancel
                    </Button>
                  </Col>
                  <Col xs={24} sm={12} md={8}>
                    <Button
                      type="primary"
                      size="large"
                      htmlType="submit"
                      style={{
                        width: "100%",
                        background:
                          "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        border: "none",
                        borderRadius: "10px",
                        height: "38px",
                        fontSize: "14px",
                        fontWeight: 600,
                        boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                        transition: "all 0.3s ease",
                      }}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(-2px)";
                        target.style.boxShadow =
                          "0 8px 25px rgba(102, 126, 234, 0.5)";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(0)";
                        target.style.boxShadow =
                          "0 4px 15px rgba(102, 126, 234, 0.4)";
                      }}
                    >
                      Update Organization
                    </Button>
                  </Col>
                </Row>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditTenantModal;
