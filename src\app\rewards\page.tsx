"use client";
import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  Table,
  Card,
  Layout,
  Spin,
} from "antd";
import { GiftOutlined, TrophyOutlined, StarOutlined } from "@ant-design/icons";
import { dataProviderInstance } from "@/providers/data-provider";
import {
  RewardCategoryItem,
  RewardFormValues,
  CreateRewardRequest,
  RewardItem,
} from "@types";
import { useNotification } from "@refinedev/core";

const { Title, Text } = Typography;
const { Content } = Layout;

// Enhanced rewards table columns
const rewardColumns = [
  {
    title: "Reward",
    key: "reward_info",
    width: 250,
    render: (record: RewardItem) => (
      <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
        <div style={{
          width: "40px",
          height: "40px",
          borderRadius: "12px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}>
          <GiftOutlined style={{ color: "white", fontSize: "16px" }} />
        </div>
        <div>
          <Text style={{
            fontWeight: 600,
            color: "#374151",
            fontSize: "14px",
            display: "block",
          }}>
            {record.name}
          </Text>
          <Text style={{
            color: "#6b7280",
            fontSize: "12px",
          }}>
            {record.category_name || "No category"}
          </Text>
        </div>
      </div>
    ),
  },
  {
    title: "Points Required",
    dataIndex: "points_required",
    key: "points_required",
    width: 150,
    render: (points_required: number) => (
      <div style={{
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "8px 12px",
        background: "rgba(102, 126, 234, 0.1)",
        borderRadius: "20px",
        justifyContent: "center",
        width: "fit-content",
      }}>
        <StarOutlined style={{ color: "#667eea", fontSize: "14px" }} />
        <Text style={{
          color: "#667eea",
          fontSize: "16px",
          fontWeight: 700,
        }}>
          {points_required?.toLocaleString() || 0}
        </Text>
      </div>
    ),
  },
  {
    title: "Activity Type",
    dataIndex: "activity_type",
    key: "activity_type",
    width: 200,
    render: (activity_type: string) => (
      <div style={{
        padding: "6px 12px",
        background: "rgba(16, 185, 129, 0.1)",
        borderRadius: "12px",
        display: "inline-block",
      }}>
        <Text style={{
          color: "#10b981",
          fontSize: "12px",
          fontWeight: 600,
        }}>
          {activity_type || "Not specified"}
        </Text>
      </div>
    ),
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
    width: 300,
    render: (description: string) => (
      <Text style={{
        color: "#6b7280",
        fontSize: "14px",
        lineHeight: "1.4",
      }}>
        {description || "No description provided"}
      </Text>
    ),
  },
];

export default function ManageRewardsPage() {
  const [form] = Form.useForm();
  const { open } = useNotification();
  const [rewardCategories, setRewardCategories] = useState<
    RewardCategoryItem[]
  >([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [rewards, setRewards] = useState<RewardItem[]>([]);
  const [rewardsLoading, setRewardsLoading] = useState(true);
  const [rewardsError, setRewardsError] = useState<string | null>(null);

  // Fetch reward categories on component mount
  const fetchRewardCategories = async () => {
    try {
      setCategoriesLoading(true);
      setCategoriesError(null);
      const response = await dataProviderInstance.listRewardCategories();
      setRewardCategories(response.data || []);
      console.log("Reward categories fetched:", response.data);
    } catch (error) {
      console.error("Error fetching reward categories:", error);
      setCategoriesError("Failed to load categories");
      setRewardCategories([]);
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Fetch rewards list
  const fetchRewards = async () => {
    try {
      setRewardsLoading(true);
      setRewardsError(null);
      const response = await dataProviderInstance.listRewards();
      setRewards(response.data || []);
      console.log("Rewards fetched:", response.data);
    } catch (error) {
      console.error("Error fetching rewards:", error);
      setRewardsError("Failed to load rewards");
      setRewards([]);
    } finally {
      setRewardsLoading(false);
    }
  };

  useEffect(() => {
    fetchRewardCategories();
    fetchRewards();
  }, []);

  // Handle form submission
  const handleSubmit = async (values: RewardFormValues) => {
    try {
      setSubmitting(true);

      // Transform form values to API request format
      const requestData: CreateRewardRequest = {
        category_id: values.category,
        name: values.name,
        points:
          typeof values.points === "string"
            ? parseInt(values.points)
            : values.points,
        activity_type: values.activityType,
        description: values.description || undefined,
      };

      console.log("Submitting reward data:", requestData);

      const response = await dataProviderInstance.createReward(requestData);

      console.log("Reward created successfully:", response.data);

      open?.({
        type: "success",
        message: "Reward created successfully!",
        description: `"${values.name}" has been added to the rewards system.`,
      });

      // Reset form after successful submission
      form.resetFields();

      // Refresh rewards list
      fetchRewards();
    } catch (error) {
      console.error("Error creating reward:", error);

      open?.({
        type: "error",
        message: "Failed to create reward",
        description: "Please check your input and try again.",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Transform categories for Select component
  const categoryOptions = rewardCategories.map((category) => ({
    value: category.id,
    label: category.description,
  }));

  // Add fallback options if no categories are loaded
  const selectOptions =
    categoryOptions.length > 0
      ? categoryOptions
      : [{ value: "", label: "No categories available", disabled: true }];

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "150px",
          height: "150px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1400,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header Section */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",
          borderRadius: "24px",
          padding: "40px 32px",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "16px",
            marginBottom: "16px",
          }}>
            <div style={{
              width: "64px",
              height: "64px",
              borderRadius: "16px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}>
              <TrophyOutlined style={{ color: "white", fontSize: "28px" }} />
            </div>
            <Title
              level={1}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                margin: 0,
                fontSize: "42px",
                letterSpacing: "-1px",
              }}
            >
              Rewards Management
            </Title>
          </div>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              lineHeight: "1.6",
            }}
          >
            Create and manage rewards to motivate tenant engagement and activity
          </Text>
        </div>

        {/* Create Reward Form Section */}
        <div
          style={{
            marginBottom: 48,
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
          }}
        >
          <div style={{
            display: "flex",
            alignItems: "center",
            gap: "12px",
            marginBottom: "32px",
            justifyContent: "center",
          }}>
            <div style={{
              width: "48px",
              height: "48px",
              borderRadius: "12px",
              background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}>
              <GiftOutlined style={{ color: "white", fontSize: "20px" }} />
            </div>
            <Title level={3} style={{
              margin: 0,
              color: "#374151",
              fontWeight: 700,
              fontSize: "24px",
            }}>
              Create New Reward
            </Title>
          </div>

          <Form form={form} layout="vertical" onFinish={handleSubmit}>
            <Row gutter={32}>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <Text style={{
                      color: "#374151",
                      fontWeight: 600,
                      fontSize: "14px"
                    }}>
                      Category
                      {!categoriesLoading && categoryOptions.length > 0 && (
                        <Text
                          style={{
                            color: "#6b7280",
                            fontWeight: 400,
                            fontSize: "12px",
                            marginLeft: "8px",
                          }}
                        >
                          ({categoryOptions.length} available)
                        </Text>
                      )}
                    </Text>
                  }
                  name="category"
                  style={{ marginBottom: 24 }}
                  rules={[
                    { required: true, message: "Please select a category" },
                  ]}
                  help={
                    categoriesError && (
                      <span style={{ color: "#dc2626" }}>
                        {categoriesError}.{" "}
                        <Button
                          type="link"
                          size="small"
                          onClick={fetchRewardCategories}
                          style={{ padding: 0 }}
                        >
                          Click to retry
                        </Button>
                      </span>
                    )
                  }
                  validateStatus={categoriesError ? "error" : ""}
                >
                  <Select
                    placeholder={
                      categoriesLoading
                        ? "Loading categories..."
                        : "Select category"
                    }
                    loading={categoriesLoading}
                    options={selectOptions}
                    disabled={categoriesLoading || categoryOptions.length === 0}
                    notFoundContent={
                      categoriesLoading ? "Loading..." : "No categories found"
                    }
                    style={{
                      height: "48px",
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <Text style={{
                      color: "#374151",
                      fontWeight: 600,
                      fontSize: "14px"
                    }}>
                      Reward Name
                    </Text>
                  }
                  name="name"
                  style={{ marginBottom: 24 }}
                  rules={[
                    { required: true, message: "Reward name is required" },
                    { min: 3, message: "Name must be at least 3 characters" },
                    { max: 100, message: "Name must not exceed 100 characters" },
                  ]}
                >
                  <Input
                    placeholder="Enter reward name"
                    style={{
                      borderRadius: "12px",
                      height: "48px",
                      border: "2px solid #e5e7eb",
                      fontSize: "16px",
                      transition: "all 0.3s ease",
                    }}
                    onFocus={(e) => {
                      const target = e.target as HTMLInputElement;
                      target.style.borderColor = "#667eea";
                      target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                    }}
                    onBlur={(e) => {
                      const target = e.target as HTMLInputElement;
                      target.style.borderColor = "#e5e7eb";
                      target.style.boxShadow = "none";
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <Text style={{
                      color: "#374151",
                      fontWeight: 600,
                      fontSize: "14px"
                    }}>
                      Points Required
                    </Text>
                  }
                  name="points"
                  style={{ marginBottom: 24 }}
                  rules={[
                    { required: true, message: "Points threshold is required" },
                    {
                      pattern: /^\d+$/,
                      message: "Points must be a positive number",
                    },
                    {
                      validator: (_, value) => {
                        if (value && parseInt(value) <= 0) {
                          return Promise.reject(
                            new Error("Points must be greater than 0")
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Enter points required (e.g. 2000)"
                    type="number"
                    min="1"
                    style={{
                      borderRadius: "12px",
                      height: "48px",
                      border: "2px solid #e5e7eb",
                      fontSize: "16px",
                      transition: "all 0.3s ease",
                    }}
                    onFocus={(e) => {
                      const target = e.target as HTMLInputElement;
                      target.style.borderColor = "#667eea";
                      target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                    }}
                    onBlur={(e) => {
                      const target = e.target as HTMLInputElement;
                      target.style.borderColor = "#e5e7eb";
                      target.style.boxShadow = "none";
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <Text style={{
                      color: "#374151",
                      fontWeight: 600,
                      fontSize: "14px"
                    }}>
                      Activity Type
                    </Text>
                  }
                  name="activityType"
                  style={{ marginBottom: 24 }}
                  rules={[
                    { required: true, message: "Activity type is required" },
                    {
                      min: 3,
                      message: "Activity type must be at least 3 characters",
                    },
                    {
                      max: 50,
                      message: "Activity type must not exceed 50 characters",
                    },
                  ]}
                >
                  <Input
                    placeholder="e.g. Game Completion"
                    style={{
                      borderRadius: "12px",
                      height: "48px",
                      border: "2px solid #e5e7eb",
                      fontSize: "16px",
                      transition: "all 0.3s ease",
                    }}
                    onFocus={(e) => {
                      const target = e.target as HTMLInputElement;
                      target.style.borderColor = "#667eea";
                      target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                    }}
                    onBlur={(e) => {
                      const target = e.target as HTMLInputElement;
                      target.style.borderColor = "#e5e7eb";
                      target.style.boxShadow = "none";
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  label={
                    <Text style={{
                      color: "#374151",
                      fontWeight: 600,
                      fontSize: "14px"
                    }}>
                      Description
                    </Text>
                  }
                  name="description"
                  style={{ marginBottom: 32 }}
                  rules={[
                    {
                      max: 500,
                      message: "Description must not exceed 500 characters",
                    },
                  ]}
                >
                  <Input.TextArea
                    placeholder="Describe the reward criteria and requirements..."
                    rows={4}
                    showCount
                    maxLength={500}
                    style={{
                      borderRadius: "12px",
                      border: "2px solid #e5e7eb",
                      fontSize: "16px",
                      resize: "none",
                      transition: "all 0.3s ease",
                    }}
                    onFocus={(e) => {
                      const target = e.target as HTMLTextAreaElement;
                      target.style.borderColor = "#667eea";
                      target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                    }}
                    onBlur={(e) => {
                      const target = e.target as HTMLTextAreaElement;
                      target.style.borderColor = "#e5e7eb";
                      target.style.boxShadow = "none";
                    }}
                  />
                </Form.Item>
              </Col>

              {/* Form Actions */}
              <Col xs={24}>
                <div style={{
                  textAlign: "center",
                  padding: "24px",
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "16px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}>
                  <Row gutter={16} justify="center">
                    <Col xs={24} sm={8} md={6}>
                      <Button
                        style={{
                          width: "100%",
                          height: "44px",
                          borderRadius: "12px",
                          border: "2px solid #e5e7eb",
                          background: "white",
                          color: "#6b7280",
                          fontSize: "14px",
                          fontWeight: 600,
                          transition: "all 0.3s ease",
                        }}
                        onClick={() => form.resetFields()}
                        disabled={submitting}
                        onMouseEnter={(e) => {
                          const target = e.target as HTMLElement;
                          target.style.borderColor = "#ef4444";
                          target.style.color = "#ef4444";
                        }}
                        onMouseLeave={(e) => {
                          const target = e.target as HTMLElement;
                          target.style.borderColor = "#e5e7eb";
                          target.style.color = "#6b7280";
                        }}
                      >
                        Reset Form
                      </Button>
                    </Col>
                    <Col xs={24} sm={8} md={6}>
                      <Button
                        disabled={submitting}
                        style={{
                          width: "100%",
                          height: "44px",
                          borderRadius: "12px",
                          border: "2px solid #667eea",
                          background: "rgba(102, 126, 234, 0.05)",
                          color: "#667eea",
                          fontSize: "14px",
                          fontWeight: 600,
                          transition: "all 0.3s ease",
                        }}
                        onMouseEnter={(e) => {
                          const target = e.target as HTMLElement;
                          target.style.background = "#667eea";
                          target.style.color = "white";
                        }}
                        onMouseLeave={(e) => {
                          const target = e.target as HTMLElement;
                          target.style.background = "rgba(102, 126, 234, 0.05)";
                          target.style.color = "#667eea";
                        }}
                      >
                        Preview
                      </Button>
                    </Col>
                    <Col xs={24} sm={8} md={6}>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={submitting}
                        style={{
                          width: "100%",
                          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                          border: "none",
                          borderRadius: "12px",
                          height: "44px",
                          fontSize: "14px",
                          fontWeight: 600,
                          boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                          transition: "all 0.3s ease",
                        }}
                        onMouseEnter={(e) => {
                          const target = e.target as HTMLElement;
                          target.style.transform = "translateY(-2px)";
                          target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                        }}
                        onMouseLeave={(e) => {
                          const target = e.target as HTMLElement;
                          target.style.transform = "translateY(0)";
                          target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                        }}
                      >
                        {submitting ? "Creating..." : "Create Reward"}
                      </Button>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Form>
        </div>

        {/* Rewards Table Section */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 32,
            }}
          >
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "12px",
            }}>
              <div style={{
                width: "48px",
                height: "48px",
                borderRadius: "12px",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}>
                <TrophyOutlined style={{ color: "white", fontSize: "20px" }} />
              </div>
              <div>
                <Title level={3} style={{
                  fontWeight: 700,
                  margin: 0,
                  color: "#374151",
                  fontSize: "24px",
                }}>
                  Active Rewards
                </Title>
                {!rewardsLoading && !rewardsError && (
                  <Text style={{
                    color: "#6b7280",
                    fontSize: "14px",
                    fontWeight: 500,
                  }}>
                    {rewards.length} reward{rewards.length !== 1 ? "s" : ""} configured
                  </Text>
                )}
              </div>
            </div>
            <div style={{ display: "flex", gap: "12px" }}>
              {rewardsError && (
                <Button
                  type="link"
                  onClick={fetchRewards}
                  style={{
                    color: "#dc2626",
                    fontWeight: 600,
                  }}
                >
                  Retry Loading
                </Button>
              )}
              <Button
                onClick={fetchRewards}
                loading={rewardsLoading}
                style={{
                  borderColor: "#667eea",
                  color: "#667eea",
                  borderRadius: "12px",
                  height: "40px",
                  fontWeight: 600,
                  background: "rgba(102, 126, 234, 0.05)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#667eea";
                  target.style.color = "white";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "rgba(102, 126, 234, 0.05)";
                  target.style.color = "#667eea";
                }}
              >
                Refresh List
              </Button>
            </div>
          </div>

          {rewardsError && (
            <div
              style={{
                background: "rgba(239, 68, 68, 0.05)",
                border: "1px solid rgba(239, 68, 68, 0.2)",
                borderRadius: "12px",
                padding: "16px 20px",
                marginBottom: "24px",
                color: "#dc2626",
              }}
            >
              {rewardsError}.{" "}
              <Button
                type="link"
                onClick={fetchRewards}
                style={{
                  padding: 0,
                  color: "#dc2626",
                  fontWeight: 600,
                }}
              >
                Click to retry
              </Button>
            </div>
          )}

          <div style={{
            background: "rgba(255, 255, 255, 0.8)",
            borderRadius: "16px",
            padding: "24px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Table
              columns={rewardColumns}
              dataSource={rewards}
              loading={rewardsLoading}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} rewards`,
                responsive: true,
                style: {
                  padding: "16px 0",
                }
              }}
              scroll={{ x: 800 }}
              style={{
                background: "white",
                borderRadius: "12px",
              }}
              locale={{
                emptyText: rewardsLoading ? (
                  <div style={{
                    padding: "60px",
                    textAlign: "center",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "16px",
                  }}>
                    <Spin size="large" />
                    <div style={{ color: "#6b7280", fontSize: "16px" }}>
                      Loading rewards...
                    </div>
                  </div>
                ) : (
                  <div style={{
                    padding: "60px",
                    textAlign: "center",
                    background: "rgba(102, 126, 234, 0.05)",
                    borderRadius: "12px",
                    margin: "20px",
                  }}>
                    <div style={{
                      width: "64px",
                      height: "64px",
                      margin: "0 auto 20px",
                      borderRadius: "50%",
                      background: "rgba(102, 126, 234, 0.1)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}>
                      <GiftOutlined style={{ fontSize: "24px", color: "#667eea" }} />
                    </div>
                    <div
                      style={{
                        color: "#6b7280",
                        fontSize: "16px",
                        marginBottom: "8px",
                        fontWeight: 500,
                      }}
                    >
                      No rewards found
                    </div>
                    <div style={{ color: "#9ca3af", fontSize: "14px" }}>
                      Create your first reward using the form above!
                    </div>
                  </div>
                ),
              }}
            />
          </div>

          {/* Footer Message */}
          <div style={{
            textAlign: "center",
            marginTop: "32px",
            padding: "24px",
            background: "rgba(248, 250, 252, 0.8)",
            borderRadius: "16px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Text style={{
              fontSize: "14px",
              color: "#6b7280",
              fontWeight: 500,
            }}>
              💡 Manage your reward system to encourage tenant engagement and activity
            </Text>
          </div>
        </div>
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .rewards-container {
          animation: fadeInUp 0.6s ease-out;
        }

        .ant-table-thead > tr > th {
          background: rgba(102, 126, 234, 0.1) !important;
          color: #374151 !important;
          font-weight: 600 !important;
          border-bottom: 2px solid rgba(102, 126, 234, 0.2) !important;
        }

        .ant-table-tbody > tr > td {
          border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
        }

        .ant-table-tbody > tr:hover > td {
          background: rgba(102, 126, 234, 0.05) !important;
        }
      `}</style>
    </Layout>
  );
}
