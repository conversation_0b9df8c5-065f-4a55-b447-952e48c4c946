"use client";
import React, { useState, useEffect } from "react";
import {
  Row,
  Typography,
  Button,
  Spin,
  message,
  Layout,
  Input,
  Space,
} from "antd";
import GameCard from "@components/card/gameCards/page";
import EditGameModal from "@components/modal/EditGameModal";
import {
  PlusOutlined,
  // ReloadOutlined,
  SearchOutlined,
  // SettingOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import { Game, FetchGamesResponse } from "@types";
import ConfigureGameModal from "@components/modal/ConfigureGameModal";
const { Title, Text } = Typography;
const { Content } = Layout;
const { Search } = Input;

export default function HomePage() {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  const router = useRouter();
  const [games, setGames] = useState<Game[]>([]);
  const [filteredGames, setFilteredGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedGameId, setSelectedGameId] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState(""); // tracks input text
  const [searchQuery, setSearchQuery] = useState(""); // tracks actual applied query
  const [logicModalVisible, setLogicModalVisible] = useState(false);
  const [selectedLogicGame, setSelectedLogicGame] = useState<Game | null>(null);
  useEffect(() => {
    fetchGames();
  }, []);

  // When games are loaded or searchQuery changes, filter games
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredGames(games);
    } else {
      // const filtered = games.filter(
      //   (game) =>
      //     game.game_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      //     game.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      //     game.category_name.toLowerCase().includes(searchQuery.toLowerCase())
      // );
      const filtered = games.filter((game) => {
        const query = searchQuery.toLowerCase();
        return (
          game.game_name.toLowerCase().includes(query) ||
          game.description.toLowerCase().includes(query) ||
          (game.category_name?.toLowerCase().includes(query) ?? false)
        );
      });

      setFilteredGames(filtered);
    }
  }, [searchQuery, games]);

  const fetchGames = async () => {
    try {
      setLoading(true);
      const response: FetchGamesResponse =
        await dataProviderInstance.listGames();
      setGames(response.data || []);
      setFilteredGames(response.data || []);
    } catch (error) {
      console.error("Error fetching exercises:", error);
      message.error("Failed to load exercises");
      setGames([]);
      setFilteredGames([]);
    } finally {
      setLoading(false);
    }
  };

  const handleEditGame = (gameId: string) => {
    setSelectedGameId(gameId);
    setEditModalVisible(true);
  };

  const handleEditModalClose = () => {
    setEditModalVisible(false);
    setSelectedGameId(null);
  };

  // When search button clicked or enter pressed, apply the filter
  const handleSearch = (value: string) => {
    setSearchQuery(value.trim());
  };
  const handleGameLogic = (gameId: string) => {
    const game = games.find((g) => g.game_id === gameId) || null;
    console.log("game:", game);
    setSelectedLogicGame(game);
    setSelectedGameId(gameId);
    setLogicModalVisible(true);
  };

  const handleLogicModalClose = () => {
    setLogicModalVisible(false);
    setSelectedLogicGame(null);
  };
  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "150px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1400,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header Section */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "48px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            padding: "40px 32px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "42px",
              letterSpacing: "-1px",
            }}
          >
            Exercise Management
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
              lineHeight: "1.6",
            }}
          >
            Create, manage, and organize your exercises collection
          </Text>
          <Space size={16}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                height: "48px",
                paddingLeft: "24px",
                paddingRight: "24px",
                fontSize: "16px",
                fontWeight: 600,
                borderRadius: "12px",
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(-2px)";
                target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(0)";
                target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
              }}
              onClick={() => router.push("/manageGames/create")}
            >
              Add New Exercise
            </Button>
          </Space>
        </div>
        {/* Games Content */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "400px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "20px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          {loading ? (
            <>
              <Spin size="large" />
              <Text
                style={{
                  color: "#6b7280",
                  fontSize: "16px",
                  fontWeight: 500,
                  marginTop: "16px",
                }}
              >
                Loading Exercises...
              </Text>
            </>
          ) : games.length === 0 ? (
            <div
              style={{
                marginBottom: "20px",
                marginTop: "20px",
                textAlign: "center",
                padding: "80px 40px",
                background: "rgba(255, 255, 255, 0.8)",
                backdropFilter: "blur(10px)",
                borderRadius: "20px",
                boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                border: "1px solid rgba(255, 255, 255, 0.2)",
              }}
            >
              <div
                style={{
                  width: "80px",
                  height: "80px",
                  margin: "0 auto 24px",
                  borderRadius: "50%",
                  background:
                    "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <PlusOutlined style={{ fontSize: "32px", color: "#667eea" }} />
              </div>
              <Title
                level={3}
                style={{
                  color: "#374151",
                  marginBottom: "16px",
                }}
              >
                No Exercises Found
              </Title>
              <Text
                style={{
                  color: "#6b7280",
                  fontSize: "16px",
                  display: "block",
                  marginBottom: "24px",
                }}
              >
                Get started by creating your first exercise!
              </Text>
              <Button
                type="primary"
                size="large"
                style={{
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                  borderRadius: "12px",
                  height: "48px",
                  paddingLeft: "24px",
                  paddingRight: "24px",
                  fontSize: "16px",
                  fontWeight: 600,
                }}
                onClick={() => router.push("/manageGames/create")}
              >
                Create First Exercise
              </Button>
            </div>
          ) : (
            <div>
              <div
                style={{
                   margin: "32px",
                  display: "flex",
                  background: "rgba(255, 255, 255, 0.8)",
                  justifyContent: "space-between",
                  alignItems: "center",
                  flexWrap: "wrap",
                  gap: "12px",
                }}
              >
                {/* Left - Title */}
                <Title
                  level={3}
                  style={{
                    color: "#374151",
                    margin: 20,
                    fontSize: "24px",
                    fontWeight: 600,
                    flex: 1,
                    minWidth: "200px",
                  }}
                >
                  Your Exercises ({filteredGames.length})
                  {searchQuery && (
                    <Text
                      style={{
                        color: "#6b7280",
                        fontSize: "16px",
                        fontWeight: 400,
                        marginLeft: "8px",
                      }}
                    >
                      - filtered from {games.length} total
                    </Text>
                  )}
                </Title>

                {/* Right - Search and Refresh on same line */}
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "9px",
                    flexWrap: "nowrap", // ✅ Prevent wrapping
                  }}
                >
                  <Search
                    placeholder="Search exercises..."
                    allowClear
                    size="large"
                    style={{
                      width: "280px", // ✅ Fixed width
                    }}
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onSearch={handleSearch}
                  />
                  <Button
                    onClick={fetchGames}
                    style={{
                      borderColor: "#667eea",
                      color: "#667eea",
                      borderRadius: "12px",
                      height: "40px",
                      fontWeight: 600,
                      background: "rgba(102, 126, 234, 0.05)",
                      transition: "all 0.3s ease",
                      whiteSpace: "nowrap", // ✅ Prevent line break inside button
                    }}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.background = "#667eea";
                      target.style.color = "white";
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.background = "rgba(102, 126, 234, 0.05)";
                      target.style.color = "#667eea";
                    }}
                  >
                    Refresh
                  </Button>
                </div>
              </div>

              {filteredGames.length === 0 ? (
                <div
                  style={{
                    textAlign: "center",
                    padding: "80px 40px",
                    background: "rgba(255, 255, 255, 0.8)",
                    backdropFilter: "blur(10px)",
                    borderRadius: "20px",
                    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                  }}
                >
                  <div
                    style={{
                      width: "80px",
                      height: "80px",
                      margin: "0 auto 24px",
                      borderRadius: "50%",
                      background:
                        "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <SearchOutlined
                      style={{ fontSize: "32px", color: "#667eea" }}
                    />
                  </div>
                  <Title
                    level={3}
                    style={{
                      color: "#374151",
                      marginBottom: "16px",
                    }}
                  >
                    No Exercises Found
                  </Title>
                  <Text
                    style={{
                      color: "#6b7280",
                      fontSize: "16px",
                      display: "block",
                      marginBottom: "24px",
                    }}
                  >
                    No exercises match your search &quot;{searchQuery}&quot;.
                    Try different keywords.
                  </Text>
                  <Button
                    type="default"
                    size="large"
                    style={{
                      borderRadius: "12px",
                      height: "48px",
                      paddingLeft: "24px",
                      paddingRight: "24px",
                      fontSize: "16px",
                      fontWeight: 600,
                    }}
                    onClick={() => {
                      setSearchInput("");
                      setSearchQuery("");
                    }}
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                <Row
                  gutter={[24, 24]}
                  style={{
                    padding: "20px",
                    justifyContent:
                      filteredGames.length < 4 ? "flex-start" : "flex-start",
                  }}
                >
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(280px, 1fr))",
                      gap: "24px",
                      padding: "20px",
                      maxWidth: "1400px",
                      margin: "0 auto",
                    }}
                  >
                    {Array.isArray(filteredGames) &&
                      filteredGames.map((game, index) => (
                        <div
                          key={game.game_id}
                          style={{
                            animation: `fadeInUp 0.6s ease-out ${
                              index * 0.1
                            }s both`,
                            display: "flex",
                            justifyContent: "center",
                          }}
                        >
                          <GameCard
                            id={game.game_id}
                            image={game.thumbnail_url}
                            title={game.game_name}
                            description={game.description}
                            players={game.no_of_levels || 0}
                            category_name={game.category_name}
                            onEdit={handleEditGame}
                            onGameLogic={handleGameLogic}
                          />
                        </div>
                      ))}
                  </div>
                </Row>
              )}
            </div>
          )}
        </div>
        <EditGameModal
          visible={editModalVisible}
          onClose={handleEditModalClose}
          gameId={selectedGameId}
          gameDetails={
            games?.find((game) => game.game_id === selectedGameId) || null
          }
          onSuccess={() => {
            fetchGames();
          }}
        />
        <ConfigureGameModal
          visible={logicModalVisible}
          onClose={handleLogicModalClose}
          gameId={selectedGameId}
          gameData={
            selectedLogicGame
              ? {
                  // Map Game to GameConfigureFormData fields
                  title: selectedLogicGame.game_name,
                  category: selectedLogicGame.category_name ?? "",
                  category_id: selectedLogicGame.category_id ?? "",
                  level: selectedLogicGame.levels?.[0]?.level_number ?? 1,
                  levelData: selectedLogicGame.levels?.[0] ?? null,
                  timer: selectedLogicGame.estimated_duration_sec ?? 0,
                  instructions: selectedLogicGame.rules ?? "",
                }
              : undefined
          }
          onSuccess={fetchGames}
        />
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </Layout>
  );
}
