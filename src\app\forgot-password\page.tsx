"use client";
import React, { useState } from "react";
import { Form, Input, <PERSON><PERSON>, Al<PERSON>, <PERSON>, Typography, Space } from "antd";
import { MailOutlined } from "@ant-design/icons";
import { supabaseClient } from "@/utils/supabase/client";
import Link from "next/link";

const { Title, Text } = Typography;

export default function ForgotPasswordPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  const onFinish = async (values: { email: string }) => {
    setLoading(true);
    setError(null);
    setMessage(null);

    const { error } = await supabaseClient.auth.resetPasswordForEmail(
      values.email,
      {
        redirectTo: `${window.location.origin}/update-password`,
      },
    );

    setLoading(false);

    if (error) {
      setError(error.message);
    } else {
      setMessage("Password reset instructions have been sent to your email.");
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "#f5f5f5",
        padding: "24px",
      }}
    >
      <Card
        style={{
          width: "100%",
          maxWidth: "400px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        }}
      >
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <div style={{ textAlign: "center" }}>
            <img
              src="/assets/logo1.png"
              alt="Logo"
              style={{ width: 100, height: 100 }}
            />
            <Title
              level={2}
              style={{
                // color: "#1890ff",
                fontWeight: 600,
              }}
            >
              Reset your password
            </Title>
            <Text type="secondary" style={{ fontSize: "16px" }}>
              Enter your email to receive reset instructions
            </Text>
          </div>

          <Form
            name="forgot_password"
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { required: true, message: "Please input your email!" },
                { type: "email", message: "Please enter a valid email!" },
              ]}
            >
              <Input
                prefix={<MailOutlined style={{ color: "#bfbfbf" }} />}
                placeholder="<EMAIL>"
                style={{ borderRadius: "6px" }}
              />
            </Form.Item>

            {error && (
              <Form.Item>
                <Alert message={error} type="error" showIcon />
              </Form.Item>
            )}

            {message && (
              <Form.Item>
                <Alert message={message} type="success" showIcon />
              </Form.Item>
            )}

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                style={{
                  borderRadius: "6px",
                  fontSize: "16px",
                  fontWeight: 500,
                }}
              >
                Send reset instructions
              </Button>
            </Form.Item>
          </Form>

          <div style={{ textAlign: "center" }}>
            <Link href="/login">
                Back to login
            </Link>
          </div>
        </Space>
      </Card>
    </div>
  );
}