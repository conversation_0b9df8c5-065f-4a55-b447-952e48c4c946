// app/RefineApp.tsx
"use client";

import React, { Suspense } from "react";
import { Refine } from "@refinedev/core";
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
import { useNotificationProvider } from "@refinedev/antd";
import routerProvider from "@refinedev/nextjs-router";

import { ColorModeContextProvider } from "@contexts/color-mode";
import { UserProvider } from "@contexts/user-context";
import LayoutWrapper from "./LayoutWrapper";
import { AppIcon } from "@components/app-icon";
import { authProviderClient } from "@providers/auth-provider/auth-provider.client";
import { dataProviderInstance } from "@providers/data-provider";
import { ROLE } from "@utils/supabase/constants";

export default function RefineApp({
  children,
  defaultMode,
  role,
}: {
  children: React.ReactNode;
  defaultMode: "light" | "dark";
  role: string;
}) {



return (
    <RefineKbarProvider>
      <ColorModeContextProvider defaultMode={defaultMode}>
        <UserProvider>
          <Refine
            routerProvider={routerProvider}
            authProvider={authProviderClient}
            dataProvider={dataProviderInstance}
            notificationProvider={useNotificationProvider}
            // resources={resources}
            options={{
              syncWithLocation: true,
              warnWhenUnsavedChanges: true,
              useNewQueryKeys: true,
              projectId: "wfNw1D-Vg8iDN-YbOEp7",
              title: { text: "Refine Project", icon: <AppIcon /> },
            }}
          >

              <LayoutWrapper>{children}</LayoutWrapper>
              <RefineKbar />

          </Refine>
        </UserProvider>
      </ColorModeContextProvider>
    </RefineKbarProvider>
  );
}
