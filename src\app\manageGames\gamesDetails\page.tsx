"use client";
import { useSearchParams } from "next/navigation";
import {
  Typography,
  Card,
  Space,
  Spin,
  message,
  Row,
  Col,
  Button,
  Select,
  Input,
  DatePicker,
  Layout,
} from "antd";

import React, { useState, useEffect } from "react";
import { dataProviderInstance } from "@providers/data-provider";
import { Game, TenantOption } from "@types";
import dayjs from "dayjs";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Content } = Layout;

const GameDetails = () => {
  const searchParams = useSearchParams();
  const gameId = searchParams.get("id") || "";

  const [gameDetails, setGameDetails] = useState<Game | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [tenantOptions, setTenantOptions] = useState<TenantOption[]>([]);
  const [saving, setSaving] = useState(false);
  const [selectedParties, setSelectedParties] = useState<string[]>([]);
  const [dueDate, setDueDate] = useState<string>("");
  const [notes, setNotes] = useState<string>("");

  useEffect(() => {
    if (gameId) {
      fetchGameDetails();
      fetchUnassignedTenants();
    }
  }, [gameId]);

  const fetchGameDetails = async () => {
    try {
      setLoading(true);

      const response = await dataProviderInstance.fetchGamedetails({
        game_id: gameId,
      });

      if (response && response.data) {
        setGameDetails(response.data);
      } else {
        throw new Error("No exercise data received");
      }
    } catch {
      message.error(ERROR_MESSAGES.load_gamedetails);
      setGameDetails(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchUnassignedTenants = async () => {
    try {
      setLoadingTenants(true);

      const response = await dataProviderInstance.listAllUnassignedTenantByGame(
        {
          game_id: gameId,
        }
      );

      if (response && response.data) {
        const tenants = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];

        const formattedTenants: TenantOption[] = tenants.map(
          (tenant: {
            id: string | number;
            tenant_name: string;
            name?: string;
          }) => ({
            label:
              tenant.name || tenant.tenant_name || `organization ${tenant.id}`,
            value: tenant.id.toString(),
          })
        );

        setTenantOptions(formattedTenants);
      } else {
        setTenantOptions([]);
      }
    } catch {
      message.error(ERROR_MESSAGES.load_unassigned_tenants);
      setTenantOptions([]);
    } finally {
      setLoadingTenants(false);
    }
  };

  const handleSave = async () => {
    if (!selectedParties.length || !gameId) {
      message.error(ERROR_MESSAGES.select_parties);
      return;
    }

    try {
      setSaving(true);

      await dataProviderInstance.assignGameToTenant({
        party_ids: selectedParties,
        game_id: gameId,
        due_date: dueDate,
        notes: notes,
      });

      message.success(SUCCESS_MESSAGES.selected_parties);
      setSelectedParties([]);

      setDueDate("");
      setNotes("");
    } catch {
      message.error(ERROR_MESSAGES.assign_game_to_parties);
    } finally {
      setSaving(false);
    }
  };

  if (loading || loadingTenants) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background decorative elements */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "300px",
            background:
              "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
            borderRadius: "0 0 50% 50%",
            transform: "scale(1.5)",
            zIndex: 0,
          }}
        />

        <Content
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "relative",
            zIndex: 1,
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "24px",
              padding: "48px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              textAlign: "center",
            }}
          >
            <Spin size="large" />
            <Text
              style={{
                display: "block",
                marginTop: "16px",
                color: "#6b7280",
                fontSize: "16px",
              }}
            >
              Loading Exercise details...
            </Text>
          </div>
        </Content>
      </Layout>
    );
  }

  if (!gameDetails) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        <Content
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "relative",
            zIndex: 1,
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "24px",
              padding: "48px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              textAlign: "center",
            }}
          >
            <Title
              level={3}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                margin: 0,
              }}
            >
              Exercise Not Found
            </Title>
            <Text
              style={{
                display: "block",
                marginTop: "16px",
                color: "#6b7280",
                fontSize: "16px",
              }}
            >
              The requested exercise could not be found.
            </Text>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1280,
          margin: "0 auto",
          padding: "40px 24px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "40px",
          }}
        >
          <Title
            level={2}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              margin: "0 0 8px 0",
              fontSize: "36px",
              letterSpacing: "-0.5px",
            }}
          >
            Exercise Details
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
            }}
          >
            View and manage exercise information
          </Text>
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "center",
            width: "100%",
          }}
        >
          <Card
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "24px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              overflow: "hidden",
              width: "1200px",
              maxWidth: "100%",
            }}
            styles={{ body: { padding: 0 } }}
            cover={
              <div
                style={{
                  position: "relative",
                  height: "400px",
                  width: "100%",
                  overflow: "hidden",
                }}
              >
                <div
                  style={{
                    position: "relative",
                    height: "500px",
                    width: "100%",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={gameDetails.thumbnail_url || "/assets/add-game.png"}
                    alt={gameDetails.game_name}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                      objectPosition: "center",
                      display: "block",
                    }}
                    onError={(e) => {
                      e.currentTarget.src = "/assets/add-game.png";
                    }}
                  />
                </div>

                <div
                  style={{
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background:
                      "linear-gradient(transparent, rgba(0, 0, 0, 0.7))",
                    padding: "40px 32px 32px",
                  }}
                >
                  <Title
                    level={3}
                    style={{
                      color: "white",
                      margin: 0,
                      textShadow: "0 2px 4px rgba(0, 0, 0, 0.5)",
                      fontSize: "28px",
                      fontWeight: 600,
                    }}
                  >
                    {gameDetails.game_name}
                  </Title>
                </div>
              </div>
            }
          >
            {/* Game Information Section */}
            <div style={{ padding: "32px" }}>
              <div
                style={{
                  background: "rgba(248, 250, 252, 0.6)",
                  borderRadius: "16px",
                  padding: "24px",
                  marginBottom: "32px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}
              >
                <Title
                  level={4}
                  style={{
                    marginBottom: "20px",
                    color: "#374151",
                    fontWeight: 600,
                  }}
                >
                  Exercise Information
                </Title>

                <Row gutter={[24, 16]}>
                  <Col xs={24} sm={12}>
                    <div
                      style={{
                        background: "rgba(255, 255, 255, 0.8)",
                        borderRadius: "12px",
                        padding: "16px",
                        border: "1px solid rgba(226, 232, 240, 0.5)",
                      }}
                    >
                      <Text
                        style={{
                          display: "block",
                          fontSize: "14px",
                          color: "#6b7280",
                          fontWeight: 500,
                          marginBottom: "4px",
                        }}
                      >
                        Category
                      </Text>
                      <Text
                        style={{
                          fontSize: "16px",
                          color: "#374151",
                          fontWeight: 600,
                        }}
                      >
                        {gameDetails.category_name}
                      </Text>
                    </div>
                  </Col>
                  <Col xs={24} sm={12}>
                    <div
                      style={{
                        background: "rgba(255, 255, 255, 0.8)",
                        borderRadius: "12px",
                        padding: "16px",
                        border: "1px solid rgba(226, 232, 240, 0.5)",
                      }}
                    >
                      <Text
                        style={{
                          display: "block",
                          fontSize: "14px",
                          color: "#6b7280",
                          fontWeight: 500,
                          marginBottom: "4px",
                        }}
                      >
                        Number of Levels
                      </Text>
                      <Text
                        style={{
                          fontSize: "16px",
                          color: "#374151",
                          fontWeight: 600,
                        }}
                      >
                        {gameDetails.no_of_levels}
                      </Text>
                    </div>
                  </Col>
                </Row>

                <div
                  style={{
                    background: "rgba(255, 255, 255, 0.8)",
                    borderRadius: "12px",
                    padding: "20px",
                    marginTop: "16px",
                    border: "1px solid rgba(226, 232, 240, 0.5)",
                  }}
                >
                  <Text
                    style={{
                      display: "block",
                      fontSize: "14px",
                      color: "#6b7280",
                      fontWeight: 500,
                      marginBottom: "8px",
                    }}
                  >
                    Description
                  </Text>
                  <Text
                    style={{
                      fontSize: "16px",
                      color: "#374151",
                      lineHeight: 1.6,
                    }}
                  >
                    {gameDetails.description}
                  </Text>
                </div>
              </div>

              {/* Organization Assignment Section */}
              <div
                style={{
                  background: "rgba(248, 250, 252, 0.6)",
                  borderRadius: "16px",
                  padding: "24px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}
              >
                <Title
                  level={4}
                  style={{
                    marginBottom: "24px",
                    color: "#374151",
                    fontWeight: 600,
                  }}
                >
                  Assign to organization
                </Title>

                <Space direction="vertical" size={20} style={{ width: "100%" }}>
                  <div>
                    <Text
                      style={{
                        display: "block",
                        marginBottom: 12,
                        fontWeight: 500,
                        color: "#374151",
                        fontSize: "15px",
                      }}
                    >
                      Select Organization
                      <span style={{ color: "#ef4444" }}>*</span>
                    </Text>             
                    <Select
                      mode="multiple"
                      placeholder="Select Organizations to assign this exercise"
                      options={tenantOptions}
                      onChange={(values) => setSelectedParties(values)}
                      loading={loadingTenants}
                      style={{
                        width: "100%",
                      }}
                      notFoundContent={
                        loadingTenants ? (
                          <Spin size="small" />
                        ) : (
                          "No organizations found"
                        )
                      }
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                    />
                  </div>

                  <Row gutter={16}>
                    <Col xs={24} sm={8}>
                      <div>
                        <Text
                          style={{
                            display: "block",
                            marginBottom: 12,
                            fontWeight: 500,
                            color: "#374151",
                            fontSize: "15px",
                          }}
                        >
                          Due Date <span style={{ color: "#ef4444" }}>*</span>
                        </Text>

                        <DatePicker
                          size="large"
                          style={{
                            width: "100%",
                            borderRadius: "12px",
                            border: "1px solid rgba(209, 213, 219, 0.8)",
                          }}
                          disabledDate={(current) =>
                            current && current < dayjs().startOf("day")
                          }
                          value={dueDate ? dayjs(dueDate) : null}
                          onChange={(_, dateString) => {
                            if (typeof dateString === "string")
                              setDueDate(dateString);
                            else setDueDate("");
                          }}
                          placeholder="Select due date"
                        />
                      </div>
                    </Col>
                    <Col xs={24} sm={16}>
                      <div>
                        <Text
                          style={{
                            display: "block",
                            marginBottom: 12,
                            fontWeight: 500,
                            color: "#374151",
                            fontSize: "15px",
                          }}
                        >
                          Notes <span style={{ color: "#ef4444" }}>*</span>
                        </Text>
                        <TextArea
                          placeholder="Enter assignment notes and instructions"
                          value={notes}
                          onChange={(e) => setNotes(e.target.value)}
                          rows={4}
                          style={{
                            width: "100%",
                            borderRadius: "12px",
                            border: "1px solid rgba(209, 213, 219, 0.8)",
                            background: "rgba(255, 255, 255, 0.9)",
                          }}
                        />
                      </div>
                    </Col>
                  </Row>

                  <div style={{ textAlign: "right", marginTop: 24 }}>
                    <Button
                      type="primary"
                      size="large"
                      onClick={handleSave}
                      loading={saving}
                      disabled={
                        !selectedParties.length || !dueDate || !notes.trim()
                      }
                      style={{
                        background:
                          "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        borderColor: "transparent",
                        borderRadius: "12px",
                        height: "48px",
                        fontSize: "16px",
                        fontWeight: 600,
                        paddingLeft: "32px",
                        paddingRight: "32px",
                        boxShadow: "0 4px 12px rgba(102, 126, 234, 0.4)",
                        transition: "all 0.3s ease",
                        color: "white",
                      }}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(-2px)";
                        target.style.boxShadow =
                          "0 6px 20px rgba(102, 126, 234, 0.6)";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(0)";
                        target.style.boxShadow =
                          "0 4px 12px rgba(102, 126, 234, 0.4)";
                      }}
                    >
                      {saving ? "Assigning Exercise..." : "Assign Exercise"}
                    </Button>
                  </div>
                </Space>
              </div>
            </div>
          </Card>
        </div>
      </Content>
    </Layout>
  );
};

export default GameDetails;
