"use client";
import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  DatePicker,
  Spin,
  message,
  Upload,
  Card,
} from "antd";

import { CloseOutlined, EditOutlined } from "@ant-design/icons";
import { supabaseClient } from "@utils/supabase/client";
import {
  CategoryItem,
  CategoryOption,
  GameFormData,
  ListTenantResponse,
  ListTenantsResponse,
  TenantOption,
} from "@types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
import { dataProviderInstance } from "@providers/data-provider";
import { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
const { Text } = Typography;

export default function AddGamePage() {
  const [form] = Form.useForm();
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [tenantData, setTenantData] = useState<ListTenantResponse>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [tenantOptions, setTenantOptions] = useState<TenantOption[]>([]);
  const router = useRouter();

  useEffect(() => {
    setLoadingTenants(true);
    dataProviderInstance
      .listTenants()
      .then((response: ListTenantsResponse) => {
        const tenants = response.data.data;
        setTenantData(tenants);

        const formattedTenants: TenantOption[] = tenants.map(
          (tenant: {
            id: string | number;
            tenant_name: string;
            name?: string;
          }) => ({
            label:
              tenant.name || tenant.tenant_name || `organization ${tenant.id}`,
            value: tenant.id.toString(),
          })
        );

        setTenantOptions(formattedTenants);
      })
      .catch((error: Error) => {
        console.error("Error fetching organizations:", error);

        message.error(ERROR_MESSAGES.failed_loadtenants);
        setTenantData([]);
        setTenantOptions([]);
      })
      .finally(() => {
        setLoadingTenants(false);
      });
  }, []);

  useEffect(() => {
    setLoadingCategories(true);
    dataProviderInstance
      .listGameCategories()
      .then((response: { data: CategoryItem[] }) => {
        if (!response || !response.data) {
          throw new Error("No category data received");
        }

        const formatted: CategoryOption[] = response.data.map(
          (item: CategoryItem) => ({
            label: item.name,
            value: item.id,
          })
        );

        setCategoryOptions(formatted);
      })
      .catch((error: Error) => {
        console.error("Error fetching categories:", error);
        message.error(ERROR_MESSAGES.load_categories);
        setCategoryOptions([]);
      })
      .finally(() => {
        setLoadingCategories(false);
      });
  }, []);

  const handleSubmit = async (values: GameFormData) => {
    try {
      setSubmitting(true);
      let thumbnailUrl = uploadedImageUrl;

      if (imageFile) {
        const fileName = `games/${Date.now()}_${imageFile.name}`;
        const { error: uploadError } = await supabaseClient.storage
          .from("image-bucket")
          .upload(fileName, imageFile, {
            cacheControl: "3600",
            upsert: true,
          });

        if (uploadError) throw uploadError;

        const { data: signedUrlData, error: signedUrlError } =
          await supabaseClient.storage
            .from("image-bucket")
            .createSignedUrl(fileName, 60 * 60 * 157680000); // 1 hour expiry

        if (signedUrlError) throw signedUrlError;

        thumbnailUrl = signedUrlData?.signedUrl;
      }
      if (!thumbnailUrl) {
        thumbnailUrl =
          "https://www.shutterstock.com/shutterstock/photos/1432985741/display_1500/stock-vector-counting-games-for-kids-and-adults-educational-math-game-result-crossword-for-social-networks-1432985741.jpg";
      }

      const gameData = {
        category_id: values.category,
        name: values.title,
        description: values.description,
        thumbnail_url: thumbnailUrl,
        no_levels: parseInt(values.no_of_levels?.toString() || "1"),
        valid_from: values.valid_from.format("YYYY-MM-DDTHH:mm:ssZ"),
        valid_to: values.valid_upto.format("YYYY-MM-DDTHH:mm:ssZ"),
        duration: values.duration,
        difficulty: values.difficulty,

        rules: values.rules,
      };

      const { error } = await supabaseClient.rpc("fn_insert_game", gameData);

      if (error) throw error;

      message.success(SUCCESS_MESSAGES.create_game);
      router.push("/manageGames");
      form.resetFields();
      form.setFieldsValue({ valid_from: dayjs() });
      setUploadedImageUrl("");
      setImageFile(null);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create game";
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  return (
    <div style={{ padding: "0 12px" }}>
      <div
        style={{
          maxWidth: 1280,
          margin: "16px auto 0",
          background: "#fff",
          borderRadius: 6,
          boxShadow: "0 1px 3px rgba(0,0,0,0.06)",
          padding: 16,
          position: "relative",
        }}
      >
        <Row gutter={16}>
          <Col xs={24} md={16}>
            <Form
              form={form}
              layout="vertical"
              size="middle"
              style={{ paddingTop: 8 }}
              onFinish={handleSubmit}
              initialValues={{
                valid_from: dayjs(),
              }}
            >
              <div style={{ marginBottom: 8 }}>
                <Typography.Title level={5} style={{ margin: 0, fontSize: 16 }}>
                  Exercise Information
                </Typography.Title>
              </div>

              <Card size="small" bordered style={{ marginBottom: 12 }}>
                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      label="Category"
                      name="category"
                      style={{ marginBottom: 12 }}
                      rules={[
                        { required: true, message: "Please select category" },
                      ]}
                    >
                      <Select
                        size="middle"
                        placeholder="Select category"
                        options={categoryOptions}
                        loading={loadingCategories}
                        notFoundContent={
                          loadingCategories ? (
                            <Spin size="small" />
                          ) : (
                            "No categories found"
                          )
                        }
                      />
                    </Form.Item>

                    <Form.Item
                      label="Valid From"
                      name="valid_from"
                      style={{ marginBottom: 12 }}
                      rules={[
                        {
                          required: true,
                          message: "Please select valid from date",
                        },
                      ]}
                    >
                      <DatePicker
                        size="middle"
                        placeholder="Select valid from date"
                        style={{ width: "100%" }}
                        format="DD/MM/YYYY"
                        onChange={() => {
                          form.validateFields(["valid_upto"]);
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      label="Description"
                      name="description"
                      style={{ marginBottom: 12 }}
                      rules={[
                        {
                          required: true,
                          message: "Please enter game description",
                        },
                      ]}
                    >
                      <Input.TextArea
                        size="small"
                        placeholder="Enter game description"
                        rows={2}
                      />
                    </Form.Item>

                    <Form.Item
                      label="Exercise Rules & Instructions"
                      name="instructions"
                      style={{ marginBottom: 0 }}
                      rules={[
                        {
                          required: true,
                          message:
                            "Please enter exercise rules and instructions",
                        },
                      ]}
                    >
                      <Input.TextArea
                        size="small"
                        placeholder="Enter detailed exercise rules and instructions"
                        rows={3}
                      />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item
                      label="Exercise Title"
                      name="title"
                      style={{ marginBottom: 12 }}
                      rules={[
                        {
                          required: true,
                          message: "Please enter Exercise title",
                        },
                      ]}
                    >
                      <Input size="middle" placeholder="Enter Exercise title" />
                    </Form.Item>

                    <Form.Item
                      label="Valid Upto"
                      name="valid_upto"
                      style={{ marginBottom: 12 }}
                      rules={[
                        {
                          required: true,
                          message: "Please select valid upto date",
                        },
                        ({ getFieldValue }) => ({
                          validator(_, value: Dayjs) {
                            const validFrom: Dayjs =
                              getFieldValue("valid_from");
                            if (
                              !value ||
                              !validFrom ||
                              value.isAfter(validFrom)
                            ) {
                              return Promise.resolve();
                            }
                            return Promise.reject(
                              new Error(
                                "Valid upto date must be after valid from date"
                              )
                            );
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        size="middle"
                        placeholder="Select valid upto date"
                        style={{ width: "100%" }}
                        format="DD/MM/YYYY"
                        disabledDate={(current) => {
                          const validFrom: Dayjs =
                            form.getFieldValue("valid_from");
                          return (
                            validFrom &&
                            current &&
                            current.isBefore(validFrom, "day")
                          );
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      label="No of Levels"
                      name="no_of_levels"
                      style={{ marginBottom: 12 }}
                      rules={[
                        {
                          required: true,
                          message: "Please enter number of levels",
                        },
                      ]}
                    >
                      <Input
                        size="middle"
                        placeholder="Enter number of levels"
                        inputMode="numeric"
                        onChange={(e) => {
                          const onlyDigits = e.target.value.replace(/\D/g, "");
                          e.target.value = onlyDigits;
                        }}
                        onKeyPress={(e) => {
                          if (
                            !/[0-9]/.test(e.key) &&
                            e.key !== "Backspace" &&
                            e.key !== "Delete"
                          ) {
                            e.preventDefault();
                          }
                        }}
                        maxLength={4}
                      />
                    </Form.Item>

                    <Form.Item
                      label="Duration (minutes)"
                      name="duration"
                      style={{ marginBottom: 12 }}
                      rules={[
                        {
                          required: true,
                          message: "Please enter duration",
                        },
                      ]}
                    >
                      <Input
                        size="middle"
                        placeholder="Enter duration in minutes"
                        inputMode="numeric"
                        onChange={(e) => {
                          const onlyDigits = e.target.value.replace(/\D/g, "");
                          e.target.value = onlyDigits;
                        }}
                        onKeyPress={(e) => {
                          if (
                            !/[0-9]/.test(e.key) &&
                            e.key !== "Backspace" &&
                            e.key !== "Delete"
                          ) {
                            e.preventDefault();
                          }
                        }}
                        maxLength={4}
                      />
                    </Form.Item>

                    <Form.Item
                      label="Difficulty Level"
                      name="difficulty"
                      style={{ marginBottom: 0 }}
                      rules={[
                        {
                          required: true,
                          message: "Please select difficulty level",
                        },
                      ]}
                    >
                      <Select
                        size="middle"
                        placeholder="Select difficulty level"
                        options={[
                          { label: "Easy", value: "easy" },
                          { label: "Medium", value: "medium" },
                          { label: "Hard", value: "hard" },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              <div style={{ marginBottom: 8 }}>
                <Typography.Title level={5} style={{ margin: 0, fontSize: 16 }}>
                  Assign to organizations
                </Typography.Title>
              </div>

              <Card size="small" bordered style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      label="Assign To organizations"
                      name="tenants"
                      style={{ marginBottom: 12 }}
                    >
                      <Select
                        size="middle"
                        mode="multiple"
                        placeholder="Assign organizations to the game"
                        options={tenantOptions}
                        loading={loadingTenants}
                        style={{ width: "100%" }}
                        notFoundContent={
                          loadingTenants ? (
                            <Spin size="small" />
                          ) : (
                            "No organizations found"
                          )
                        }
                      />
                    </Form.Item>

                    <Form.Item shouldUpdate style={{ marginBottom: 0 }}>
                      {() => {
                        const selectedIds: string[] =
                          form.getFieldValue("tenants") || [];

                        const removeTenant = (idToRemove: string) => {
                          const updated = selectedIds.filter(
                            (id: string) => id !== idToRemove
                          );
                          form.setFieldsValue({ tenants: updated });
                        };

                        return selectedIds.length > 0 ? (
                          <div>
                            <Text style={{ fontSize: 13 }}>
                              Assigning organizations:
                            </Text>
                            <div
                              style={{
                                marginTop: 6,
                                maxHeight: 100,
                                overflowY: "auto",
                                border: "1px solid #e0e0e0",
                                borderRadius: 4,
                                padding: 8,
                                background: "#ffffff",
                                boxShadow: "0 1px 2px rgba(0, 0, 0, 0.04)",
                              }}
                            >
                              <ul
                                style={{
                                  margin: 0,
                                  paddingLeft: 0,
                                  listStyle: "none",
                                }}
                              >
                                {selectedIds.map((id: string) => {
                                  const tenant = tenantData.find(
                                    (t) => t.id === id
                                  );
                                  const name =
                                    tenant?.tenant_name || `organization ${id}`;

                                  return (
                                    <li
                                      key={id}
                                      style={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        padding: "3px 6px",
                                        borderBottom: "1px solid #f0f0f0",
                                        fontSize: 13,
                                      }}
                                    >
                                      <span>{name}</span>
                                      <CloseOutlined
                                        onClick={() => removeTenant(id)}
                                        style={{
                                          color: "#ff4d4f",
                                          cursor: "pointer",
                                          fontSize: 12,
                                        }}
                                      />
                                    </li>
                                  );
                                })}
                              </ul>
                            </div>
                          </div>
                        ) : null;
                      }}
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: 12,
                }}
              >
                <Button
                  type="primary"
                  htmlType="submit"
                  size="middle"
                  loading={submitting}
                  style={{ background: "#6366f1", border: "none" }}
                >
                  {submitting ? "Submitting..." : "Submit Game"}
                </Button>
              </div>
            </Form>
          </Col>

          <Col xs={24} md={8}>
            <Card size="small" bordered style={{ marginBottom: 12 }}>
              <div style={{ marginBottom: 8 }}>
                <Typography.Title level={5} style={{ margin: 0, fontSize: 16 }}>
                  Exercise Thumbnail
                </Typography.Title>
              </div>

              <div
                style={{
                  position: "relative",
                  height: 200,
                  borderRadius: 6,
                  background: "#f0f0f0",
                  overflow: "hidden",
                  marginBottom: 12,
                }}
              >
                {uploadedImageUrl ? (
                  <img
                    src={uploadedImageUrl}
                    alt="Game Thumbnail"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                  />
                ) : (
                  <div
                    style={{
                      background:
                        "url('/assets/add-game.png') center/cover no-repeat",
                      width: "100%",
                      height: "100%",
                    }}
                  />
                )}

                <Upload {...uploadProps}>
                  <Button
                    shape="circle"
                    icon={<EditOutlined />}
                    size="small"
                    style={{
                      position: "absolute",
                      top: 12,
                      right: 12,
                      background: "#ffffffcc",
                      boxShadow: "0 2px 6px rgba(0,0,0,0.2)",
                      zIndex: 10,
                    }}
                  />
                </Upload>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
}
