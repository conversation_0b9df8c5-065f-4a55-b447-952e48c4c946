"use client";
import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  DatePicker,
  Spin,
  message,
  Upload,
  Layout,
} from "antd";
import Image from "next/image";
import { CloseOutlined, EditOutlined, ArrowLeftOutlined, PlayCircleOutlined, TeamOutlined, UploadOutlined } from "@ant-design/icons";
import { supabaseClient } from "@utils/supabase/client";
import {
  CategoryItem,
  CategoryOption,
  GameFormData,
  ListTenantResponse,
  ListTenantsResponse,
  TenantOption,
} from "@types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
import { dataProviderInstance } from "@providers/data-provider";
import { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
const { Text, Title } = Typography;
const { Content } = Layout;

export default function AddGamePage() {
  const [form] = Form.useForm();
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [tenantData, setTenantData] = useState<ListTenantResponse>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [tenantOptions, setTenantOptions] = useState<TenantOption[]>([]);
  const router = useRouter();
  useEffect(() => {
    setLoadingTenants(true);
    dataProviderInstance
      .listTenants()
      .then((response: ListTenantsResponse) => {
        const tenants = response.data.data;
        setTenantData(tenants);

        const formattedTenants: TenantOption[] = tenants.map(
          (tenant: {
            id: string | number;
            tenant_name: string;
            name?: string;
          }) => ({
            label: tenant.name || tenant.tenant_name || `Tenant ${tenant.id}`,
            value: tenant.id.toString(),
          })
        );

        setTenantOptions(formattedTenants);
      })
      .catch((error: Error) => {
        console.error("Error fetching tenants:", error);

        message.error(ERROR_MESSAGES.failed_loadtenants);
        setTenantData([]);
        setTenantOptions([]);
      })
      .finally(() => {
        setLoadingTenants(false);
      });
  }, []);

  useEffect(() => {
    setLoadingCategories(true);
    dataProviderInstance
      .listGameCategories()
      .then((response: { data: CategoryItem[] }) => {
        if (!response || !response.data) {
          throw new Error("No category data received");
        }

        const formatted: CategoryOption[] = response.data.map(
          (item: CategoryItem) => ({
            label: item.name,
            value: item.id,
          })
        );

        setCategoryOptions(formatted);
      })
      .catch((error: Error) => {
        console.error("Error fetching categories:", error);
        message.error(ERROR_MESSAGES.load_categories);
        setCategoryOptions([]);
      })
      .finally(() => {
        setLoadingCategories(false);
      });
  }, []);

  const handleSubmit = async (values: GameFormData) => {
    try {
      setSubmitting(true);
      let thumbnailUrl = uploadedImageUrl;

      if (imageFile) {
        const fileName = `games/${Date.now()}_${imageFile.name}`;
        const { error: uploadError } = await supabaseClient.storage
          .from("image-bucket")
          .upload(fileName, imageFile, {
            cacheControl: "3600",
            upsert: true,
          });

        if (uploadError) throw uploadError;

        const { data: signedUrlData, error: signedUrlError } =
          await supabaseClient.storage
            .from("image-bucket")
            .createSignedUrl(fileName, 60 * 60 * 157680000); // 1 hour expiry

        if (signedUrlError) throw signedUrlError;

        thumbnailUrl = signedUrlData?.signedUrl;
      }
      if (!thumbnailUrl) {
        thumbnailUrl =
          "https://www.shutterstock.com/shutterstock/photos/1432985741/display_1500/stock-vector-counting-games-for-kids-and-adults-educational-math-game-result-crossword-for-social-networks-1432985741.jpg";
      }

      const gameData = {
        category_id: values.category,
        name: values.title,
        description: values.description,
        thumbnail_url: thumbnailUrl,
        no_levels: parseInt(values.no_of_levels?.toString() || "1"),
        valid_from: values.valid_from.format("YYYY-MM-DDTHH:mm:ssZ"),
        valid_to: values.valid_upto.format("YYYY-MM-DDTHH:mm:ssZ"),
      };

      const { error } = await supabaseClient.rpc("fn_insert_game", gameData);

      if (error) throw error;

      message.success(SUCCESS_MESSAGES.create_game);
      router.push("/manageGames");
      form.resetFields();
      form.setFieldsValue({ valid_from: dayjs() });
      setUploadedImageUrl("");
      setImageFile(null);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create game";
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "150px",
          height: "150px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1600,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header Section */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",
          borderRadius: "24px",
          padding: "40px 32px",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "16px",
            marginBottom: "16px",
          }}>
            <Button
              icon={<ArrowLeftOutlined />}
              style={{
                background: "rgba(102, 126, 234, 0.1)",
                border: "1px solid rgba(102, 126, 234, 0.2)",
                color: "#667eea",
                borderRadius: "12px",
                height: "44px",
                width: "44px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              onClick={() => router.push("/manageGames")}
            />
            <Title
              level={1}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                margin: 0,
                fontSize: "36px",
                letterSpacing: "-1px",
              }}
            >
              Create New Game
            </Title>
          </div>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              lineHeight: "1.6",
            }}
          >
            Design and configure a new game for your platform
          </Text>
        </div>

        {/* Main Form Container */}
        <div
          style={{

            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
            position: "relative",
          }}
        >
          {/* Game Thumbnail Upload Section */}
          <div style={{
            marginBottom: "32px",
            background: "rgba(248, 250, 252, 0.8)",
            borderRadius: "20px",
            padding: "24px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "12px",
              marginBottom: "20px",
            }}>
              <div style={{
                width: "40px",
                height: "40px",
                borderRadius: "12px",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}>
                <UploadOutlined style={{ color: "white", fontSize: "16px" }} />
              </div>
              <Title level={4} style={{
                margin: 0,
                color: "#374151",
                fontWeight: 600,
                fontSize: "18px",
              }}>
                Game Thumbnail
              </Title>
            </div>

            <div
              style={{
                position: "relative",
                height: 240,
                borderRadius: "16px",
                background: "rgba(255, 255, 255, 0.8)",
                overflow: "hidden",
                border: "2px dashed #e5e7eb",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {uploadedImageUrl ? (
                <Image
                  src={uploadedImageUrl}
                  alt="Game Thumbnail"
                  fill
                  style={{
                    objectFit: "cover",
                  }}
                  priority
                />
              ) : (
                <div
                  style={{
                    background: "url('/assets/add-game.png') center/contain no-repeat",
                    width: "100%",
                    height: "100%",
                    opacity: 0.6,
                  }}
                />
              )}

              <Upload {...uploadProps}>
                <Button
                  icon={<EditOutlined />}
                  style={{
                    position: "absolute",
                    top: "16px",
                    right: "16px",
                    background: "rgba(255, 255, 255, 0.9)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(102, 126, 234, 0.2)",
                    color: "#667eea",
                    borderRadius: "12px",
                    height: "40px",
                    fontWeight: 600,
                    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                    zIndex: 10,
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.background = "#667eea";
                    target.style.color = "white";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.background = "rgba(255, 255, 255, 0.9)";
                    target.style.color = "#667eea";
                  }}
                >
                  {uploadedImageUrl ? "Change Image" : "Upload Image"}
                </Button>
              </Upload>
            </div>

            <Text style={{
              color: "#6b7280",
              fontSize: "12px",
              fontStyle: "italic",
              marginTop: "8px",
              display: "block",
            }}>
              Recommended size: 1200x600px. Max file size: 5MB
            </Text>
          </div>

          <Form
            form={form}
            layout="vertical"
            size="large"
            onFinish={handleSubmit}
            initialValues={{
              valid_from: dayjs(),
            }}
          >
            {/* Game Information Section */}
            <div
              style={{
                marginBottom: 32,
                background: "rgba(248, 250, 252, 0.8)",
                borderRadius: "20px",
                padding: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <div style={{
                display: "flex",
                alignItems: "center",
                gap: "12px",
                marginBottom: "32px",
              }}>
                <div style={{
                  width: "48px",
                  height: "48px",
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}>
                  <PlayCircleOutlined style={{ color: "white", fontSize: "20px" }} />
                </div>
                <Title level={3} style={{
                  margin: 0,
                  color: "#374151",
                  fontWeight: 700,
                  fontSize: "24px",
                }}>
                  Game Information
                </Title>
              </div>
              <Row gutter={32}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Game Category
                      </Text>
                    }
                    name="category"
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Please select category" },
                    ]}
                  >
                    <Select
                      placeholder="Select game category"
                      options={categoryOptions}
                      loading={loadingCategories}
                      style={{
                        height: "48px",
                      }}
                      notFoundContent={
                        loadingCategories ? (
                          <Spin size="small" />
                        ) : (
                          "No categories found"
                        )
                      }
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Valid From Date
                      </Text>
                    }
                    name="valid_from"
                    style={{ marginBottom: 24 }}
                    rules={[
                      {
                        required: true,
                        message: "Please select valid from date",
                      },
                    ]}
                  >
                    <DatePicker
                      placeholder="Select valid from date"
                      style={{
                        width: "100%",
                        height: "48px",
                        borderRadius: "12px",
                        border: "2px solid #e5e7eb",
                      }}
                      format="DD/MM/YYYY"
                      onChange={() => {
                        form.validateFields(["valid_upto"]);
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Game Description
                      </Text>
                    }
                    name="description"
                    style={{ marginBottom: 24 }}
                    rules={[
                      {
                        required: true,
                        message: "Please enter game description",
                      },
                    ]}
                  >
                    <Input.TextArea
                      placeholder="Enter detailed game description"
                      rows={4}
                      style={{
                        borderRadius: "12px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        resize: "none",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Game Title
                      </Text>
                    }
                    name="title"
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Please enter game title" },
                    ]}
                  >
                    <Input
                      placeholder="Enter game title"
                      style={{
                        borderRadius: "12px",
                        height: "48px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Valid Until Date
                      </Text>
                    }
                    name="valid_upto"
                    style={{ marginBottom: 24 }}
                    rules={[
                      {
                        required: true,
                        message: "Please select valid upto date",
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value: Dayjs) {
                          const validFrom: Dayjs = getFieldValue("valid_from");
                          if (!value || !validFrom || value.isAfter(validFrom)) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            new Error(
                              "Valid upto date must be after valid from date"
                            )
                          );
                        },
                      }),
                    ]}
                  >
                    <DatePicker
                      placeholder="Select valid upto date"
                      style={{
                        width: "100%",
                        height: "48px",
                        borderRadius: "12px",
                        border: "2px solid #e5e7eb",
                      }}
                      format="DD/MM/YYYY"
                      disabledDate={(current) => {
                        const validFrom: Dayjs = form.getFieldValue("valid_from");
                        return (
                          validFrom &&
                          current &&
                          current.isBefore(validFrom, "day")
                        );
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Number of Levels
                      </Text>
                    }
                    name="no_of_levels"
                    style={{ marginBottom: 24 }}
                    rules={[
                      {
                        required: true,
                        message: "Please enter number of levels",
                      },
                    ]}
                  >
                    <Input
                      placeholder="Enter number of levels"
                      inputMode="numeric"
                      style={{
                        borderRadius: "12px",
                        height: "48px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        transition: "all 0.3s ease",
                      }}
                      onChange={(e) => {
                        const onlyDigits = e.target.value.replace(/\D/g, "");
                        e.target.value = onlyDigits;
                      }}
                      onKeyDown={(e) => {
                        if (
                          !/[0-9]/.test(e.key) &&
                          e.key !== "Backspace" &&
                          e.key !== "Delete" &&
                          e.key !== "Tab" &&
                          e.key !== "ArrowLeft" &&
                          e.key !== "ArrowRight"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                      maxLength={4}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* Tenant Assignment Section */}
            <div
              style={{
                marginBottom: 32,
                background: "rgba(248, 250, 252, 0.8)",
                borderRadius: "20px",
                padding: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <div style={{
                display: "flex",
                alignItems: "center",
                gap: "12px",
                marginBottom: "32px",
              }}>
                <div style={{
                  width: "48px",
                  height: "48px",
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}>
                  <TeamOutlined style={{ color: "white", fontSize: "20px" }} />
                </div>
                <Title level={3} style={{
                  margin: 0,
                  color: "#374151",
                  fontWeight: 700,
                  fontSize: "24px",
                }}>
                  Assign to Tenants
                </Title>
              </div>
              <Row gutter={32}>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <Text style={{
                        color: "#374151",
                        fontWeight: 600,
                        fontSize: "14px"
                      }}>
                        Assign to Tenants
                      </Text>
                    }
                    name="tenants"
                    style={{ marginBottom: 24 }}
                    rules={[
                      {
                        required: true,
                        message: "Please select at least one tenant",
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select tenants to assign this game"
                      options={tenantOptions}
                      loading={loadingTenants}
                      style={{
                        minHeight: "48px",
                      }}
                      notFoundContent={
                        loadingTenants ? (
                          <Spin size="small" />
                        ) : (
                          "No tenants found"
                        )
                      }
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Selected Tenants Display */}
              <Form.Item shouldUpdate style={{ marginBottom: 0 }}>
                {() => {
                  const selectedIds: string[] =
                    form.getFieldValue("tenants") || [];

                  const removeTenant = (idToRemove: string) => {
                    const updated = selectedIds.filter(
                      (id: string) => id !== idToRemove
                    );
                    form.setFieldsValue({ tenants: updated });
                  };

                  return selectedIds.length > 0 ? (
                    <div style={{
                      marginTop: "16px",
                      padding: "20px",
                      background: "rgba(102, 126, 234, 0.05)",
                      borderRadius: "12px",
                      border: "1px solid rgba(102, 126, 234, 0.1)",
                    }}>
                      <Text style={{
                        fontSize: "14px",
                        fontWeight: 600,
                        color: "#374151",
                        display: "block",
                        marginBottom: "12px",
                      }}>
                        Selected Tenants ({selectedIds.length}):
                      </Text>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: "8px",
                        }}
                      >
                        {selectedIds.map((id: string) => {
                          const tenant = tenantData.find(
                            (t) => t.id === id
                          );
                          const name =
                            tenant?.tenant_name || `Tenant ${id}`;

                          return (
                            <div
                              key={id}
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                                padding: "6px 12px",
                                background: "white",
                                borderRadius: "20px",
                                border: "1px solid rgba(102, 126, 234, 0.2)",
                                fontSize: "12px",
                                fontWeight: 500,
                              }}
                            >
                              <span style={{ color: "#374151" }}>{name}</span>
                              <CloseOutlined
                                onClick={() => removeTenant(id)}
                                style={{
                                  cursor: "pointer",
                                  color: "#ef4444",
                                  fontSize: "10px",
                                  padding: "2px",
                                  borderRadius: "50%",
                                  transition: "all 0.2s ease",
                                }}
                                onMouseEnter={(e) => {
                                  const target = e.target as HTMLElement;
                                  target.style.background = "#ef4444";
                                  target.style.color = "white";
                                }}
                                onMouseLeave={(e) => {
                                  const target = e.target as HTMLElement;
                                  target.style.background = "transparent";
                                  target.style.color = "#ef4444";
                                }}
                              />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : null;
                }}
              </Form.Item>

              <Text style={{
                color: "#6b7280",
                fontSize: "12px",
                fontStyle: "italic",
                marginTop: "16px",
                display: "block",
              }}>
                💡 You can select multiple tenants. Use the search function to find specific tenants quickly.
              </Text>
            </div>

            {/* Submit Button */}
            <div style={{
              textAlign: "center",
              marginTop: 48,
              padding: "32px",
              background: "rgba(255, 255, 255, 0.8)",
              borderRadius: "20px",
              border: "1px solid rgba(226, 232, 240, 0.5)",
            }}>
              <Row gutter={24} justify="center">
                <Col xs={24} sm={12} md={8}>
                  <Button
                    style={{
                      width: "100%",
                      height: "48px",
                      borderRadius: "12px",
                      border: "2px solid #e5e7eb",
                      background: "white",
                      color: "#6b7280",
                      fontSize: "16px",
                      fontWeight: 600,
                      transition: "all 0.3s ease",
                    }}
                    onClick={() => router.push("/manageGames")}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.borderColor = "#ef4444";
                      target.style.color = "#ef4444";
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.borderColor = "#e5e7eb";
                      target.style.color = "#6b7280";
                    }}
                  >
                    Cancel
                  </Button>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                    style={{
                      width: "100%",
                      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                      border: "none",
                      borderRadius: "12px",
                      height: "48px",
                      fontSize: "16px",
                      fontWeight: 600,
                      boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                      transition: "all 0.3s ease",
                    }}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.transform = "translateY(-2px)";
                      target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.transform = "translateY(0)";
                      target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                    }}
                  >
                    {submitting ? "Creating Game..." : "Create Game"}
                  </Button>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .game-form-container {
          animation: fadeInUp 0.6s ease-out;
        }
      `}</style>
    </Layout>
  );
}
