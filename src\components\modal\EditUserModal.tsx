"use client";

import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Upload,
  message,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { EditUserFormValues, EditUserModalProps } from "@types";
import { supabaseClient } from "@utils/supabase/client";

export const EditUserModal: React.FC<EditUserModalProps> = ({
  visible,
  user,
  roles,
  loading,
  onCancel,
  onSubmit,
  isProfileEdit = false,
}) => {
  const [form] = Form.useForm();
  const [avatarUrl, setAvatarUrl] = useState<string>("");
  const [uploadingAvatar, setUploadingAvatar] = useState(false);

  React.useEffect(() => {
    if (visible && user) {
      // Populate form with user data
      form.setFieldsValue({
        firstName: user.name?.split(" ")[0] || "",
        lastName: user.name?.split(" ").slice(1).join(" ") || "",
        email: user.email,
        role_id: user.role,
        dob: null, // Not available in API
        gender: user.gender || "",
        countryCode: "+91",
        phone: user.phone_number || "",
        bio: user.bio || "",
      });
      // Set initial avatar URL
      setAvatarUrl(user.avatar || "");
    }
  }, [visible, user, form]);

  const handleAvatarUpload = async (file: File) => {
    try {
      setUploadingAvatar(true);
      const fileName = `avatars/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 24 * 365);

      if (signedUrlError) throw signedUrlError;

      const newAvatarUrl = signedUrlData?.signedUrl;
      if (!newAvatarUrl) throw new Error("Failed to get avatar URL");

      setAvatarUrl(newAvatarUrl);
    } catch (error) {
      console.error("Error uploading avatar:", error);
      message.error("Failed to upload avatar");
    } finally {
      setUploadingAvatar(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        return false;
      }

      // Create preview URL and set file for upload
      const previewUrl = URL.createObjectURL(file);
      setAvatarUrl(previewUrl);
      handleAvatarUpload(file);
      return false;
    },
    showUploadList: false,
  };

  const handleCancel = () => {
    form.resetFields();
    setAvatarUrl(user?.avatar || "");
    onCancel();
  };

  const handleSubmit = async (values: EditUserFormValues) => {
    // Include avatar URL in the form values
    const formValuesWithAvatar = {
      ...values,
      avatar_url: avatarUrl,
    };
    await onSubmit(formValuesWithAvatar);
    form.resetFields();
    setAvatarUrl("");
  };

  return (
    <Modal
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "12px",
            padding: "8px 0",
          }}
        >
          <div
            style={{
              width: "40px",
              height: "40px",
              borderRadius: "10px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <UploadOutlined style={{ color: "white", fontSize: "18px" }} />
          </div>
          <div>
            <div
              style={{
                fontSize: "18px",
                fontWeight: 600,
                color: "#374151",
                lineHeight: "1.2",
              }}
            >
              {isProfileEdit ? "Edit My Profile" : "Edit User Profile"}
            </div>
            <div
              style={{
                fontSize: "13px",
                color: "#6b7280",
                fontWeight: 400,
              }}
            >
              {isProfileEdit
                ? "Update your personal information and profile image"
                : "Update user information and profile image"}
            </div>
          </div>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button
          key="cancel"
          onClick={handleCancel}
          style={{
            height: "40px",
            borderRadius: "8px",
            fontWeight: 500,
            border: "1px solid #d1d5db",
            color: "#6b7280",
          }}
        >
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={() => form.submit()}
          style={{
            height: "40px",
            borderRadius: "8px",
            fontWeight: 600,
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            border: "none",
            boxShadow: "0 2px 8px rgba(102, 126, 234, 0.3)",
          }}
        >
          {loading
            ? "Updating..."
            : isProfileEdit
            ? "Update Profile"
            : "Update User"}
        </Button>,
      ]}
      width={1100}
      styles={{
        content: {
          borderRadius: "16px",
          padding: "0",
        },
        header: {
          borderBottom: "1px solid #f3f4f6",
          padding: "20px 24px",
          marginBottom: "0",
        },
        body: {
          padding: "24px",
        },
        footer: {
          borderTop: "1px solid #f3f4f6",
          padding: "16px 24px",
          marginTop: "0",
        },
      }}
    >
      {user && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{
            background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
            borderRadius: "12px",
            padding: "24px",
            border: "1px solid #e2e8f0",
          }}
        >
          <Row gutter={32}>
            {/* Left Side - Form Fields */}
            <Col xs={24} md={15}>
              {/* Personal Information Section */}
              <div
                style={{
                  background: "rgba(255, 255, 255, 0.9)",
                  borderRadius: "12px",
                  padding: "20px",
                  marginBottom: "20px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
                }}
              >
                <div
                  style={{
                    marginBottom: "16px",
                    paddingBottom: "12px",
                    borderBottom: "1px solid #f3f4f6",
                  }}
                >
                  <h4
                    style={{
                      margin: 0,
                      fontSize: "16px",
                      fontWeight: 600,
                      color: "#374151",
                    }}
                  >
                    Personal Information
                  </h4>
                  <p
                    style={{
                      margin: "4px 0 0 0",
                      fontSize: "13px",
                      color: "#6b7280",
                    }}
                  >
                    Update basic user details
                  </p>
                </div>

                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      name="firstName"
                      label={
                        <span
                          style={{
                            color: "#374151",
                            fontWeight: 500,
                            fontSize: "14px",
                          }}
                        >
                          First Name
                        </span>
                      }
                      style={{ marginBottom: 16 }}
                      rules={[
                        { required: true, message: "First Name is required" },
                      ]}
                    >
                      <Input
                        size="large"
                        placeholder="Enter first name"
                        style={{
                          borderRadius: "8px",
                          border: "1px solid #d1d5db",
                          fontSize: "14px",
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <Form.Item
                      name="lastName"
                      label={
                        <span
                          style={{
                            color: "#374151",
                            fontWeight: 500,
                            fontSize: "14px",
                          }}
                        >
                          Last Name
                        </span>
                      }
                      style={{ marginBottom: 16 }}
                      rules={[
                        { required: true, message: "Last Name is required" },
                      ]}
                    >
                      <Input
                        size="large"
                        placeholder="Enter last name"
                        style={{
                          borderRadius: "8px",
                          border: "1px solid #d1d5db",
                          fontSize: "14px",
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      name="gender"
                      label={
                        <span
                          style={{
                            color: "#374151",
                            fontWeight: 500,
                            fontSize: "14px",
                          }}
                        >
                          Gender
                        </span>
                      }
                      style={{ marginBottom: 16 }}
                      rules={[
                        { required: true, message: "Gender is required" },
                      ]}
                    >
                      <Select
                        size="large"
                        placeholder="Select gender"
                        style={{
                          borderRadius: "8px",
                        }}
                      >
                        <Select.Option value="male">Male</Select.Option>
                        <Select.Option value="female">Female</Select.Option>
                        <Select.Option value="other">Other</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <Form.Item
                      label={
                        <span
                          style={{
                            color: "#374151",
                            fontWeight: 500,
                            fontSize: "14px",
                          }}
                        >
                          Role
                        </span>
                      }
                      name="role_id"
                      style={{ marginBottom: 16 }}
                      rules={[
                        { required: true, message: "Please select a role" },
                      ]}
                    >
                      <Select
                        placeholder="Select role"
                        size="large"
                        disabled
                        style={{
                          borderRadius: "8px",
                        }}
                      >
                        {roles.map((role) => (
                          <Select.Option
                            key={role.role_name}
                            value={role.role_name}
                          >
                            {role.display_name || role.role_name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              {/* Contact Information Section */}
              <div
                style={{
                  background: "rgba(255, 255, 255, 0.9)",
                  borderRadius: "12px",
                  padding: "20px",
                  marginBottom: "20px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
                }}
              >
                <div
                  style={{
                    marginBottom: "16px",
                    paddingBottom: "12px",
                    borderBottom: "1px solid #f3f4f6",
                  }}
                >
                  <h4
                    style={{
                      margin: 0,
                      fontSize: "16px",
                      fontWeight: 600,
                      color: "#374151",
                    }}
                  >
                    Contact Information
                  </h4>
                  <p
                    style={{
                      margin: "4px 0 0 0",
                      fontSize: "13px",
                      color: "#6b7280",
                    }}
                  >
                    Phone number and additional details
                  </p>
                </div>

                <Form.Item
                  label={
                    <span
                      style={{
                        color: "#374151",
                        fontWeight: 500,
                        fontSize: "14px",
                      }}
                    >
                      Phone Number
                    </span>
                  }
                  style={{ marginBottom: 20 }}
                  required
                >
                  <div style={{ display: "flex", gap: "8px" }}>
                    <Form.Item
                      name="countryCode"
                      noStyle
                      rules={[
                        { required: true, message: "Country code is required" },
                      ]}
                      initialValue="+91"
                    >
                      <Select
                        size="large"
                        style={{
                          width: 120,
                          borderRadius: "8px",
                        }}
                      >
                        <Select.Option value="+1">+1 (US)</Select.Option>
                        <Select.Option value="+44">+44 (UK)</Select.Option>
                        <Select.Option value="+91">+91 (IN)</Select.Option>
                        <Select.Option value="+61">+61 (AU)</Select.Option>
                        <Select.Option value="+81">+81 (JP)</Select.Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="phone"
                      noStyle
                      rules={[
                        { required: true, message: "Phone number is required" },
                        {
                          pattern: /^\d{7,14}$/,
                          message: "Enter a valid phone number (7–14 digits)",
                        },
                      ]}
                    >
                      <Input
                        size="large"
                        inputMode="numeric"
                        pattern="\d*"
                        onKeyPress={(e) => {
                          if (!/[0-9]/.test(e.key)) {
                            e.preventDefault();
                          }
                        }}
                        style={{
                          flex: 1,
                          borderRadius: "8px",
                          border: "1px solid #d1d5db",
                          fontSize: "14px",
                        }}
                        placeholder="Phone Number"
                        maxLength={14}
                        minLength={7}
                        type="tel"
                      />
                    </Form.Item>
                  </div>
                </Form.Item>

                <Form.Item
                  label={
                    <span
                      style={{
                        color: "#374151",
                        fontWeight: 500,
                        fontSize: "14px",
                      }}
                    >
                      User Bio
                    </span>
                  }
                  name="bio"
                  rules={[{ required: true, message: "Please enter user bio" }]}
                >
                  <Input.TextArea
                    placeholder="Add bio..."
                    rows={4}
                    style={{
                      borderRadius: "8px",
                      resize: "none",
                      border: "1px solid #d1d5db",
                      fontSize: "14px",
                    }}
                  />
                </Form.Item>
              </div>
            </Col>

            {/* Right Side - Profile Image Upload */}
            <Col xs={24} md={9}>
              <div
                style={{
                  background: "rgba(255, 255, 255, 0.9)",
                  borderRadius: "12px",
                  padding: "20px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
                  height: "fit-content",
                  position: "sticky",
                  top: "0",
                }}
              >
                <div
                  style={{
                    marginBottom: "16px",
                    paddingBottom: "12px",
                    borderBottom: "1px solid #f3f4f6",
                    textAlign: "center",
                  }}
                >
                  <h4
                    style={{
                      margin: 0,
                      fontSize: "16px",
                      fontWeight: 600,
                      color: "#374151",
                    }}
                  >
                    Profile Image
                  </h4>
                  <p
                    style={{
                      margin: "4px 0 0 0",
                      fontSize: "13px",
                      color: "#6b7280",
                    }}
                  >
                    Upload or update profile picture
                  </p>
                </div>

                <div
                  style={{
                    width: "100%",
                    height: "240px",
                    borderRadius: "12px",
                    overflow: "hidden",
                    background:
                      "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    marginBottom: "20px",
                    border: "2px dashed #d1d5db",
                    position: "relative",
                  }}
                >
                  {avatarUrl ? (
                    <img
                      src={avatarUrl}
                      alt="Profile Preview"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        borderRadius: "10px",
                      }}
                    />
                  ) : (
                    <div
                      style={{
                        textAlign: "center",
                        color: "#9ca3af",
                      }}
                    >
                      <UploadOutlined
                        style={{
                          fontSize: "48px",
                          marginBottom: "12px",
                          display: "block",
                          color: "#d1d5db",
                        }}
                      />
                      <div
                        style={{
                          fontSize: "14px",
                          fontWeight: 500,
                          marginBottom: "4px",
                        }}
                      >
                        No image selected
                      </div>
                      <div
                        style={{
                          fontSize: "12px",
                          color: "#6b7280",
                        }}
                      >
                        Click below to upload
                      </div>
                    </div>
                  )}
                </div>

                <Form.Item name="profileImage" style={{ marginBottom: 0 }}>
                  <Upload {...uploadProps}>
                    <Button
                      icon={<UploadOutlined />}
                      loading={uploadingAvatar}
                      style={{
                        width: "100%",
                        height: "48px",
                        borderRadius: "10px",
                        border: "none",
                        color: "white",
                        fontWeight: 600,
                        fontSize: "14px",
                        background:
                          "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        boxShadow: "0 2px 8px rgba(102, 126, 234, 0.3)",
                        transition: "all 0.3s ease",
                      }}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(-1px)";
                        target.style.boxShadow =
                          "0 4px 12px rgba(102, 126, 234, 0.4)";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(0)";
                        target.style.boxShadow =
                          "0 2px 8px rgba(102, 126, 234, 0.3)";
                      }}
                    >
                      {uploadingAvatar
                        ? "Uploading..."
                        : avatarUrl
                        ? "Change Image"
                        : "Choose Image"}
                    </Button>
                  </Upload>
                </Form.Item>

                <div
                  style={{
                    marginTop: "12px",
                    padding: "12px",
                    background: "rgba(59, 130, 246, 0.05)",
                    borderRadius: "8px",
                    border: "1px solid rgba(59, 130, 246, 0.1)",
                  }}
                >
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#374151",
                      fontWeight: 500,
                      marginBottom: "4px",
                    }}
                  >
                    Image Requirements:
                  </div>
                  <ul
                    style={{
                      margin: 0,
                      paddingLeft: "16px",
                      fontSize: "11px",
                      color: "#6b7280",
                      lineHeight: "1.4",
                    }}
                  >
                    <li>Supported formats: JPG, PNG, GIF</li>
                    <li>Maximum file size: 5MB</li>
                    <li>Recommended: Square aspect ratio</li>
                  </ul>
                </div>
              </div>
            </Col>
          </Row>
        </Form>
      )}
    </Modal>
  );
};
