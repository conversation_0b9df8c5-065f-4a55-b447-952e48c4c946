"use client";
import React, { useState, useEffect } from "react";
import { Layout,  Button, Avatar, Space, Drawer, Typography, Dropdown } from "antd";
import {
  UserOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { useRouter, usePathname } from "next/navigation";
import NavigationMenu from "./NavigationMenu";
import { ROLE } from "@utils/supabase/constants";
import LogoutDialog from "@components/dialog/logoutDialog";

const { Header, Content } = Layout;
const { Title } = Typography;

const SUPABASE_PROJECT_REF = process.env.NEXT_PUBLIC_SUPABASE_URL
  ? new URL(process.env.NEXT_PUBLIC_SUPABASE_URL).hostname.split('.')[0]
  : "iwdfzvfqbtokqetmbmbp";
const SUPABASE_SESSION_KEY = `sb-${SUPABASE_PROJECT_REF}-auth-token`;

const roleConfig = {
  caregiver: {
   //vatar: "/navbar/caregiver.png",
    title: "Caregiver Connect",
  },
  superadmin: {
   //vatar: "/navbar/superadmin.png",
    title: "Tenant Engagement Hub",
  },
  tenant: {
   //vatar: "/navbar/tenant.png",
    title: "Tenant Hub",
  },
};
export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const role = ROLE as string;
  const {  title } = roleConfig[role as keyof typeof roleConfig];

  useEffect(() => {
    const session =
      localStorage.getItem(SUPABASE_SESSION_KEY) ||
      sessionStorage.getItem(SUPABASE_SESSION_KEY);

    if (!session) {
      router.push("/login");
    } else {
      setIsAuthenticated(true);
    }
  }, [pathname, router]);

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleLogout = () => {
    // Clear session storage
    localStorage.clear();
    sessionStorage.clear();
    // Redirect to login page
    router.push("/login");
  };

  const handleAvatarClick = () => {
    router.push("/profile");
  };

  if (!isAuthenticated) {
    return null; // or a loading spinner
  }

  return (
    <Layout>
      <Header
        style={{
          backgroundColor: "#fff",
          borderBottom: "1px solid #e8e8e8",
          padding: "0 16px",
          height: "64px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          flexWrap: "wrap",
          position: "sticky",
          top: 0,
          zIndex: 100,
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "32px" }}>
          {/* Logo and Title */}
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <div
              style={{
                width: "32px",
                height: "32px",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                fontWeight: "bold",
                fontSize: "18x",
              }}
            >
              ✱
            </div>
            <Title level={4}style={{ margin: 0 }}>
              {title}
            </Title>
          </div>

          {/* Navigation Menu */}
          {!isMobile && <NavigationMenu />}
        </div>

        {/* Right Side Icons */}
        <Space size="middle">
          {/* <Button type="text" icon={<SearchOutlined />} />
          <Button type="text" icon={<TeamOutlined />} />
          <Button type="text" icon={<BellOutlined />} /> */}

          <Dropdown
            trigger={["click"]}
            menu={{
              items: [
                {
                  key: "profile",
                  label: "Profile",
                  onClick: handleAvatarClick,
                },
                {
                  key: "logout",
                  label: <LogoutDialog onLogout={handleLogout} />,
                  onClick: (e) => e.domEvent.stopPropagation(),
                },
              ],
            }}
            placement="bottomRight"
          >
            <Avatar
              style={{
                backgroundColor: "#ff7f50",
                cursor: "pointer",
                transition: "transform 0.2s ease-in-out",
              }}
              icon={<UserOutlined />}
              className="avatar-hover"
            />
          </Dropdown>
          {isMobile && (
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setDrawerVisible(true)}
            />
          )}
        </Space>
      </Header>
      {/* Drawer for Mobile Menu */}
      <Drawer
        title="Navigation"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
       
      </Drawer>
      <Content style={{ minHeight: "calc(100vh - 64px)" }}>{children}</Content>
    </Layout>
  );
}
