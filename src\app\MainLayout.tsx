"use client";
import React, { useState, useEffect } from "react";
import { Layout, <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Typo<PERSON>, Spin } from "antd";
import {
  UserOutlined,
  MenuOutlined,
  SearchOutlined,
  TeamOutlined,
  BellOutlined,
} from "@ant-design/icons";
import { useRouter, usePathname } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import NavigationMenu from "./NavigationMenu";
// import LogoutDialog from "@components/dialog/logoutDialog";
import { ProfileData, UserData } from "@types";

const { Header, Content } = Layout;
const { Title } = Typography;

const SUPABASE_PROJECT_REF = process.env.NEXT_PUBLIC_SUPABASE_URL
  ? new URL(process.env.NEXT_PUBLIC_SUPABASE_URL).hostname.split(".")[0]
  : process.env.SUPABASE_SESSION_KEY;
const SUPABASE_SESSION_KEY = `sb-${SUPABASE_PROJECT_REF}-auth-token`;

const roleConfig = {
  CAREGIVER: {
    title: "Caregiver Connect",
  },
  SUPERADMIN: {
    title: "Admin Dashboard",
  },
  TENANT: {
    title: "Tenant Management Hub",
  },
  TENANTADM: {
    title: "Tenant Management Hub",
  },
  PLAYER: {
    title: "Player Portal",
  },
  default: {
    title: "RecallLoop",
  },
};

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState<string>("default");
  const [pageTitle, setPageTitle] = useState<string>("RecallLoop");
  const [role, setRole] = useState<string>("default");
  const [titleLoading, setTitleLoading] = useState<boolean>(true);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const storedRole = (localStorage.getItem("role") || "").trim();
    setRole(storedRole);
    const config =
      roleConfig[storedRole as keyof typeof roleConfig] || roleConfig.default;
    console.log(config, role, userRole, titleLoading);
  }, []);

  const fetchProfileData = async () => {
    try {
      setLoadingProfile(true);
      const userResponse = await dataProviderInstance.getUser();
      const userId = userResponse.data?.id;

      if (!userId || !userResponse.data) {
        throw new Error("User not found");
      }

      setUserData({
        id: userResponse.data.id,
        email: userResponse.data.email || "",
        user_metadata: {
          first_name: userResponse.data.user_metadata?.first_name || "",
          last_name: userResponse.data.user_metadata?.last_name || "",
          username: userResponse.data.user_metadata?.username || "",
          email: userResponse.data.user_metadata?.email || "",
        },
      });

      const profileResponse = await dataProviderInstance.getProfileDetails({
        party_id: userId,
      });

      if (
        profileResponse.data &&
        profileResponse.data.status === "success" &&
        profileResponse.data.data
      ) {
        setProfileData(profileResponse.data.data);
      }
    } catch (error) {
      console.error("Error fetching profile data:", error);
    } finally {
      setLoadingProfile(false);
    }
  };

  const getUserDisplayName = () => {
    if (profileData?.name) {
      return profileData.name;
    }
    if (profileData?.first_name && profileData?.last_name) {
      return `${profileData.first_name} ${profileData.last_name}`;
    }
    return userData?.user_metadata?.username || "User";
  };

  const handleAvatarClick = () => {
    const role = localStorage.getItem("role");
    if (role === "TENANT") {
      router.push("/tenants/profile");
    } else {
      router.push("/profile");
    }
  };

  useEffect(() => {
    fetchProfileData();
  }, []);

  // Fetch user role and set title
  useEffect(() => {
    const fetchUserRoleAndTitle = async () => {
      try {
        setTitleLoading(true);
        const userData = JSON.parse(localStorage.getItem("user") || "{}");
        const currentUserId = userData.id;

        if (currentUserId) {
          const response = await dataProviderInstance.getUserInfo(
            currentUserId
          );
          const fetchedUserRole = (
            response.data.data.party_type_key || ""
          ).trim();

          setUserRole(fetchedUserRole);

          // Set title based on role, fallback to default if not found
          const config =
            roleConfig[fetchedUserRole as keyof typeof roleConfig] ||
            roleConfig.default;
          setPageTitle(config.title);

          console.log("User role fetched:", fetchedUserRole);
          console.log("Title set to:", config.title);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
        // Fallback to default
        setUserRole("default");
        setPageTitle(roleConfig.default.title);
      } finally {
        setTitleLoading(false);
      }
    };

    const session =
      localStorage.getItem(SUPABASE_SESSION_KEY) ||
      sessionStorage.getItem(SUPABASE_SESSION_KEY);

    if (!session) {
      router.push("/login");
    } else {
      setIsAuthenticated(true);
      fetchUserRoleAndTitle();
    }
  }, [pathname, router]);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 1026);
      setIsTablet(width > 414 && width <= 1024);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  if (!isAuthenticated) {
    return null;
  }

  const isSmallDevice = isMobile || isTablet;
  const iconSize = isMobile ? 18 : isTablet ? 20 : 22;
  const rightGap = isMobile ? "8px" : isTablet ? "12px" : "16px";
  const headerPadding = isMobile ? "0 8px" : isTablet ? "0 12px" : "0 16px";

  return (
    <Layout>
      <Header
        style={{
          backgroundColor: "#fff",
          borderBottom: "1px solid #e8e8e8",
          padding: headerPadding,
          height: "64px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          position: "sticky",
          top: 0,
          zIndex: 100,

          flexWrap: "nowrap",
          minWidth: 0,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: isSmallDevice ? "8px" : "32px",
            flex: "1 1 auto",
            minWidth: 0,
            overflow: "hidden",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              flexShrink: 0,
            }}
          >
            <div
              style={{
                width: isMobile ? "28px" : "32px",
                height: isMobile ? "28px" : "32px",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                borderRadius: "6px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                fontWeight: "bold",
                fontSize: isMobile ? "14px" : "18px",
              }}
            >
              ✱
            </div>
            <Title
              level={4}
              style={{
                margin: 0,
                fontSize: isMobile ? "14px" : isTablet ? "16px" : "20px",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                lineHeight: 1.2,
              }}
            >
              {titleLoading ? "Loading..." : pageTitle}
            </Title>
          </div>

          {!isSmallDevice && <NavigationMenu />}
        </div>

        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: rightGap,
            flexShrink: 0,

            whiteSpace: "nowrap",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: isMobile ? "6px" : "8px",
              flexShrink: 0,
            }}
          >
            <SearchOutlined
              style={{
                fontSize: iconSize,
                cursor: "pointer",
                display: "block",
                lineHeight: 1,
              }}
              onClick={() => {
                /* TODO: search action */
              }}
            />
            <TeamOutlined
              style={{
                fontSize: iconSize,
                cursor: "pointer",
                display: "block",
                lineHeight: 1,
              }}
              onClick={() => {
                /* TODO: group chat action */
              }}
            />
            <BellOutlined
              style={{
                fontSize: iconSize,
                cursor: "pointer",
                display: "block",
                lineHeight: 1,
              }}
              onClick={() => {
                /* TODO: notification action */
              }}
            />
          </div>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: isMobile ? "4px" : "8px",
              cursor: "pointer",
              flexShrink: 0,
            }}
            onClick={handleAvatarClick}
          >
            {loadingProfile ? (
              <Spin size="small" />
            ) : (
              <>
                {!isMobile && (
                  <div
                    style={{
                      textAlign: "right",
                      maxWidth: isTablet ? "80px" : "120px",
                      overflow: "hidden",
                    }}
                  >
                    <div
                      style={{
                        fontSize: isTablet ? "12px" : "14px",
                        fontWeight: "500",
                        lineHeight: "1.2",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {getUserDisplayName()}
                    </div>
                  </div>
                )}
                <Avatar
                  src={profileData?.avatar_url}
                  size={isMobile ? 28 : isTablet ? 32 : 40}
                  style={{
                    backgroundColor: "#ff7f50",
                    cursor: "pointer",
                    transition: "transform 0.2s ease-in-out",
                    flexShrink: 0,
                  }}
                  icon={!profileData?.avatar_url && <UserOutlined />}
                  className="avatar-hover"
                />
              </>
            )}
          </div>

          {isMobile && (
            <Button
              type="text"
              icon={<MenuOutlined style={{ fontSize: 16 }} />}
              onClick={() => setDrawerVisible(true)}
              style={{
                flexShrink: 0,
                padding: "2px",
                minWidth: "24px",
                height: "24px",
                border: "none",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            />
          )}
        </div>
      </Header>

      <Drawer
        title="Navigation"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        <NavigationMenu />
      </Drawer>

      <Content style={{ minHeight: "calc(100vh - 64px)" }}>{children}</Content>
    </Layout>
  );
}
