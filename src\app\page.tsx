"use client";

import { Suspense } from "react";
import { Spin } from "antd";
import { NavigateToResource } from "@refinedev/nextjs-router";
import { Authenticated } from "@refinedev/core";

const LoadingFallback = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh'
  }}>
    <Spin size="large" />
  </div>
);

export default function IndexPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Authenticated key="home-page" fallback={<LoadingFallback />}>
        <NavigateToResource />
      </Authenticated>
    </Suspense>
  );
}
