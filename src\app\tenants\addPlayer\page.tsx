"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Card,
  Typography,
  DatePicker,
  Row,
  Col,
  Switch,
  message,
  Spin,
} from "antd";
import { dataProviderInstance } from "@providers/data-provider";
import { RoleType, ManageUserFormValues, Game } from "@types";
// import { supabaseClient } from "@utils/supabase/client";
import { useNotification } from "@refinedev/core";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
const { Title, Text } = Typography;
const { TextArea } = Input;

export default function AddPlayerPage() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loginRequired] = useState(true); // Always enabled as per UI
  const [roles, setRoles] = useState<RoleType[]>([]);
  const [rolesLoading, setRolesLoading] = useState(true);
  const { open } = useNotification();
  const tenantCode = localStorage.getItem("tenant_code");
  const party_id = localStorage.getItem("party_id");
  const tenant_id = localStorage.getItem("tenant_id");

  // Assign Game section state
  const [gameOptions, setGameOptions] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);
  const [selectedGameId, setSelectedGameId] = useState<string>("");
  const [gameDueDate, setGameDueDate] = useState<string>("");
  const [gameNotes, setGameNotes] = useState<string>("");
  //Fetch roles and user info (separate useEffect like userManagement page)
  useEffect(() => {
    const fetchData = async () => {
      try {
        setRolesLoading(true);
        //  Fetch roles for dropdown
        console.log("📋 Fetching roles...");
        const rolesResponse = await dataProviderInstance.listRoles();
        setRoles(rolesResponse.data || []);
        console.log("✅ Roles fetched:", rolesResponse.data);
      } catch (error: unknown) {
        console.error("❌ Error in fetchData:", error);
        message.error(
          `Failed to load required data: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      } finally {
        setRolesLoading(false);
        console.log("🏁 fetchData completed");
      }
    };

    fetchData();
  }, []);

  const fetchUnassignedGames = useCallback(async () => {
    if (!party_id) return;

    try {
      setLoadingGames(true);
      console.log("🎮 Fetching unassigned games for party_id:", party_id);

      const response = await dataProviderInstance.listUnAssignedGames({
        tenant_id: tenant_id || "",
        party_id: party_id,
      });

      if (response && response.data) {
        const games = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];
        setGameOptions(games);
        console.log("✅ Unassigned games fetched:", games);
      } else {
        setGameOptions([]);
      }
    } catch (error) {
      console.error("❌ Error fetching unassigned games:", error);
      message.error(ERROR_MESSAGES.load_games);
      setGameOptions([]);
    } finally {
      setLoadingGames(false);
    }
  }, [party_id]);

  // Fetch unassigned games when party_id is available
  useEffect(() => {
    if (party_id) {
      fetchUnassignedGames();
    }
  }, [party_id, fetchUnassignedGames]);

  const onFinish = async (values: ManageUserFormValues) => {
    setLoading(true);
    try {
      // Validate login fields if login is required
      if (loginRequired) {
        if (!values.username || !values.email || !values.password) {
          open?.({
            type: "error",
            message: "Validation Error",
            description:
              "Please fill in all login fields when login is enabled.",
          });
          setLoading(false);
          return;
        }
      }
      // Find the selected role
      const selectedRole = roles.find(
        (role) => role.role_name === values.role_id
      );
      if (!selectedRole) {
        open?.({
          type: "error",
          message: "Role Error",
          description: "Selected role not found. Please select a valid role.",
        });
        setLoading(false);
        return;
      }
      // Determine party_type_key based on selected role
      const party_type_key =
        selectedRole.role_name.toLowerCase() === "caregiver"
          ? "CAREGIVER"
          : selectedRole.role_name.toLowerCase() === "player"
          ? "PLAYER"
          : "";

      // Validate tenant code is available (critical for API call)
      if (!tenantCode) {
        open?.({
          type: "error",
          message: "Tenant Code Missing",
          description:
            "Tenant code is required but not available. Please refresh the page and try again.",
        });
        setLoading(false);
        return;
      }
      try {
        let authUserId = null;
        if (loginRequired) {
          const signUpResponse = await dataProviderInstance.signUpUser({
            email: values.email!,
            password: values.password!,
            first_name: values.firstName!,
            last_name: values.lastName!,
            phone_number: `${values.countryCode!} ${values.phone!}`,
            username: values.username!,
            tenant_code: tenantCode!,
            party_id: party_id!,
            rolename: selectedRole.role_name.toUpperCase(),
            avatar_url: null,
            party_type_key,
          });
          if (!signUpResponse.data?.user?.id) {
            throw new Error("Failed to create user. No user ID returned.");
          }
          authUserId = signUpResponse.data.user.id;
          console.log("Auth user created:", authUserId);
        }

        const playerData = {
          bio: (values.bio as string) || "",
          date_of_birth: values.dob
            ? typeof values.dob === "string"
              ? values.dob
              : values.dob.format("YYYY-MM-DD")
            : null,
          first_name: values.firstName as string,
          gender: values.gender as string,
          is_login_enabled: loginRequired,
          last_name: values.lastName as string,
          party_id: authUserId ?? "",
          party_type_key,
          phone_number: `${values.countryCode} ${values.phone}`,
          profile_url: "",
          //  profile_url: thumbnailUrl as string,
          role_id: selectedRole.id,
          tenant_code: tenantCode, // Validated to exist above
        };
        const createPlayerResponse = await dataProviderInstance.manageUser(
          playerData
        );
        console.log(
          "✅ Player created successfully:",
          createPlayerResponse.data
        );

        // Update auth user metadata if login was enabled
        // if (authUserId && loginRequired) {
        //   const { data: updateData, error: updateError } = await supabaseClient.auth.updateUser({
        //     data: {
        //       role_id: selectedRole.id,
        //       role_name: selectedRole.role_name,
        //     },
        //   });

        //   console.log("📋 UpdateUser API Response:", updateData);

        //   if (updateError) {
        //     console.error("User update error:", updateError);
        //     console.warn(
        //       "Failed to update user metadata, but player was created successfully"
        //     );
        //   } else {
        //     console.log("User metadata updated successfully");
        //   }
        // }
        // Success notification will be shown after game assignment (if any)
        if (!selectedGameId) {
          // Only show this if no game is being assigned
          open?.({
            type: "success",
            message: `${selectedRole.role_name} Created Successfully!`,
            description: `${selectedRole.role_name} "${values.firstName} ${
              values.lastName
            }" has been added${party_id ? " with login access" : ""}.`,
          });
        }
        // Auto-assign game if one is selected
        if (selectedGameId && authUserId) {
          try {
            await dataProviderInstance.assignGame({
              // party_ids: authUserId ? [authUserId] : [],
              // game_id: selectedGameId,
              // due_date: gameDueDate || undefined,
              // notes: gameNotes || undefined,
              due_date: null,
              game_ids: selectedGameId ? [selectedGameId] : ([] as string[]),
              notes: gameNotes || null, // You must include this if your function expects it
              party_ids: authUserId ? [authUserId] : [],
            });
            message.success("Player created and game assigned successfully!");
            router.push("/tenants/viewUsers");
            // Reset game assignment form
            setSelectedGameId("");
            setGameDueDate("");
            setGameNotes("");
            // Refresh unassigned games list
            fetchUnassignedGames();
          } catch (gameError) {
            console.error("❌ Error assigning game:", gameError);
            message.error(
              "Player created successfully, but failed to assign game"
            );
          }
        } else {
          console.log("ℹ️ No game selected, skipping game assignment");
        }
        // Reset form
        form.resetFields();
      } catch (error) {
        open?.({
          type: "error",
          message: "Failed to Create Player",
        });
        console.error("Failed to create player:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  const onReset = () => {
    form.resetFields();
  };

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 8,
      }}
    >
      <Card>
        {/* Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "32px",
            paddingBottom: "16px",
          }}
        >
          <Title level={3} style={{ margin: 0, fontWeight: 600 }}>
            Add User
          </Title>
          <Button
            type="text"
            onClick={() => window.history.back()}
            style={{ color: "#666" }}
          >
            Cancel
          </Button>
        </div>

        {/* Form */}
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ width: "100%" }}
        >
          <Title level={5} style={{ marginBottom: "24px", color: "#333" }}>
            User Information
          </Title>

          <Row gutter={32}>
            <Col xs={24} md={12}>
              <Form.Item
                name="firstName"
                label="First Name"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "First Name is required" }]}
              >
                <Input size="large" placeholder="First Name" />
              </Form.Item>

              <Form.Item
                name="dob"
                label="Date of Birth"
                style={{ marginBottom: 16 }}
                rules={[
                  { required: true, message: "Date of Birth is required" },
                ]}
              >
                <DatePicker
                  size="large"
                  style={{ width: "100%" }}
                  disabledDate={(current) =>
                    current &&
                    current > dayjs().subtract(18, "year").endOf("day")
                  }
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="lastName"
                label="Last Name"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "Last Name is required" }]}
              >
                <Input size="large" placeholder="Last Name" />
              </Form.Item>

              <Form.Item
                name="gender"
                label="Gender"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "Gender is required" }]}
              >
                <Select size="large" placeholder="Select Gender">
                  <Select.Option value="male">Male</Select.Option>
                  <Select.Option value="female">Female</Select.Option>
                  <Select.Option value="other">Other</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Game Assignments"
                name="gameAssignments"
                rules={[
                  {
                    required: true,
                    message: "You have to select at least one game assignment",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select game assignments"
                  style={{ minHeight: "40px" }}
                  options={gameAssignments}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Team Name" name="teamName">
                <Input
                  placeholder="Add team name"
                  style={{
                    borderRadius: "4px",
                    height: "40px",
                  }}
                />
              </Form.Item>
            </Col>
          </Row> */}

          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                label="Phone Number"
                style={{ marginBottom: 24 }}
                required
              >
                <div style={{ display: "flex" }}>
                  <Form.Item
                    name="countryCode"
                    noStyle
                    rules={[
                      { required: true, message: "Country code is required" },
                    ]}
                    initialValue="+91"
                  >
                    <Select
                      size="large"
                      style={{
                        width: 120,
                        borderRadius: "6px 0 0 6px",
                        fontSize: 16,
                      }}
                    >
                      <Select.Option value="+1">+1 (US)</Select.Option>
                      <Select.Option value="+44">+44 (UK)</Select.Option>
                      <Select.Option value="+91">+91 (IN)</Select.Option>
                      <Select.Option value="+61">+61 (AU)</Select.Option>
                      <Select.Option value="+81">+81 (JP)</Select.Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="phone"
                    noStyle
                    rules={[
                      { required: true, message: "Phone number is required" },
                      {
                        pattern: /^\d{7,14}$/,
                        message: "Enter a valid phone number (7-14 digits)",
                      },
                    ]}
                  >
                    <Input
                      size="large"
                      style={{
                        width: "calc(100% - 120px)",
                        borderRadius: "0 6px 6px 0",
                      }}
                      placeholder="Phone Number"
                      maxLength={14}
                      minLength={7}
                      type="tel"
                    />
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label={<span>Role</span>}
                name="role_id"
                rules={[{ required: true, message: "Please select a role" }]}
              >
                <Select
                  placeholder={
                    rolesLoading ? "Loading roles..." : "Select role"
                  }
                  loading={rolesLoading}
                  style={{ height: "40px" }}
                  disabled={rolesLoading}
                  notFoundContent={
                    rolesLoading ? "Loading..." : "No roles found"
                  }
                >
                  {roles.map((role) => (
                    <Select.Option key={role.role_name} value={role.role_name}>
                      {role.display_name || role.role_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="User Bio"
            name="bio"
            rules={[{ required: true, message: "Please enter user bio" }]}
          >
            <TextArea
              placeholder="Add bio..."
              rows={4}
              style={{
                borderRadius: "4px",
                resize: "none",
              }}
            />
          </Form.Item>

          {/* Login Required Section */}
          <Form.Item style={{ marginBottom: 16 }}>
            <div style={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Title level={4} style={{ margin: 0, fontWeight: 700 }}>
                User Details
              </Title>
              {/* <Switch checked={loginRequired} onChange={setLoginRequired} /> */}
              <Switch
                checked={true}
                disabled={true}
                style={{ margin: "0 8px" }}
              />
              <Title level={5} style={{ margin: 0, fontWeight: 300 }}>
                ( Login Enabled )
              </Title>
            </div>
          </Form.Item>
          {loginRequired && (
            <div>
              <Row gutter={24}>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="username"
                    label="Username"
                    style={{ marginBottom: 16 }}
                    rules={[
                      { required: true, message: "Username is required" },
                    ]}
                  >
                    <Input size="large" placeholder="Username" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="email"
                    label="Email"
                    style={{ marginBottom: 16 }}
                    rules={[
                      { required: true, message: "Email is required" },
                      {
                        type: "email",
                        message: "Please enter a valid email address",
                      },
                    ]}
                  >
                    <Input size="large" placeholder="Email" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="password"
                    label="Password"
                    style={{ marginBottom: 16 }}
                    rules={[
                      { required: true, message: "Password is required" },
                      {
                        min: 6,
                        message: "Password must be at least 6 characters",
                      },
                    ]}
                  >
                    <Input.Password size="large" placeholder="Password" />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          )}
          {/* {createdPlayerId && ( */}
          <div
            style={{
              marginTop: "32px",
              paddingTop: "24px",
              borderTop: "1px solid #f0f0f0",
            }}
          >
            <Typography.Title level={4} style={{ marginBottom: "8px" }}>
              Game Assignment (Optional)
            </Typography.Title>
            <Typography.Text
              type="secondary"
              style={{ marginBottom: "24px", display: "block" }}
            >
              Select a game to automatically assign to the player after creation
            </Typography.Text>

            <Row gutter={16}>
              <Col xs={24} md={24}>
                <div style={{ marginBottom: 16 }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Select Game
                  </label>
                  <Select
                    size="large"
                    placeholder="Select a game to assign"
                    value={selectedGameId}
                    onChange={(value) => {
                      console.log("🎮 Game selected:", value);
                      setSelectedGameId(value);
                    }}
                    loading={loadingGames}
                    style={{ width: "100%" }}
                    options={gameOptions.map((game) => ({
                      label: game.game_name,
                      value: game.game_id,
                    }))}
                    notFoundContent={
                      loadingGames ? (
                        <Spin size="small" />
                      ) : (
                        "No games available"
                      )
                    }
                  />
                </div>
              </Col>

              <Col xs={24} md={5}>
                <div style={{ marginBottom: 16 }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Due Date
                  </label>
                  <DatePicker
                    size="large"
                    style={{ width: "100%" }}
                    placeholder="Select due date"
                    value={gameDueDate ? dayjs(gameDueDate) : null}
                    onChange={(_, dateString) => {
                      if (typeof dateString === "string")
                        setGameDueDate(dateString);
                      else setGameDueDate("");
                    }}
                    disabledDate={(current) =>
                      current && current < dayjs().startOf("day")
                    }
                  />
                </div>
              </Col>
              <Col xs={24} sm={19}>
                <div>
                  <Text
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Notes <span style={{ color: "red" }}>*</span>
                  </Text>
                  <TextArea
                    placeholder="Enter notes"
                    value={gameNotes}
                    onChange={(e) => setGameNotes(e.target.value)}
                    rows={4}
                    style={{ width: "100%" }}
                  />
                </div>
              </Col>
            </Row>
          </div>
          {/* )} */}
          {/* Action Buttons */}
          <div
            style={{
              display: "flex",
              gap: "12px",
              marginTop: "32px",
              paddingTop: "24px",
              justifyContent: "flex-end",
            }}
          >
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
                backgroundColor: "#6366f1",
                borderColor: "#6366f1",
                borderRadius: "4px",
                height: "40px",
                fontSize: "14px",
                fontWeight: 500,
                paddingLeft: "24px",
                paddingRight: "24px",
              }}
            >
              Submit
            </Button>
            <Button
              onClick={onReset}
              style={{
                borderRadius: "4px",
                height: "40px",
                fontSize: "14px",
                paddingLeft: "24px",
                paddingRight: "24px",
              }}
            >
              Reset
            </Button>
          </div>
        </Form>

        {/* Assign Game Section */}
      </Card>
    </div>
  );
}
