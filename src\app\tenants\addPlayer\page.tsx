"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Card,
  Typography,
  DatePicker,
  Row,
  Col,
  Switch,
  message,
  Spin,
  Layout,
} from "antd";
import { dataProviderInstance } from "@providers/data-provider";
import { RoleType, ManageUserFormValues, Game } from "@types";
// import { supabaseClient } from "@utils/supabase/client";
import { useNotification } from "@refinedev/core";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
const { Title, Text } = Typography;
const { TextArea } = Input;
const { Content } = Layout;

export default function AddPlayerPage() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loginRequired] = useState(true); // Always enabled as per UI
  const [roles, setRoles] = useState<RoleType[]>([]);
  const [rolesLoading, setRolesLoading] = useState(true);
  const { open } = useNotification();
  const tenantCode = localStorage.getItem("tenant_code");
  const party_id = localStorage.getItem("party_id");
  const tenant_id = localStorage.getItem("tenant_id");

  // Assign Game section state
  const [gameOptions, setGameOptions] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);
  const [selectedGameId, setSelectedGameId] = useState<string>("");
  const [gameDueDate, setGameDueDate] = useState<string>("");
  const [gameNotes, setGameNotes] = useState<string>("");
  //Fetch roles and user info (separate useEffect like userManagement page)
  useEffect(() => {
    const fetchData = async () => {
      try {
        setRolesLoading(true);
        //  Fetch roles for dropdown
        console.log("📋 Fetching roles...");
        const rolesResponse = await dataProviderInstance.listRoles();
        setRoles(rolesResponse.data || []);
        console.log("✅ Roles fetched:", rolesResponse.data);
      } catch (error: unknown) {
        console.error("❌ Error in fetchData:", error);
        message.error(
          `Failed to load required data: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      } finally {
        setRolesLoading(false);
        console.log("🏁 fetchData completed");
      }
    };

    fetchData();
  }, []);

  const fetchUnassignedGames = useCallback(async () => {
    if (!party_id) return;

    try {
      setLoadingGames(true);
      console.log("🎮 Fetching unassigned exercises for party_id:", party_id);

      const response = await dataProviderInstance.listUnAssignedGames({
        tenant_id: tenant_id || "",
        party_id: party_id,
      });

      if (response && response.data) {
        const games = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];
        setGameOptions(games);
        console.log("✅ Unassigned exercises fetched:", games);
      } else {
        setGameOptions([]);
      }
    } catch (error) {
      console.error("❌ Error fetching unassigned Exercise:", error);
      message.error(ERROR_MESSAGES.load_games);
      setGameOptions([]);
    } finally {
      setLoadingGames(false);
    }
  }, [party_id]);

  // Fetch unassigned games when party_id is available
  useEffect(() => {
    if (party_id) {
      fetchUnassignedGames();
    }
  }, [party_id, fetchUnassignedGames]);

  const onFinish = async (values: ManageUserFormValues) => {
    setLoading(true);
    try {
      // Validate login fields if login is required
      if (loginRequired) {
        if (!values.username || !values.email || !values.password) {
          open?.({
            type: "error",
            message: "Validation Error",
            description:
              "Please fill in all login fields when login is enabled.",
          });
          setLoading(false);
          return;
        }
      }
      // Find the selected role
      const selectedRole = roles.find(
        (role) => role.role_name === values.role_id
      );
      if (!selectedRole) {
        open?.({
          type: "error",
          message: "Role Error",
          description: "Selected role not found. Please select a valid role.",
        });
        setLoading(false);
        return;
      }
      // Determine party_type_key based on selected role
      const p_party_type_key =
        selectedRole.role_name.toLowerCase() === "caregiver"
          ? "CAREGIVER"
          : selectedRole.role_name.toLowerCase() === "player"
          ? "PLAYER"
          : "";
      const party_type_key =
        selectedRole.role_name.toLowerCase() === "caregiver"
          ? "CAREGIVER"
          : selectedRole.role_name.toLowerCase() === "player"
          ? "PLAYER"
          : "";
      // Validate tenant code is available (critical for API call)
      if (!tenantCode) {
        open?.({
          type: "error",
          message: "Tenant Code Missing",
          description:
            "Tenant code is required but not available. Please refresh the page and try again.",
        });
        setLoading(false);
        return;
      }
      try {
         const userDetails = JSON.parse(localStorage.getItem("user") || "{}");
        let authUserId = null;
        if (loginRequired) {
          const signUpResponse = await dataProviderInstance.signUpUser({
            email: values.email!,
            password: values.password!,
            // first_name: values.firstName!,
            // last_name: values.lastName!,
            // phone_number: `${values.countryCode!} ${values.phone!}`,
            // username: values.username!,
            // tenant_code: tenantCode!,
            // party_id: party_id!,
            // rolename: selectedRole.role_name.toUpperCase(),
            // avatar_url: null,
            // party_type_key,
          });
          if (!signUpResponse.data?.user?.id) {
            throw new Error("Failed to create user. No user ID returned.");
          }
          authUserId = signUpResponse.data.user.id;
         
          const reqparams = {
            p_first_name: values.firstName as string,
            p_last_name: values.lastName as string,
            p_email: values.email as string,
            p_address: "",
            p_phone: values.countryCode + " " + values.phone,
            p_username: values.username as string,
            p_tenant_code: tenantCode as string,
            p_rolename: selectedRole.role_name.toUpperCase(),
            p_avatar_url: "",
            p_party_type_key,
            p_party_id: userDetails.id as string,
            p_user_id: authUserId as string,
          };
          await dataProviderInstance.updateParty(reqparams);
          console.log("Auth user created:", authUserId);
        }

        const playerData = {
          bio: (values.bio as string) || "",
          date_of_birth: values.dob
            ? typeof values.dob === "string"
              ? values.dob
              : values.dob.format("YYYY-MM-DD")
            : null,
          first_name: values.firstName as string,
          gender: values.gender as string,
          is_login_enabled: loginRequired,
          last_name: values.lastName as string,
          party_id: authUserId ?? "",
          party_type_key,
          phone_number: `${values.countryCode} ${values.phone}`,
          profile_url: "",
          parent_party_id: userDetails.id as string,
          
          //  profile_url: thumbnailUrl as string,
          role_id: selectedRole.id,
          tenant_code: tenantCode, // Validated to exist above
        };
        const createPlayerResponse = await dataProviderInstance.manageUser(
          playerData
        );
        console.log(
          "✅ Player created successfully:",
          createPlayerResponse.data
        );

        // Update auth user metadata if login was enabled
        // if (authUserId && loginRequired) {
        //   const { data: updateData, error: updateError } = await supabaseClient.auth.updateUser({
        //     data: {
        //       role_id: selectedRole.id,
        //       role_name: selectedRole.role_name,
        //     },
        //   });

        //   console.log("📋 UpdateUser API Response:", updateData);

        //   if (updateError) {
        //     console.error("User update error:", updateError);
        //     console.warn(
        //       "Failed to update user metadata, but player was created successfully"
        //     );
        //   } else {
        //     console.log("User metadata updated successfully");
        //   }
        // }
        // Success notification will be shown after game assignment (if any)
        if (!selectedGameId) {
          // Only show this if no Exercise is being assigned
          open?.({
            type: "success",
            message: `${selectedRole.role_name} Created Successfully!`,
            description: `${selectedRole.role_name} "${values.firstName} ${
              values.lastName
            }" has been added${party_id ? " with login access" : ""}.`,
          });
        }
        // Auto-assign Exercise if one is selected
        if (selectedGameId && authUserId) {
          try {
            await dataProviderInstance.assignGame({
              // party_ids: authUserId ? [authUserId] : [],
              // game_id: selectedGameId,
              // due_date: gameDueDate || undefined,
              // notes: gameNotes || undefined,
              due_date: null,
              game_ids: selectedGameId ? [selectedGameId] : ([] as string[]),
              notes: gameNotes || null, // You must include this if your function expects it
              party_ids: authUserId ? [authUserId] : [],
            });
            message.success(
              "Player created and exercise assigned successfully!"
            );
            router.push("/tenants/viewUsers");
            // Reset game assignment form
            setSelectedGameId("");
            setGameDueDate("");
            setGameNotes("");
            // Refresh unassigned games list
            fetchUnassignedGames();
          } catch (gameError) {
            console.error("❌ Error assigning exercise:", gameError);
            message.error(
              "Player created successfully, but failed to assign exercise"
            );
          }
        } else {
          console.log("ℹ️ No exercise selected, skipping exercise assignment");
        }
        // Reset form
        form.resetFields();
      } catch (error) {
        open?.({
          type: "error",
          message: "Failed to Create Player",
        });
        console.error("Failed to create player:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  const onReset = () => {
    form.resetFields();
  };

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "48px",
            maxWidth: "800px",
          }}
        >
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            Add New User
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Create a new user account with comprehensive details and optional
            exercise assignments
          </Text>
        </div>

        {/* Main Form Container */}
        <Card
          style={{
            width: "100%",
            maxWidth: "1000px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "16px",
          }}
          styles={{ body: { padding: "32px" } }}
        >
          {/* Header */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "40px",
              paddingBottom: "24px",
              borderBottom: "1px solid rgba(226, 232, 240, 0.5)",
            }}
          >
            <Title
              level={2}
              style={{
                margin: 0,
                fontWeight: 600,
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              User Information
            </Title>
            <Button
              type="text"
              onClick={() => window.history.back()}
              size="large"
              style={{
                color: "#6b7280",
                borderRadius: "12px",
                padding: "8px 16px",
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.background = "rgba(107, 114, 128, 0.1)";
                target.style.color = "#374151";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.background = "transparent";
                target.style.color = "#6b7280";
              }}
            >
              Cancel
            </Button>
          </div>

          {/* Form */}
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            style={{ width: "100%" }}
          >
            {/* Basic Information Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Title
                level={4}
                style={{
                  marginBottom: "24px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Basic Information
              </Title>

              <Row gutter={32}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="firstName"
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        First Name
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "First Name is required" },
                    ]}
                  >
                    <Input
                      size="large"
                      placeholder="Enter first name"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                      }}
                    />
                  </Form.Item>

                  {/* <Form.Item
                    name="dob"
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Date of Birth
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Date of Birth is required" },
                    ]}
                  >
                    <DatePicker
                      size="large"
                      style={{
                        width: "100%",
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                      }}
                      placeholder="Select date of birth"
                      disabledDate={(current) =>
                        current &&
                        current > dayjs().subtract(18, "year").endOf("day")
                      }
                    />
                  </Form.Item> */}
                  <Form.Item
                    name="dob"
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Date of Birth
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Date of Birth is required" },
                    ]}
                  >
                    <DatePicker
                      size="large"
                      style={{
                        width: "100%",
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                      }}
                      placeholder="Select date of birth"
                      defaultPickerValue={dayjs().subtract(18, "year")}
                      disabledDate={(current) =>
                        current &&
                        current > dayjs().subtract(18, "year").endOf("day")
                      }
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="lastName"
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Last Name
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Last Name is required" },
                    ]}
                  >
                    <Input
                      size="large"
                      placeholder="Enter last name"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="gender"
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Gender
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[{ required: true, message: "Gender is required" }]}
                  >
                    <Select
                      size="large"
                      placeholder="Select gender"
                      style={{
                        borderRadius: "12px",
                      }}
                    >
                      <Select.Option value="male">Male</Select.Option>
                      <Select.Option value="female">Female</Select.Option>
                      <Select.Option value="other">Other</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Game Assignments"
                name="gameAssignments"
                rules={[
                  {
                    required: true,
                    message: "You have to select at least one game assignment",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select game assignments"
                  style={{ minHeight: "40px" }}
                  options={gameAssignments}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Team Name" name="teamName">
                <Input
                  placeholder="Add team name"
                  style={{
                    borderRadius: "4px",
                    height: "40px",
                  }}
                />
              </Form.Item>
            </Col>
          </Row> */}

            {/* Contact Information Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Title
                level={4}
                style={{
                  marginBottom: "24px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Contact Information
              </Title>

              <Row gutter={32}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Phone Number
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    required
                  >
                    <div style={{ display: "flex" }}>
                      <Form.Item
                        name="countryCode"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: "Country code is required",
                          },
                        ]}
                        initialValue="+91"
                      >
                        <Select
                          size="large"
                          style={{
                            width: 120,
                            borderRadius: "12px 0 0 12px",
                            fontSize: 16,
                          }}
                        >
                          <Select.Option value="+1">+1 (US)</Select.Option>
                          <Select.Option value="+44">+44 (UK)</Select.Option>
                          <Select.Option value="+91">+91 (IN)</Select.Option>
                          <Select.Option value="+61">+61 (AU)</Select.Option>
                          <Select.Option value="+81">+81 (JP)</Select.Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        name="phone"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: "Phone number is required",
                          },
                          {
                            pattern: /^\d{7,14}$/,
                            message: "Enter a valid phone number (7-14 digits)",
                          },
                        ]}
                      >
                        <Input
                          size="large"
                          style={{
                            width: "calc(100% - 120px)",
                            borderRadius: "0 12px 12px 0",
                            border: "1px solid rgba(209, 213, 219, 0.8)",
                            background: "rgba(255, 255, 255, 0.9)",
                          }}
                          placeholder="Phone Number"
                          maxLength={14}
                          minLength={7}
                          type="tel"
                        />
                      </Form.Item>
                    </div>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Role
                      </Text>
                    }
                    name="role_id"
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Please select a role" },
                    ]}
                  >
                    <Select
                      placeholder={
                        rolesLoading ? "Loading roles..." : "Select role"
                      }
                      loading={rolesLoading}
                      size="large"
                      style={{
                        borderRadius: "12px",
                      }}
                      disabled={rolesLoading}
                      notFoundContent={
                        rolesLoading ? "Loading..." : "No roles found"
                      }
                    >
                      {roles.map((role) => (
                        <Select.Option
                          key={role.role_name}
                          value={role.role_name}
                        >
                          {role.display_name || role.role_name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label={
                  <Text style={{ fontWeight: 500, color: "#374151" }}>
                    User Bio
                  </Text>
                }
                name="bio"
                style={{ marginBottom: 0 }}
                rules={[{ required: true, message: "Please enter user bio" }]}
              >
                <TextArea
                  placeholder="Enter user biography..."
                  rows={4}
                  style={{
                    borderRadius: "12px",
                    resize: "none",
                    border: "1px solid rgba(209, 213, 219, 0.8)",
                    background: "rgba(255, 255, 255, 0.9)",
                  }}
                />
              </Form.Item>
            </div>

            {/* Login Credentials Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 12,
                  marginBottom: "24px",
                }}
              >
                <Title
                  level={4}
                  style={{
                    margin: 0,
                    fontWeight: 600,
                    color: "#374151",
                  }}
                >
                  Login Credentials
                </Title>
                <Switch
                  checked={true}
                  disabled={true}
                  style={{
                    margin: "0 8px",
                    background:
                      "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  }}
                />
                <Text
                  style={{
                    margin: 0,
                    fontWeight: 500,
                    color: "#10b981",
                    fontSize: "14px",
                  }}
                >
                  Login Enabled
                </Text>
              </div>

              {loginRequired && (
                <Row gutter={24}>
                  <Col xs={24} md={8}>
                    <Form.Item
                      name="username"
                      label={
                        <Text style={{ fontWeight: 500, color: "#374151" }}>
                          Username
                        </Text>
                      }
                      style={{ marginBottom: 24 }}
                      rules={[
                        { required: true, message: "Username is required" },
                      ]}
                    >
                      <Input
                        size="large"
                        placeholder="Enter username"
                        style={{
                          borderRadius: "12px",
                          border: "1px solid rgba(209, 213, 219, 0.8)",
                          background: "rgba(255, 255, 255, 0.9)",
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <Form.Item
                      name="email"
                      label={
                        <Text style={{ fontWeight: 500, color: "#374151" }}>
                          Email
                        </Text>
                      }
                      style={{ marginBottom: 24 }}
                      rules={[
                        { required: true, message: "Email is required" },
                        {
                          type: "email",
                          message: "Please enter a valid email address",
                        },
                      ]}
                    >
                      <Input
                        size="large"
                        placeholder="Enter email address"
                        style={{
                          borderRadius: "12px",
                          border: "1px solid rgba(209, 213, 219, 0.8)",
                          background: "rgba(255, 255, 255, 0.9)",
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <Form.Item
                      name="password"
                      label={
                        <Text style={{ fontWeight: 500, color: "#374151" }}>
                          Password
                        </Text>
                      }
                      style={{ marginBottom: 24 }}
                      rules={[
                        { required: true, message: "Password is required" },
                        {
                          min: 6,
                          message: "Password must be at least 6 characters",
                        },
                      ]}
                    >
                      <Input.Password
                        size="large"
                        placeholder="Enter password"
                        style={{
                          borderRadius: "12px",
                          border: "1px solid rgba(209, 213, 219, 0.8)",
                          background: "rgba(255, 255, 255, 0.9)",
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}
            </div>
            {/* Game Assignment Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Title
                level={4}
                style={{
                  marginBottom: "8px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Exercise Assignment (Optional)
              </Title>
              <Text
                type="secondary"
                style={{
                  marginBottom: "24px",
                  display: "block",
                  color: "#6b7280",
                  fontSize: "14px",
                }}
              >
                Select a exercise to automatically assign to the player after
                creation
              </Text>

              <Row gutter={24}>
                <Col xs={24} md={24}>
                  <div style={{ marginBottom: 24 }}>
                    <Text
                      style={{
                        display: "block",
                        marginBottom: 8,
                        fontWeight: 500,
                        color: "#374151",
                      }}
                    >
                      Select Exercise
                    </Text>
                    <Select
                      size="large"
                      placeholder="Select a exercise to assign"
                      value={selectedGameId}
                      onChange={(value) => {
                        console.log("🎮 exercise selected:", value);
                        setSelectedGameId(value);
                      }}
                      loading={loadingGames}
                      style={{
                        width: "100%",
                        borderRadius: "12px",
                      }}
                      options={gameOptions.map((game) => ({
                        label: game.game_name,
                        value: game.game_id,
                      }))}
                      notFoundContent={
                        loadingGames ? (
                          <Spin size="small" />
                        ) : (
                          "No exercises available"
                        )
                      }
                    />
                  </div>
                </Col>

                <Col xs={24} md={8}>
                  <div style={{ marginBottom: 24 }}>
                    <Text
                      style={{
                        display: "block",
                        marginBottom: 8,
                        fontWeight: 500,
                        color: "#374151",
                      }}
                    >
                      Due Date
                    </Text>
                    <DatePicker
                      size="large"
                      style={{
                        width: "100%",
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                      }}
                      placeholder="Select due date"
                      value={gameDueDate ? dayjs(gameDueDate) : null}
                      onChange={(_, dateString) => {
                        if (typeof dateString === "string")
                          setGameDueDate(dateString);
                        else setGameDueDate("");
                      }}
                      disabledDate={(current) =>
                        current && current < dayjs().startOf("day")
                      }
                    />
                  </div>
                </Col>
                <Col xs={24} md={16}>
                  <div style={{ marginBottom: 0 }}>
                    <Text
                      style={{
                        display: "block",
                        marginBottom: 8,
                        fontWeight: 500,
                        color: "#374151",
                      }}
                    >
                      Notes <span style={{ color: "#ef4444" }}>*</span>
                    </Text>
                    <TextArea
                      placeholder="Enter assignment notes..."
                      value={gameNotes}
                      onChange={(e) => setGameNotes(e.target.value)}
                      rows={4}
                      style={{
                        width: "100%",
                        borderRadius: "12px",
                        resize: "none",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </div>
            {/* Action Buttons */}
            <div
              style={{
                display: "flex",
                gap: "16px",
                marginTop: "40px",
                paddingTop: "32px",
                justifyContent: "center",
                borderTop: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  borderColor: "transparent",
                  borderRadius: "12px",
                  height: "48px",
                  fontSize: "16px",
                  fontWeight: 600,
                  paddingLeft: "32px",
                  paddingRight: "32px",
                  boxShadow: "0 4px 12px rgba(102, 126, 234, 0.4)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-2px)";
                  target.style.boxShadow =
                    "0 6px 20px rgba(102, 126, 234, 0.6)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow =
                    "0 4px 12px rgba(102, 126, 234, 0.4)";
                }}
              >
                {loading ? "Creating User..." : "Create User"}
              </Button>
              <Button
                onClick={onReset}
                size="large"
                style={{
                  borderRadius: "12px",
                  height: "48px",
                  fontSize: "16px",
                  fontWeight: 500,
                  paddingLeft: "32px",
                  paddingRight: "32px",
                  border: "1px solid rgba(209, 213, 219, 0.8)",
                  background: "rgba(255, 255, 255, 0.9)",
                  color: "#6b7280",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "rgba(243, 244, 246, 0.9)";
                  target.style.borderColor = "#9ca3af";
                  target.style.color = "#374151";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "rgba(255, 255, 255, 0.9)";
                  target.style.borderColor = "rgba(209, 213, 219, 0.8)";
                  target.style.color = "#6b7280";
                }}
              >
                Reset Form
              </Button>
            </div>
          </Form>
        </Card>
      </Content>
    </Layout>
  );
}
