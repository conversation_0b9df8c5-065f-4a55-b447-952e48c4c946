import React from "react";
import { Table } from "antd";
import type { ColumnType } from "antd/es/table";

export interface CommonTableProps<T> {
  columns: ColumnType<T>[];
  dataSource: T[];
  loading?: boolean;
  rowKey: string;
  [key: string]: unknown;
}

export function CommonTable<T extends object>({ columns, dataSource, loading, rowKey, ...rest }: CommonTableProps<T>) {
  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      loading={loading}
      rowKey={rowKey}
      size="small"
      {...rest}
      style={{
        ...(typeof rest.style === "object" && rest.style !== null ? rest.style : {}),
        fontSize: 11,
        padding: 0,
        lineHeight: 1.1,
      }}
    />
  );
} 