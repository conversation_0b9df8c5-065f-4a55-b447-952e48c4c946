"use client";

import React, { useEffect, useState } from "react";
import { CommonTable } from "@components/common/Table";
import {
  Input,
  Button,
  Avatar,
  Card,
  Typography,
  Select,
  Space,
  Tag,
  Tooltip,
  message,
  Spin,
  Modal,
  Layout,
} from "antd";
import {
  UserOutlined,
  PlusOutlined,
  AppstoreOutlined,
  UserAddOutlined,
  EyeFilled,
  EditOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import { CAREGIVER, PLAYER } from "@utils/supabase/constants";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { RoleType, EditUserFormValues, PlayerStatisticsResponse } from "@types";
import AssignCaregiverDialog from "@components/dialog/assignCaregiverDialog";
import AssignGameDialog from "@components/dialog/gameListDialog";
import { EditUserModal } from "@components/modal/EditUserModal";

const { Text, Title } = Typography;
const { Content } = Layout;
const { Option } = Select;
const PAGE_SIZE = 7;

interface User {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
  avatar_url?: string;
  first_name: string;
  last_name: string;
  gender: string;
  bio: string;
  phone_number: string;
}

interface ApiUser {
  id: string;
  name: string;
  email: string;
  avatar_url?: string;
  gender: string;
  bio: string;
  phone_number: string;
}

export default function ViewUsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [roles, setRoles] = useState<RoleType[]>([]);
  const [usersData, setUsersData] = useState<User[]>([]);
  const [isAssignCaregiverVisible, setIsAssignCaregiverVisible] =
    useState(false);
  const [isAssigGameVisible, setIsAssignGameVisible] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<User | null>(null);
  const [isUserModalVisible, setIsUserModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [statistics, setStatistics] = useState<PlayerStatisticsResponse | null>(
    null
  );
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editLoading, setEditLoading] = useState(false);
  const fetchData = async () => {
    setLoading(true);
    try {
      const parent_party_id = localStorage.getItem("parent_party_id") || "";
      //const party_type_key = localStorage.getItem("role") || "";
      const tenant_code = localStorage.getItem("tenant_code") || "";
      const inputPara = {
        party_id: parent_party_id,
        tenant_code: tenant_code,
        party_type_key: "TENANTADM",
      };
      const [rolesResponse, usersResponse] = await Promise.all([
        dataProviderInstance.listRoles(),
        dataProviderInstance.listTenantUsers(inputPara),
      ]);

      setRoles(rolesResponse.data);

      const users = [
        ...(usersResponse.data.data.caregivers || []).map((u: ApiUser) => ({
          id: u.id,
          name: u.name,
          role: CAREGIVER,
          email: u.email,
          avatar: u.avatar_url,
          gender: u.gender,
          bio: u.bio,
          phone_number: u.phone_number,
        })),
        ...(usersResponse.data.data.players || []).map((u: ApiUser) => ({
          id: u.id,
          name: u.name,
          role: PLAYER,
          email: u.email,
          avatar: u.avatar_url,
          gender: u.gender,
          bio: u.bio,
          phone_number: u.phone_number,
        })),
      ];
      setUsersData(users);
    } catch (error) {
      console.error(error);
      message.error(ERROR_MESSAGES.load_games);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, []);
  const handleAssignCaregiver = (player: User) => {
    setSelectedPlayer(player);
    setIsAssignCaregiverVisible(true);
  };
  const handleAssignGame = (player: User) => {
    setSelectedPlayer(player);
    setIsAssignGameVisible(true);
  };
  const handleViewUser = (user: User) => {
    const tenant_code = localStorage.getItem("tenant_code") || "";

    dataProviderInstance
      .getPlayerStatistics({
        party_id: user.id,
        tenant_code: tenant_code,
      })
      .then((response) => {
        setStatistics(response.data); // Save API response
        setSelectedUser(user); // Save selected user
        setIsUserModalVisible(true); // Show modal
      })
      .catch((error) => {
        console.error("Error fetching user info:", error);
      });
  };

  const handleEditUser = (user: User) => {
    console.log("edit user:", user);
    setEditingUser(user);
    setIsEditModalVisible(true);
  };

  const handleDeleteUser = (user: User) => {
    Modal.confirm({
      title: "Delete User",
      content: `Are you sure you want to delete ${user.name}? This action cannot be undone.`,
      okText: "Yes, Delete",
      okType: "danger",
      cancelText: "Cancel",
      width: 800,
      onOk: async () => {
        try {
          setLoading(true);

          const response = await dataProviderInstance.deleteUser({
            user_id: user.id,
          });

          if (response.data && response.data.status === "success") {
            message.success("User deleted successfully");

            fetchData();
          } else if (response.data && response.data.status === "error") {
            message.error(response.data.message || ERROR_MESSAGES.error);
          } else {
            message.error(ERROR_MESSAGES.error);
          }
        } catch (error) {
          console.error("Error deleting user:", error);
          message.error(ERROR_MESSAGES.error);
        } finally {
          setLoading(false);
        }
      },
    });
  };
  const handleEditSubmit = async (values: EditUserFormValues) => {
    if (!editingUser) return;
    console.log(values);

    try {
      setEditLoading(true);
      const updateParams = {
        user_id: editingUser.id.toString(),
        avatar_url: values.avatar_url || editingUser.avatar || "",
        first_name: values.firstName,
        last_name: values.lastName,
        gender: values.gender,
        email: values.email,
        bio: values.bio,
        phone_number: values.phone,
      };
      await dataProviderInstance.updateProfileInfo(updateParams);
      message.success(`${editingUser.name} has been updated successfully`);

      handleEditModalClose();
      // Refresh the users list
      fetchData();
    } catch (error) {
      console.error("Error updating user:", error);
      message.error("Failed to update user");
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditModalClose = () => {
    setIsEditModalVisible(false);
    setEditingUser(null);
  };

  const filteredUsers = usersData.filter((user) => {
    const matchesRole =
      roleFilter === "All" ||
      user.role.toLowerCase() === roleFilter.toLowerCase();
    const matchesSearch =
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.toString().includes(searchTerm);
    return matchesRole && matchesSearch;
  });
  const userColumns = [
    {
      title: "SI No",
      key: "siNo",
      width: 80,
      render: (_: unknown, __: unknown, index: number) => (
        <Text style={{ fontWeight: 500, textAlign: "center" }}>
          {(currentPage - 1) * PAGE_SIZE + index + 1}
        </Text>
      ),
    },
    {
      title: "",
      dataIndex: "avatar",
      key: "avatar_url",
      width: 80,
      align: "center" as const,
      render: (_: unknown, record: User) => (
        <Avatar
          src={record.avatar}
          icon={<UserOutlined />}
          style={{ backgroundColor: "#6366f1" }}
          size={40}
        />
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text style={{ fontWeight: 500 }}>{text}</Text>,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      render: (text: string) => <Text style={{ color: "#666" }}>{text}</Text>,
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      render: (role: string) => (
        <Tag color={role === "Caretaker" ? "blue" : "green"}>
          {role.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      width: 400,
      align: "center" as const,
      render: (_: unknown, record: User) => (
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Space size="small">
            <Tooltip title="View User Profile">
              <Button
                type="primary"
                size="small"
                icon={<EyeFilled />}
                style={{
                  background:
                    "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  border: "none",
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(16, 185, 129, 0.3)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-1px)";
                  target.style.boxShadow = "0 4px 12px rgba(16, 185, 129, 0.4)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow = "0 2px 8px rgba(16, 185, 129, 0.3)";
                }}
                onClick={() =>
                  handleViewUser({ ...record, id: record.id.toString() })
                }
              />
            </Tooltip>
            <Tooltip title="Edit User">
              <Button
                type="default"
                size="small"
                icon={<EditOutlined />}
                style={{
                  backgroundColor: "#fff7e6",
                  color: "#fa8c16",
                  border: "1px solid #ffd591",
                }}
                onClick={() =>
                  handleEditUser({ ...record, id: record.id.toString() })
                }
              />
            </Tooltip>
            <Tooltip title="Delete User">
              <Button
                type="default"
                size="small"
                icon={<DeleteOutlined />}
                style={{
                  backgroundColor: "#fff2f0",
                  color: "#ff4d4f",
                  border: "1px solid #ffccc7",
                }}
                onClick={() =>
                  handleDeleteUser({ ...record, id: record.id.toString() })
                }
              />
            </Tooltip>
            <Tooltip title="Assign a Excersise to this User">
              <Button
                type="default"
                size="small"
                icon={<AppstoreOutlined />}
                style={{
                  background:
                    "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)",
                  color: "#3b82f6",
                  border: "1px solid rgba(59, 130, 246, 0.3)",
                  borderRadius: "8px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#3b82f6";
                  target.style.color = "white";
                  target.style.transform = "translateY(-1px)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background =
                    "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)";
                  target.style.color = "#3b82f6";
                  target.style.transform = "translateY(0)";
                }}
                onClick={() => handleAssignGame(record)}
              />
            </Tooltip>
            <Tooltip
              title={
                record.role === PLAYER
                  ? "Assign a Caretaker to this Player"
                  : undefined
              }
            >
              <Button
                type="default"
                size="small"
                icon={<UserAddOutlined />}
                style={{
                  visibility: record.role === PLAYER ? "visible" : "hidden",
                  background:
                    "linear-gradient(135deg, rgba(245, 124, 0, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%)",
                  color: "#f59e0b",
                  border: "1px solid rgba(245, 158, 11, 0.3)",
                  borderRadius: "8px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#f59e0b";
                  target.style.color = "white";
                  target.style.transform = "translateY(-1px)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background =
                    "linear-gradient(135deg, rgba(245, 124, 0, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%)";
                  target.style.color = "#f59e0b";
                  target.style.transform = "translateY(0)";
                }}
                onClick={() => handleAssignCaregiver(record)}
              />
            </Tooltip>
          </Space>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <Layout
        style={{
          maxWidth: 1280,
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          margin: "0 auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background decorative elements */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "300px",
            background:
              "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
            borderRadius: "0 0 50% 50%",
            transform: "scale(1.5)",
            zIndex: 0,
          }}
        />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
            width: "100%",
            zIndex: 1,
            position: "relative",
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              padding: "40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "20px",
            }}
          >
            <Spin size="large" />
            <Text
              style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}
            >
              Loading users...
            </Text>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "48px",
            maxWidth: "800px",
          }}
        >
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            User Management
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Manage and monitor all users in your tenant organization
          </Text>
        </div>

        {/* Main Content Container */}
        <div
          style={{
            width: "100%",
            maxWidth: "1200px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            padding: "32px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          {/* Search and Filter Section */}
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "32px",
              gap: 16,
              background: "rgba(255, 255, 255, 0.6)",
              backdropFilter: "blur(10px)",
              borderRadius: "16px",
              padding: "24px",
              boxShadow: "0 4px 16px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
            }}
          >
            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                gap: "16px",
                width: "100%",
                maxWidth: 600,
              }}
            >
              <Input.Search
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: "250px",
                  borderRadius: "12px",
                  minWidth: 120,
                  flex: 1,
                }}
                size="large"
              />
              <Select
                value={roleFilter}
                onChange={(value) => setRoleFilter(value)}
                style={{
                  width: 140,
                  borderRadius: "12px",
                  minWidth: 100,
                }}
                size="large"
              >
                <Option value="All">All Roles</Option>
                {roles.map((role: RoleType) => (
                  <Option key={role.role_name} value={role.role_name}>
                    {role.role_name}
                  </Option>
                ))}
              </Select>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                borderRadius: "12px",
                height: "48px",
                paddingLeft: "24px",
                paddingRight: "24px",
                fontSize: "16px",
                fontWeight: 600,
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                transition: "all 0.3s ease",
                minWidth: 140,
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(-2px)";
                target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(0)";
                target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
              }}
              onClick={() => {
                router.push("/tenants/addPlayer");
              }}
            >
              Add User
            </Button>
          </div>

          {/* Users Table */}
          <Card
            style={{
              borderRadius: "20px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              overflow: "hidden",
            }}
            styles={{ body: { padding: 0 } }}
          >
            <div
              style={{
                minWidth: 600,
                background: "transparent",
              }}
            >
              <CommonTable
                key={currentPage}
                columns={userColumns}
                dataSource={filteredUsers}
                loading={false}
                rowKey="id"
                pagination={{
                  pageSize: PAGE_SIZE,
                  current: currentPage,
                  onChange: (page: number) => setCurrentPage(page),
                  showSizeChanger: false,
                  showQuickJumper: false,
                  showTotal: (total: number, range: [number, number]) =>
                    `${range[0]}-${range[1]} of ${total} items`,
                  style: {
                    padding: "16px 24px",
                    background: "rgba(248, 250, 252, 0.8)",
                    borderTop: "1px solid rgba(226, 232, 240, 0.5)",
                  },
                }}
                bordered={false}
                style={{
                  background: "transparent",
                }}
              />
            </div>
          </Card>
          <EditUserModal
            visible={isEditModalVisible}
            user={
              editingUser
                ? { ...editingUser, id: Number(editingUser.id) }
                : null
            }
            roles={roles}
            loading={editLoading}
            onCancel={handleEditModalClose}
            onSubmit={handleEditSubmit}
          />
        </div>
      </Content>
      {/* Dialogs and Modals */}
      <AssignCaregiverDialog
        open={isAssignCaregiverVisible}
        onClose={() => setIsAssignCaregiverVisible(false)}
        player={
          selectedPlayer
            ? { id: selectedPlayer.id, name: selectedPlayer.name }
            : null
        }
      />
      <AssignGameDialog
        visible={isAssigGameVisible}
        onClose={() => setIsAssignGameVisible(false)}
        player={
          selectedPlayer
            ? { id: selectedPlayer.id, name: selectedPlayer.name }
            : null
        }
      />
      <Modal
        title={
          <Text
            style={{
              fontSize: "20px",
              fontWeight: 600,
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            User Details
          </Text>
        }
        open={isUserModalVisible}
        onCancel={() => {
          setIsUserModalVisible(false);
          setStatistics(null);
          setSelectedUser(null);
        }}
        footer={null}
        style={{ borderRadius: "16px" }}
        styles={{
          content: {
            borderRadius: "16px",
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(10px)",
          },
        }}
      >
        {selectedUser && (
          <div
            style={{
              textAlign: "center",
              marginTop: 16,
              padding: "24px",
              background:
                "linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)",
              borderRadius: "12px",
            }}
          >
            <Avatar
              size={80}
              src={selectedUser.avatar}
              icon={<UserOutlined />}
              style={{
                backgroundColor: "#667eea",
                marginBottom: 24,
                boxShadow: "0 4px 16px rgba(102, 126, 234, 0.3)",
              }}
            />
            <div style={{ textAlign: "left", marginBottom: 16 }}>
              <Text strong style={{ fontSize: 16, color: "#374151" }}>
                Name:{" "}
              </Text>
              <Text style={{ fontSize: 16, color: "#6b7280" }}>
                {selectedUser.name}
              </Text>
            </div>
            <div style={{ textAlign: "left", marginBottom: 16 }}>
              <Text strong style={{ fontSize: 16, color: "#374151" }}>
                Email:{" "}
              </Text>
              <Text style={{ fontSize: 16, color: "#6b7280" }}>
                {selectedUser.email}
              </Text>
            </div>
            <div style={{ textAlign: "left", marginBottom: 16 }}>
              <Text strong style={{ fontSize: 16, color: "#374151" }}>
                Role:{" "}
              </Text>
              <Tag
                color={selectedUser.role === "Caretaker" ? "blue" : "green"}
                style={{
                  borderRadius: 8,
                  padding: "4px 12px",
                  fontSize: 14,
                  fontWeight: 600,
                }}
              >
                {selectedUser.role.toUpperCase()}
              </Tag>
            </div>
            {statistics?.game_statistics?.length ? (
              <div style={{ marginTop: 24, textAlign: "left" }}>
                <Text strong style={{ fontSize: 18, color: "#374151" }}>
                  Game Statistics
                </Text>
                {statistics.game_statistics.map((game, idx) => (
                  <div
                    key={idx}
                    style={{
                      marginTop: 16,
                      padding: 16,
                      backgroundColor: "#f9fafb",
                      borderRadius: 8,
                    }}
                  >
                    <p>
                      <strong>Game:</strong> {game.game_name?.trim()}
                    </p>
                    <p>
                      <strong>Status:</strong> {game.session_status}
                    </p>
                    <p>
                      <strong>Score:</strong> {game.score ?? "N/A"}
                    </p>
                    <p>
                      <strong>Device:</strong> {game.device_info ?? "N/A"}
                    </p>
                    <p>
                      <strong>Started At:</strong>{" "}
                      {new Date(game.started_at).toLocaleString()}
                    </p>
                    <p>
                      <strong>Ended At:</strong>{" "}
                      {new Date(game.ended_at).toLocaleString()}
                    </p>
                    {/* Levels */}
                    {Array.isArray(game.levels) && game.levels.length > 0 && (
                      <div style={{ marginTop: 12 }}>
                        <strong>Levels:</strong>
                        <ul style={{ paddingLeft: 16, marginTop: 4 }}>
                          {game.levels.map((level, i) => (
                            <li key={i} style={{ marginBottom: 8 }}>
                              <p>
                                <strong>Title:</strong> {level.title}
                              </p>
                              <p>
                                <strong>Badge:</strong> {level.badge_name} –{" "}
                                {level.badge_description}
                              </p>
                              <p>
                                <strong>Completed:</strong>{" "}
                                {level.completed ? "Yes" : "No"}
                              </p>
                              <p>
                                <strong>Target Score:</strong>{" "}
                                {level.target_score}
                              </p>
                              <p>
                                <strong>Time Limit:</strong> {level.time_limit}{" "}
                                seconds
                              </p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Rewards */}
                    {Array.isArray(game.rewards) && game.rewards.length > 0 && (
                      <div style={{ marginTop: 12 }}>
                        <strong>Rewards:</strong>
                        <ul style={{ paddingLeft: 16 }}>
                          {game.rewards.map((reward, i) => (
                            <li key={i}>
                              {reward.reward_name} – {reward.points_awarded} pts
                              <br />
                              <em>{reward.reward_description}</em>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p>No statistics available</p>
            )}
          </div>
        )}
      </Modal>

      {/* CSS Animations and Responsive Styles */}
      <style jsx global>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .ant-table-thead > tr > th {
          background: rgba(248, 250, 252, 0.8) !important;
          border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
          font-weight: 600 !important;
          color: #374151 !important;
        }

        .ant-table-tbody > tr:hover > td {
          background: rgba(102, 126, 234, 0.05) !important;
        }

        .ant-table-tbody > tr > td {
          border-bottom: 1px solid rgba(226, 232, 240, 0.3) !important;
        }

        @media (max-width: 768px) {
          .ant-card {
            padding: 0 !important;
          }
          .ant-table-wrapper {
            overflow-x: auto;
          }
          .ant-table {
            min-width: 600px;
          }
          .ant-modal {
            width: 95vw !important;
            max-width: 95vw !important;
          }
        }
        @media (max-width: 600px) {
          .ant-card {
            padding: 0 !important;
          }
          .ant-table {
            min-width: 500px;
          }
          .ant-modal {
            width: 99vw !important;
            max-width: 99vw !important;
          }
        }
      `}</style>
    </Layout>
  );
}
