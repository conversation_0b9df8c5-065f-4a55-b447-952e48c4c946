"use client";

import React, { useEffect, useState } from "react";
import { CommonTable } from "@components/common-table";
import {
  Input,
  Button,
  Avatar,
  Card,
  Typography,
  Select,
  Space,
  Tag,
  Tooltip,
  message,
  Spin,
  Modal,
} from "antd";
import {
  UserOutlined,
  PlusOutlined,
  AppstoreOutlined,
  UserAddOutlined,
  EyeFilled,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import { CAREGIVER, PLAYER } from "@utils/supabase/constants";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { RoleType } from "@types";
import AssignCaregiverDialog from "@components/dialog/assignCaregiverDialog";
import AssignGameDialog from "@components/dialog/gameListDialog";

const { Text } = Typography;
const { Option } = Select;
const PAGE_SIZE = 7;

interface User {
  id: number;
  name: string;
  role: string;
  email: string;
  avatar?: string;
}

export default function ViewUsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [roles, setRoles] = useState<RoleType[]>([]);
  const [usersData, setUsersData] = useState<User[]>([]);
  const [isAssignCaregiverVisible, setIsAssignCaregiverVisible] =
    useState(false);
  const [isAssigGameVisible, setIsAssignGameVisible] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<User | null>(null);
  const [isUserModalVisible, setIsUserModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const handleAssignCaregiver = (player: User) => {
    setSelectedPlayer(player);
    setIsAssignCaregiverVisible(true);
  };
  const handleAssignGame = (player: User) => {
    setSelectedPlayer(player);
    setIsAssignGameVisible(true);
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsUserModalVisible(true);
  };

  const filteredUsers = usersData.filter((user) => {
    const matchesRole =
      roleFilter === "All" ||
      user.role.toLowerCase() === roleFilter.toLowerCase();
    const matchesSearch =
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.toString().includes(searchTerm);
    return matchesRole && matchesSearch;
  });
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const parent_party_id = localStorage.getItem("parent_party_id") || "";
        //const party_type_key = localStorage.getItem("role") || "";
        const tenant_code = localStorage.getItem("tenant_code") || "";
        const inputPara = {
          party_id: parent_party_id,
          tenant_code: tenant_code,
          party_type_key: "TENANTADM",
        };
        const [rolesResponse, usersResponse] = await Promise.all([
          dataProviderInstance.listRoles(),
          dataProviderInstance.listTenantUsers(inputPara),
        ]);

        setRoles(rolesResponse.data);

        const users = [
          ...(usersResponse.data.data.caregivers || []).map(
            (u: {
              id: number;
              name: string;
              email: string;
              avatar_url?: string;
            }) => ({
              id: u.id,
              name: u.name,
              role: CAREGIVER,
              email: u.email,
              avatar: u.avatar_url || "",
            })
          ),
          ...(usersResponse.data.data.players || []).map(
            (u: {
              id: number;
              name: string;
              email: string;
              avatar_url?: string;
            }) => ({
              id: u.id,
              name: u.name,
              role: PLAYER,
              email: u.email,
              avatar: u.avatar_url || "",
            })
          ),
        ];
        setUsersData(users);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  const userColumns = [
    {
      title: "SI No",
      key: "siNo",
      width: 80,
      render: (_: unknown, __: unknown, index: number) => (
        <Text style={{ fontWeight: 500, textAlign: "center" }}>
          {(currentPage - 1) * PAGE_SIZE + index + 1}
        </Text>
      ),
    },
    {
      title: "",
      dataIndex: "avatar_url",
      key: "avatar_url",
      width: 80,
      align: "center" as const,
      render: (
        _: unknown,
        record: {
          id: number;
          name: string;
          email: string;
          avatar?: string;
          role: string;
        }
      ) => (
        <Avatar
          src={record.avatar}
          icon={<UserOutlined />}
          style={{ backgroundColor: "#6366f1" }}
          size={40}
        />
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text style={{ fontWeight: 500 }}>{text}</Text>,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      render: (text: string) => <Text style={{ color: "#666" }}>{text}</Text>,
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      render: (role: string) => (
        <Tag color={role === "Caretaker" ? "blue" : "green"}>
          {role.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      width: 300,
      align: "center" as const,
      render: (
        _: unknown,
        record: {
          id: number;
          name: string;
          email: string;
          avatar?: string;
          role: string;
        }
      ) => (
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Space size="small">
            <Tooltip title="View User Profile">
              <Button
                type="primary"
                size="small"
                icon={<EyeFilled />}
                style={{
                  backgroundColor: "#00b894",
                  color: "white",
                  border: "none",
                }}
                onClick={() => handleViewUser(record)}
              />
            </Tooltip>
            <Tooltip title="Assign a Game to this User">
              <Button
                type="default"
                size="small"
                icon={<AppstoreOutlined />}
                style={{
                  backgroundColor: "#e0f7fa",
                  color: "#00796b",
                  border: "1px solid #b2ebf2",
                }}
                onClick={() => handleAssignGame(record)}
              />
            </Tooltip>
            <Tooltip
              title={
                record.role === PLAYER
                  ? "Assign a Caretaker to this Player"
                  : undefined
              }
            >
              <Button
                type="default"
                size="small"
                icon={<UserAddOutlined />}
                style={{
                  visibility: record.role === PLAYER ? "visible" : "hidden",
                  backgroundColor: "#fff3e0",
                  color: "#f57c00",
                  border: "1px solid #ffe0b2",
                }}
                onClick={() => handleAssignCaregiver(record)}
              ></Button>
            </Tooltip>
          </Space>
        </div>
      ),
    },
  ];

  return (
    <Spin spinning={loading} tip="Loading users...">
      <div
        style={{
          maxWidth: 1280,
          margin: "24px auto 0",
          background: "#fff",
          borderRadius: 8,
          padding: 24,
          boxSizing: "border-box",
        }}
      >
        {/* Header Section */}
        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "24px",
            gap: 16,
          }}
        >
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              gap: "16px",
              width: "100%",
              maxWidth: 600,
            }}
          >
            <Input.Search
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: "200px",
                borderRadius: "4px",
                minWidth: 120,
                flex: 1,
              }}
            />
            <Select
              value={roleFilter}
              onChange={(value) => setRoleFilter(value)}
              style={{ width: 120, borderRadius: "4px", minWidth: 100 }}
            >
              <Option value="All">All Roles</Option>
              {roles.map((role: RoleType) => (
                <Option key={role.role_name} value={role.role_name}>
                  {role.role_name}
                </Option>
              ))}
            </Select>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{
              backgroundColor: "#6366f1",
              borderColor: "#6366f1",
              borderRadius: "15px",
              marginTop: 8,
              minWidth: 120,
              width: "fit-content",
            }}
            onClick={() => {
              router.push("/tenants/addPlayer");
            }}
          >
            Add User
          </Button>
        </div>

        {/* Users Table */}
        <Card
          style={{
            borderRadius: "8px",
            border: "1px solid #f0f0f0",
            overflowX: "auto",
          }}
          bodyStyle={{ padding: 0 }}
        >
          <div style={{ minWidth: 600 }}>
            <CommonTable
              key={currentPage}
              columns={userColumns}
              dataSource={filteredUsers}
              loading={false}
              rowKey="id"
              pagination={{
                pageSize: PAGE_SIZE,
                current: currentPage,
                onChange: (page: number) => setCurrentPage(page),
                showSizeChanger: false,
                showQuickJumper: false,
                showTotal: (total: number, range: [number, number]) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }}
              bordered={false}
            />
          </div>
        </Card>
        <AssignCaregiverDialog
          open={isAssignCaregiverVisible}
          onClose={() => setIsAssignCaregiverVisible(false)}
          player={selectedPlayer}
        />
        <AssignGameDialog
          visible={isAssigGameVisible}
          onClose={() => setIsAssignGameVisible(false)}
          player={selectedPlayer}
        />
        <Modal
          title="User Details"
          open={isUserModalVisible}
          onCancel={() => setIsUserModalVisible(false)}
          footer={null}
        >
          {selectedUser && (
            <div style={{ textAlign: "left", marginTop: 16 }}>
              <Avatar
                size={80}
                src={selectedUser.avatar}
                icon={<UserOutlined />}
                style={{ backgroundColor: "#6366f1", marginBottom: 16 }}
              />
              <div style={{ marginBottom: 8 }}>
                <Text strong>Name: </Text>
                <Text>{selectedUser.name}</Text>
              </div>
              <div style={{ marginBottom: 8 }}>
                <Text strong>Email: </Text>
                <Text>{selectedUser.email}</Text>
              </div>
              <div>
                <Text strong>Role: </Text>
                <Tag
                  color={selectedUser.role === "Caretaker" ? "blue" : "green"}
                >
                  {selectedUser.role.toUpperCase()}
                </Tag>
              </div>
            </div>
          )}
        </Modal>
      </div>
      <style jsx global>{`
        @media (max-width: 768px) {
          .ant-card {
            padding: 0 !important;
          }
          .ant-table-wrapper {
            overflow-x: auto;
          }
          .ant-table {
            min-width: 600px;
          }
          .ant-modal {
            width: 95vw !important;
            max-width: 95vw !important;
          }
        }
        @media (max-width: 600px) {
          .ant-card {
            padding: 0 !important;
          }
          .ant-table {
            min-width: 500px;
          }
          .ant-modal {
            width: 99vw !important;
            max-width: 99vw !important;
          }
        }
      `}</style>
    </Spin>
  );
}
