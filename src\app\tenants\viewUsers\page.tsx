"use client";

import React, { useEffect, useState } from "react";
import { CommonTable } from "@components/common-table";
import {
  Input,
  Button,
  Avatar,
  Card,
  Typography,
  Select,
  Space,
  Tag,
  Tooltip,
  message,
  Spin,
  Modal,
  Layout,
} from "antd";
import {
  UserOutlined,
  PlusOutlined,
  AppstoreOutlined,
  UserAddOutlined,
  EyeFilled,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import { CAREGIVER, PLAYER } from "@utils/supabase/constants";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { RoleType } from "@types";
import AssignCaregiverDialog from "@components/dialog/assignCaregiverDialog";
import AssignGameDialog from "@components/dialog/gameListDialog";

const { Text, Title } = Typography;
const { Option } = Select;
const { Content } = Layout;
const PAGE_SIZE = 7;

interface User {
  id: number;
  name: string;
  role: string;
  email: string;
  avatar?: string;
}

export default function ViewUsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [roles, setRoles] = useState<RoleType[]>([]);
  const [usersData, setUsersData] = useState<User[]>([]);
  const [isAssignCaregiverVisible, setIsAssignCaregiverVisible] =
    useState(false);
  const [isAssigGameVisible, setIsAssignGameVisible] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<User | null>(null);
  const [isUserModalVisible, setIsUserModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const handleAssignCaregiver = (player: User) => {
    setSelectedPlayer(player);
    setIsAssignCaregiverVisible(true);
  };
  const handleAssignGame = (player: User) => {
    setSelectedPlayer(player);
    setIsAssignGameVisible(true);
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsUserModalVisible(true);
  };

  const filteredUsers = usersData.filter((user) => {
    const matchesRole =
      roleFilter === "All" ||
      user.role.toLowerCase() === roleFilter.toLowerCase();
    const matchesSearch =
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.toString().includes(searchTerm);
    return matchesRole && matchesSearch;
  });
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const parent_party_id = localStorage.getItem("parent_party_id") || "";
        //const party_type_key = localStorage.getItem("role") || "";
        const tenant_code = localStorage.getItem("tenant_code") || "";
        const inputPara = {
          party_id: parent_party_id,
          tenant_code: tenant_code,
          party_type_key: "TENANTADM",
        };
        const [rolesResponse, usersResponse] = await Promise.all([
          dataProviderInstance.listRoles(),
          dataProviderInstance.listTenantUsers(inputPara),
        ]);

        setRoles(rolesResponse.data);

        const users = [
          ...(usersResponse.data.data.caregivers || []).map(
            (u: {
              id: number;
              name: string;
              email: string;
              avatar_url?: string;
            }) => ({
              id: u.id,
              name: u.name,
              role: CAREGIVER,
              email: u.email,
              avatar: u.avatar_url || "",
            })
          ),
          ...(usersResponse.data.data.players || []).map(
            (u: {
              id: number;
              name: string;
              email: string;
              avatar_url?: string;
            }) => ({
              id: u.id,
              name: u.name,
              role: PLAYER,
              email: u.email,
              avatar: u.avatar_url || "",
            })
          ),
        ];
        setUsersData(users);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  const userColumns = [
    {
      title: "SI No",
      key: "siNo",
      width: 80,
      render: (_: unknown, __: unknown, index: number) => (
        <Text style={{ fontWeight: 500, textAlign: "center" }}>
          {(currentPage - 1) * PAGE_SIZE + index + 1}
        </Text>
      ),
    },
    {
      title: "",
      dataIndex: "avatar_url",
      key: "avatar_url",
      width: 80,
      align: "center" as const,
      render: (
        _: unknown,
        record: {
          id: number;
          name: string;
          email: string;
          avatar?: string;
          role: string;
        }
      ) => (
        <Avatar
          src={record.avatar}
          icon={<UserOutlined />}
          style={{ backgroundColor: "#6366f1" }}
          size={40}
        />
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text style={{ fontWeight: 500 }}>{text}</Text>,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      render: (text: string) => <Text style={{ color: "#666" }}>{text}</Text>,
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      render: (role: string) => (
        <Tag
          style={{
            background: role === "caregiver"
              ? "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)"
              : "linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%)",
            color: role === "caregiver" ? "#3b82f6" : "#10b981",
            border: `1px solid ${role === "caregiver" ? "rgba(59, 130, 246, 0.3)" : "rgba(16, 185, 129, 0.3)"}`,
            borderRadius: "8px",
            fontWeight: 500,
            padding: "4px 12px",
          }}
        >
          {role.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      width: 300,
      align: "center" as const,
      render: (
        _: unknown,
        record: {
          id: number;
          name: string;
          email: string;
          avatar?: string;
          role: string;
        }
      ) => (
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Space size="small">
            <Tooltip title="View User Profile">
              <Button
                type="primary"
                size="small"
                icon={<EyeFilled />}
                style={{
                  background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  border: "none",
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(16, 185, 129, 0.3)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-1px)";
                  target.style.boxShadow = "0 4px 12px rgba(16, 185, 129, 0.4)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow = "0 2px 8px rgba(16, 185, 129, 0.3)";
                }}
                onClick={() => handleViewUser(record)}
              />
            </Tooltip>
            <Tooltip title="Assign a Game to this User">
              <Button
                type="default"
                size="small"
                icon={<AppstoreOutlined />}
                style={{
                  background: "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)",
                  color: "#3b82f6",
                  border: "1px solid rgba(59, 130, 246, 0.3)",
                  borderRadius: "8px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#3b82f6";
                  target.style.color = "white";
                  target.style.transform = "translateY(-1px)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)";
                  target.style.color = "#3b82f6";
                  target.style.transform = "translateY(0)";
                }}
                onClick={() => handleAssignGame(record)}
              />
            </Tooltip>
            <Tooltip
              title={
                record.role === PLAYER
                  ? "Assign a Caretaker to this Player"
                  : undefined
              }
            >
              <Button
                type="default"
                size="small"
                icon={<UserAddOutlined />}
                style={{
                  visibility: record.role === PLAYER ? "visible" : "hidden",
                  background: "linear-gradient(135deg, rgba(245, 124, 0, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%)",
                  color: "#f57c00",
                  border: "1px solid rgba(245, 124, 0, 0.3)",
                  borderRadius: "8px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#f57c00";
                  target.style.color = "white";
                  target.style.transform = "translateY(-1px)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "linear-gradient(135deg, rgba(245, 124, 0, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%)";
                  target.style.color = "#f57c00";
                  target.style.transform = "translateY(0)";
                }}
                onClick={() => handleAssignCaregiver(record)}
              ></Button>
            </Tooltip>
          </Space>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <Layout
        style={{
          maxWidth: 1280,
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          margin: "0 auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background decorative elements */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "300px",
            background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
            borderRadius: "0 0 50% 50%",
            transform: "scale(1.5)",
            zIndex: 0,
          }}
        />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
            width: "100%",
            zIndex: 1,
            position: "relative",
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              padding: "40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "20px",
            }}
          >
            <Spin size="large" />
            <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
              Loading users...
            </Text>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          maxWidth: "800px",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            User Management
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Manage your caregivers and players with advanced tools and insights
          </Text>
        </div>

        {/* Main Content Container */}
        <div
          style={{
            width: "100%",
            maxWidth: "1200px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            padding: "32px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          {/* Header Section */}
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "32px",
              gap: 16,
            }}
          >
            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                gap: "16px",
                width: "100%",
                maxWidth: 600,
              }}
            >
              <Input.Search
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: "200px",
                  borderRadius: "12px",
                  minWidth: 120,
                  flex: 1,
                  boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                  border: "1px solid rgba(255,255,255,0.3)",
                }}
              />
              <Select
                value={roleFilter}
                onChange={(value) => setRoleFilter(value)}
                style={{
                  width: 140,
                  borderRadius: "12px",
                  minWidth: 120,
                }}
              >
                <Option value="All">All Roles</Option>
                {roles.map((role: RoleType) => (
                  <Option key={role.role_name} value={role.role_name}>
                    {role.role_name}
                  </Option>
                ))}
              </Select>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                borderRadius: "12px",
                height: "40px",
                fontSize: "14px",
                fontWeight: 600,
                minWidth: 120,
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(-2px)";
                target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(0)";
                target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
              }}
              onClick={() => {
                router.push("/tenants/addPlayer");
              }}
            >
              Add User
            </Button>
        </div>

          {/* Users Table */}
          <Card
            style={{
              borderRadius: "20px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              overflow: "hidden",
            }}
            styles={{ body: { padding: 0 } }}
          >
            <div style={{ minWidth: 600 }}>
              <CommonTable
                key={currentPage}
                columns={userColumns}
                dataSource={filteredUsers}
                loading={false}
                rowKey="id"
                pagination={{
                  pageSize: PAGE_SIZE,
                  current: currentPage,
                  onChange: (page: number) => setCurrentPage(page),
                  showSizeChanger: false,
                  showQuickJumper: false,
                  showTotal: (total: number, range: [number, number]) =>
                    `${range[0]}-${range[1]} of ${total} items`,
                }}
                bordered={false}
              />
            </div>
          </Card>
        </div>
      </Content>

      <AssignCaregiverDialog
        open={isAssignCaregiverVisible}
        onClose={() => setIsAssignCaregiverVisible(false)}
        player={selectedPlayer}
      />
      <AssignGameDialog
        visible={isAssigGameVisible}
        onClose={() => setIsAssignGameVisible(false)}
        player={selectedPlayer}
      />
      <Modal
        title={
          <Text style={{
            fontSize: "18px",
            fontWeight: 600,
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}>
            User Details
          </Text>
        }
        open={isUserModalVisible}
        onCancel={() => setIsUserModalVisible(false)}
        footer={null}
        style={{
          borderRadius: "16px",
        }}
      >
        {selectedUser && (
          <div style={{
            textAlign: "center",
            marginTop: 16,
            padding: "20px",
            background: "linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)",
            borderRadius: "12px",
          }}>
            <Avatar
              size={80}
              src={selectedUser.avatar}
              icon={<UserOutlined />}
              style={{
                backgroundColor: "#6366f1",
                marginBottom: 20,
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.3)",
              }}
            />
            <div style={{ marginBottom: 12, textAlign: "left" }}>
              <Text strong style={{ color: "#374151" }}>Name: </Text>
              <Text style={{ color: "#6b7280" }}>{selectedUser.name}</Text>
            </div>
            <div style={{ marginBottom: 12, textAlign: "left" }}>
              <Text strong style={{ color: "#374151" }}>Email: </Text>
              <Text style={{ color: "#6b7280" }}>{selectedUser.email}</Text>
            </div>
            <div style={{ textAlign: "left" }}>
              <Text strong style={{ color: "#374151" }}>Role: </Text>
              <Tag
                color={selectedUser.role === "Caretaker" ? "blue" : "green"}
                style={{
                  borderRadius: "8px",
                  fontWeight: 500,
                  padding: "4px 12px",
                }}
              >
                {selectedUser.role.toUpperCase()}
              </Tag>
            </div>
          </div>
        )}
      </Modal>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .user-management-container {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>

      <style jsx global>{`
        @media (max-width: 768px) {
          .ant-card {
            padding: 0 !important;
          }
          .ant-table-wrapper {
            overflow-x: auto;
          }
          .ant-table {
            min-width: 600px;
          }
          .ant-modal {
            width: 95vw !important;
            max-width: 95vw !important;
          }
        }
        @media (max-width: 600px) {
          .ant-card {
            padding: 0 !important;
          }
          .ant-table {
            min-width: 500px;
          }
          .ant-modal {
            width: 99vw !important;
            max-width: 99vw !important;
          }
        }
      `}</style>
    </Layout>
  );
}
