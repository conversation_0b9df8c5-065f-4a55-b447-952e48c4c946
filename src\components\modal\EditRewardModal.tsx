import {
  <PERSON><PERSON>,
  But<PERSON>,
  Form,
  Input,
  Select,
  Typography,
  Row,
  Col,
  Card,
} from "antd";
import { GiftOutlined } from "@ant-design/icons";
import { useEffect } from "react";
import { RewardFormValues, RewardItem } from "@types";

const { Title, Text } = Typography;

interface EditRewardModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: RewardFormValues) => void | Promise<void>;
  reward: RewardItem | null;
  submitting: boolean;
  selectOptions: {
    label: string;
    value: string;
    name?: string;
    disabled?: boolean;
  }[];
}

export const EditRewardModal: React.FC<EditRewardModalProps> = ({
  visible,
  onCancel,
  onSubmit,
  reward,
  submitting,
  selectOptions,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (reward && selectOptions.length > 0) {
      let categoryValue = reward.category_id;

      const categoryById = selectOptions.find(
        (option) => option.value === reward.category_id
      );

      if (!categoryById) {
        const categoryByName = selectOptions.find(
          (option) =>
            option.name === reward.category_name ||
            option.label === reward.category_name
        );

        if (categoryByName) {
          categoryValue = categoryByName.value;
        }
      }

      form.setFieldsValue({
        name: reward.name,
        points: reward.points_required,
        category: categoryValue,
        activityType: reward.activity_type,
        description: reward.description,
      });

      console.log("Reward data:", {
        category_id: reward.category_id,
        category_name: reward.category_name,
        selectedValue: categoryValue,
      });
      console.log("Available selectOptions:", selectOptions);
    } else {
      form.resetFields();
    }
  }, [reward, form, selectOptions]);

  const inputStyle = {
    borderRadius: "12px",
    height: "48px",
    border: "2px solid #e5e7eb",
    fontSize: "16px",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
  };

  const focusStyle = {
    borderColor: "#667eea",
    boxShadow:
      "0 0 0 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1)",
  };

  const handleInputFocus = (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const target = e.target;
    Object.assign(target.style, focusStyle);
  };

  const handleInputBlur = (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const target = e.target;
    target.style.borderColor = "#e5e7eb";
    target.style.boxShadow = "0 1px 3px rgba(0, 0, 0, 0.1)";
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={900}
      centered
      styles={{
        content: {
          borderRadius: "20px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          padding: "2px",
          border: "none",
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
        },
        header: {
          borderBottom: "none",
          padding: "0",
          background: "transparent",
        },
        body: {
          padding: "0",
          background: "white",
          borderRadius: "18px",
          margin: "0",
        },
      }}
      destroyOnClose
    >
      <div
        style={{
          background: "white",
          borderRadius: "18px",
          overflow: "hidden",
          minHeight: "480px",
        }}
      >
        {/* Header */}
        <div
          style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            padding: "16px 24px",
            color: "white",
            display: "flex",
            alignItems: "center",
            gap: "12px",
          }}
        >
          <div
            style={{
              width: "36px",
              height: "36px",
              borderRadius: "10px",
              background: "rgba(255, 255, 255, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backdropFilter: "blur(10px)",
            }}
          >
            <GiftOutlined style={{ fontSize: "18px", color: "white" }} />
          </div>
          <div>
            <Title
              level={3}
              style={{
                margin: 0,
                color: "white",
                fontWeight: 700,
                fontSize: "28px",
              }}
            >
              Edit Reward
            </Title>
            <Text
              style={{
                color: "rgba(255, 255, 255, 0.8)",
                fontSize: "16px",
              }}
            >
              Update reward details and configuration
            </Text>
          </div>
        </div>

        {/* Content */}
        <div style={{ padding: "20px" }}>
          {/* Form Card */}
          <Card
            style={{
              borderRadius: "16px",
              border: "1px solid rgba(226, 232, 240, 0.3)",
              background: "rgba(255, 255, 255, 0.95)",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
            }}
            styles={{ body: { padding: "24px" } }}
          >
            <Form form={form} layout="vertical" onFinish={onSubmit}>
              {/* Form Fields */}
              <Row gutter={[20, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          marginBottom: "6px",
                        }}
                      >
                        Category
                      </span>
                    }
                    name="category"
                    rules={[
                      { required: true, message: "Please select a category" },
                    ]}
                  >
                    <Select
                      placeholder="Select category"
                      options={selectOptions}
                      style={{
                        ...inputStyle,
                      }}
                      size="large"
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          marginBottom: "6px",
                        }}
                      >
                        Reward Name
                      </span>
                    }
                    name="name"
                    rules={[
                      { required: true, message: "Reward name is required" },
                      { min: 3, message: "Name must be at least 3 characters" },
                      {
                        max: 100,
                        message: "Name must not exceed 100 characters",
                      },
                    ]}
                  >
                    <Input
                      placeholder="Enter reward name"
                      style={inputStyle}
                      onFocus={handleInputFocus}
                      onBlur={handleInputBlur}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          marginBottom: "6px",
                        }}
                      >
                        Points Required
                      </span>
                    }
                    name="points"
                    rules={[
                      { required: true, message: "Points are required" },
                      {
                        validator: (_, value) =>
                          value && value > 0
                            ? Promise.resolve()
                            : Promise.reject("Points must be greater than 0"),
                      },
                    ]}
                  >
                    <Input
                      placeholder="Enter points required (e.g. 2000)"
                      type="number"
                      min="1"
                      style={inputStyle}
                      onFocus={handleInputFocus}
                      onBlur={handleInputBlur}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} md={12}>
                  <Form.Item
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          marginBottom: "6px",
                        }}
                      >
                        Activity Type
                      </span>
                    }
                    name="activityType"
                    rules={[
                      { required: true, message: "Activity type is required" },
                      { min: 3, message: "At least 3 characters" },
                      { max: 50, message: "Max 50 characters" },
                    ]}
                  >
                    <Input
                      placeholder="e.g. Exercise Completion"
                      style={inputStyle}
                      onFocus={handleInputFocus}
                      onBlur={handleInputBlur}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    label={
                      <span
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          marginBottom: "6px",
                        }}
                      >
                        Description
                      </span>
                    }
                    name="description"
                  >
                    <Input.TextArea
                      placeholder="Describe the reward criteria and requirements..."
                      rows={3}
                      showCount
                      maxLength={500}
                      style={{
                        ...inputStyle,
                        height: "auto",
                        resize: "none",
                        padding: "12px 16px",
                      }}
                      onFocus={handleInputFocus}
                      onBlur={handleInputBlur}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Action Buttons */}
              <div
                style={{
                  marginTop: "24px",
                  padding: "20px",
                  background:
                    "linear-gradient(145deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.8))",
                  borderRadius: "12px",
                  border: "1px solid rgba(226, 232, 240, 0.3)",
                  textAlign: "center",
                }}
              >
                <Row gutter={12} justify="center">
                  <Col xs={24} sm={10} md={8}>
                    <Button
                      size="large"
                      style={{
                        width: "100%",
                        height: "42px",
                        borderRadius: "10px",
                        border: "2px solid #e5e7eb",
                        background: "white",
                        color: "#6b7280",
                        fontSize: "14px",
                        fontWeight: 600,
                        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
                      }}
                      onClick={onCancel}
                      disabled={submitting}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.borderColor = "#ef4444";
                        target.style.color = "#ef4444";
                        target.style.transform = "translateY(-1px)";
                        target.style.boxShadow =
                          "0 4px 12px rgba(239, 68, 68, 0.15)";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.color = "#6b7280";
                        target.style.transform = "translateY(0)";
                        target.style.boxShadow =
                          "0 2px 4px rgba(0, 0, 0, 0.05)";
                      }}
                    >
                      Cancel
                    </Button>
                  </Col>
                  <Col xs={24} sm={10} md={8}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                      size="large"
                      style={{
                        width: "100%",
                        height: "42px",
                        background:
                          "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        border: "none",
                        borderRadius: "10px",
                        fontSize: "14px",
                        fontWeight: 600,
                        boxShadow: "0 4px 16px rgba(102, 126, 234, 0.4)",
                        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      }}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(-2px)";
                        target.style.boxShadow =
                          "0 8px 24px rgba(102, 126, 234, 0.5)";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(0)";
                        target.style.boxShadow =
                          "0 4px 16px rgba(102, 126, 234, 0.4)";
                      }}
                    >
                      {submitting ? "Updating..." : "Update Reward"}
                    </Button>
                  </Col>
                </Row>
              </div>
            </Form>
          </Card>
        </div>
      </div>
    </Modal>
  );
};
