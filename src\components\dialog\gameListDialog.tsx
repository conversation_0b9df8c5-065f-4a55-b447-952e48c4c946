import React, { useState, useEffect } from "react";
import { Modal, Table, message, Spin } from "antd";
import { dataProviderInstance } from "@providers/data-provider";
// import { FetchGamesResponse, Game, ListTenantGamesRequest } from "@types";
import { Game } from "@types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
interface AssignCaregiverDialogProps {
  visible: boolean;
  onClose: () => void;
  player: { id: number; name: string } | null;
}

const AssignGameDialog: React.FC<AssignCaregiverDialogProps> = ({
  visible,
  onClose,
  player,
}) => {
  const [gameData, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    const fetchGames = async () => {
      try {
        setLoading(true);
        const para = {
          tenant_id: localStorage.getItem("tenant_id"),
          party_id: player?.id
        };
        console.log("request");
        console.log(para);
        // const response: FetchGamesResponse =
        //   await dataProviderInstance.listUnAssignedGames(
        //    tenant_id: tenant_id || "",
        // party_id: party_id,
        //   );
        const response = await dataProviderInstance.listUnAssignedGames({
          tenant_id: para.tenant_id || "",
          party_id: para.party_id ? String(para.party_id) : "",
        });

        console.log(response);

        setGames(response.data);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
        setGames([]);
      } finally {
        setLoading(false);
      }
    };
    if (visible) {
      fetchGames();
    }
  }, [visible]);
  useEffect(() => {
    if (!visible) {
      setSelectedRowKeys([]);
    }
  }, [visible]);

  const handleAssign = async () => {
    if (!player) return;
    if (selectedRowKeys.length === 0) {
      message.warning(ERROR_MESSAGES.select_game);
      return;
    }
    try {
      console.log("assign game", player);
      console.log("selectedRowKeys", selectedRowKeys);
      await dataProviderInstance.assignGame({
        // party_ids: [String(player.id)],
        // tenant_id: localStorage.getItem("tenant_id") || "",
        // game_ids: selectedRowKeys as string[],
        // due_date: null,

        due_date: null,
        game_ids: selectedRowKeys as string[],
        notes: null, // You must include this if your function expects it
        party_ids: [String(player.id)],
      });
      message.success(SUCCESS_MESSAGES.assign_games);
      onClose();
    } catch (error) {
      console.log(error);
      message.error(ERROR_MESSAGES.assign_games);
    } finally {
    }
  };

  const columns = [
    {
      title: "Image",
      dataIndex: "thumbnail_url",
      key: "thumbnail_url",
      width: 80,
      render: (url: string) => (
        <img
          src={url || "/assets/add-game.png"}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/assets/add-game.png";
          }}
          alt="thumbnail"
          style={{
            width: 32,
            height: 32,
            objectFit: "cover",
            // borderRadius: "50%",
          }}
        />
      ),
    },
    {
      title: "Game Name",
      dataIndex: "game_name",
      key: "game_name",
      render: (text: string) => (
        <span style={{ fontWeight: 600, fontSize: 16, color: "#222" }}>
          {text}
        </span>
      ),
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
      render: (text: string) => (
        <span style={{ color: "#888", fontSize: 15 }}>{text}</span>
      ),
    },
    {
      title: "No of levels",
      dataIndex: "no_of_levels",
      key: "no_of_levels",
      render: (text: string) => (
        <span style={{ color: "#888", fontSize: 15 }}>{text}</span>
      ),
    },
    {
      title: "Valid From",
      dataIndex: "valid_from",
      key: "valid_from",
      render: (valid_from: string) => (
        <span style={{ color: "#888", fontSize: 15 }}>
          {valid_from ? new Date(valid_from).toISOString().slice(0, 10) : "-"}
        </span>
      ),
    },
    {
      title: "Valid To",
      dataIndex: "valid_to",
      key: "valid_to",
      render: (valid_to: string) => (
        <span style={{ color: "#888", fontSize: 15 }}>
          {valid_to ? new Date(valid_to).toISOString().slice(0, 10) : "-"}
        </span>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
  };
  console.log("Game Data:", gameData);
  return (
    <Modal
      title="Assign Game"
      visible={visible}
      onCancel={onClose}
      onOk={handleAssign}
      okText="Assign"
      width={800}
    >
      <Spin spinning={loading}>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={gameData}
          // rowKey="id"
          rowKey={(record) => record.game_id}
          pagination={{ pageSize: 5 }}
        />
      </Spin>
    </Modal>
  );
};

export default AssignGameDialog;
