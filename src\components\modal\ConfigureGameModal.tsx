"use client";
import React, { useEffect, useState } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  InputNumber,
  Checkbox,
  message,
} from "antd";
import { supabaseClient } from "@utils/supabase/client";
import {
  CategoryItem,
  CategoryOption,
  GameConfigureFormData,
  GameConfigureModalData,
} from "@types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
import { dataProviderInstance } from "@providers/data-provider";
import RichTextEditor from "@components/RichTextEditor";
const { Text, Title } = Typography;

interface ConfigureGameModalProps {
  visible: boolean;
  onClose: () => void;
  gameId: string | null;
  gameData?: GameConfigureModalData;
  onSuccess?: () => void;
}

const WORD_RECALL_ID = "086becae-3131-4809-a145-e8a0cbe8b796";

const ConfigureGameModal: React.FC<ConfigureGameModalProps> = ({
  visible,
  gameId,
  onClose,
  gameData,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [words, setWords] = useState<string[]>([]);
  const [problems, setProblems] = useState<
    { question: string; answer: string | number }[]
  >([]);
  const [selectedCategory, setSelectedCategory] = useState<
    string | undefined
  >();
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [rewardOptions, setRewardOptions] = useState<
    { label: string; value: string; points: number }[]
  >([]);
  const [badgeOptions, setBadgeOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [loadingRewards, setLoadingRewards] = useState(true);
  const [loadingBadges, setLoadingBadges] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // Load categories and rewards
  useEffect(() => {
    setLoadingCategories(true);
    dataProviderInstance
      .listGameCategories()
      .then((response: { data: CategoryItem[] }) => {
        if (!response || !response.data)
          throw new Error("No category data received");
        const formatted: CategoryOption[] = response.data.map(
          (item: CategoryItem) => ({
            label: item.name,
            value: item.id,
          })
        );
        setCategoryOptions(formatted);
      })
      .catch(() => {
        message.error(ERROR_MESSAGES.load_categories);
        setCategoryOptions([]);
      })
      .finally(() => setLoadingCategories(false));

    setLoadingRewards(true);
    dataProviderInstance
      .listRewards()
      .then(
        (response: {
          data: { id: string; name: string; points_required: number }[];
        }) => {
          if (!response || !response.data)
            throw new Error("No rewards data received");
          setRewardOptions(
            response.data.map((reward) => ({
              label: reward.name,
              value: reward.id,
              points: reward.points_required,
            }))
          );
        }
      )
      .catch(() => {
        message.error("Failed to load rewards");
        setRewardOptions([]);
      })
      .finally(() => setLoadingRewards(false));
    setLoadingBadges(true);
    dataProviderInstance
      .listBadges()
      .then((response: { data: { id: string; name: string }[] }) => {
        if (!response || !response.data)
          throw new Error("No badge data received");
        setBadgeOptions(
          response.data.map((reward) => ({
            label: reward.name,
            value: reward.id,
          }))
        );
      })
      .catch(() => {
        message.error("Failed to load rewards");
        setBadgeOptions([]);
      })
      .finally(() => setLoadingBadges(false));
  }, []);

  useEffect(() => {
    if (form.getFieldValue("reward_id") && rewardOptions.length > 0) {
      const selectedReward = rewardOptions.find(
        (r) => r.value === form.getFieldValue("reward_id")
      );
      if (selectedReward) {
        form.setFieldsValue({ points: selectedReward.points });
      }
    }
  }, [form, rewardOptions, form.getFieldValue("reward_id")]);

  // Prefill form when modal opens and gameData is available
  useEffect(() => {
    if (visible && gameData && categoryOptions.length > 0) {
      let categoryValue = gameData.category_id;
      // Fallback: if category_id is missing, map from name
      if (!categoryValue && gameData.category) {
        const match = categoryOptions.find(
          (opt) => opt.label === gameData.category
        );
        if (match) categoryValue = match.value;
      }
      form.setFieldsValue({
        title: gameData.title,
        category: categoryValue,
        level: gameData.level,
        timer: gameData.timer,
        instructions: gameData.instructions,
      });
      setSelectedCategory(categoryValue);
    } else if (!visible) {
      form.resetFields();
      setWords([]);
      setProblems([]);
      setSelectedCategory(undefined);
    }
  }, [visible, gameData, form, categoryOptions]);

  // Handle category change
  // const handleCategoryChange = (value: string) => {
  //   console.log("category value:", value);
  //   setSelectedCategory(value);
  //   setWords([]);
  //   setProblems([]);
  //   form.setFieldsValue({ assets: undefined });
  // };

  // Problem helpers
  const handleProblemChange = (
    index: number,
    field: "question" | "answer",
    value: string | number
  ) => {
    const updated = [...problems];
    updated[index] = { ...updated[index], [field]: value };
    setProblems(updated);
  };

  const addProblem = () =>
    setProblems([...problems, { question: "", answer: "" }]);
  const removeProblem = (index: number) =>
    setProblems(problems.filter((_, i) => i !== index));

  // Submit handler
  const handleFinish = async (values: GameConfigureFormData) => {
    console.log("game data in modal:", gameId);
    console.log("game data in modal2:", gameData);
    setSubmitting(true);
    const payload = {
      //title: values.title,
      // category_id: values.category,
      game_level_id: gameData?.levelData?.level_id,
      //timer: values.timer,
      //instructions: values.instructions,
      reward_id: values.reward_id,
      assets: selectedCategory === WORD_RECALL_ID ? { words } : { problems },
      logic: {
        min_correct: values.min_correct,
        next_game_unlocked: values.next_game_unlocked,
      },
      rewards: {
        points: values.points,
        badge_id: values.badge_id,
      },
      game_id: gameId, // Pass game_id if needed for update
    };
    console.log("game data in modal:", payload);
    console.log("game data in modal:", gameId);
    try {
      console.log("Submitting:", payload);
      const { error } = await supabaseClient.rpc(
        "fn_update_game_config",
        payload
      );
      if (error) throw error;
      message.success(SUCCESS_MESSAGES.configure_game);
      onSuccess?.();
      onClose();
      form.resetFields();
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to configure game logic";
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      centered
      destroyOnClose
      title={null}
      bodyStyle={{ padding: 0, borderRadius: 24, background: "transparent" }}
      style={{ borderRadius: 24 }}
    >
      <div
        style={{
          background: "rgba(255, 255, 255, 0.95)",
          borderRadius: "24px",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          padding: 24,
        }}
      >
        <div
          style={{
            textAlign: "center",
            marginBottom: "32px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            padding: "20px 20px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          <Title
            level={2}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              margin: 0,
              fontSize: "30px",
              letterSpacing: "-1px",
            }}
          >
            Configure Game Logic
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "16px",
              fontWeight: 500,
              display: "block",
              marginBottom: "0",
              lineHeight: "1.6",
            }}
          >
            Set up game rules, logic, and rewards for your custom game.
          </Text>
        </div>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          size="large"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Title"
                name="title"
                rules={[{ required: true, message: "Please enter Title" }]}
              >
                <Input placeholder="Title" disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<Text>Select Category</Text>}
                name="category"
                rules={[
                  {
                    required: true,
                    message: "Please select category",
                  },
                ]}
              >
                <Select
                  placeholder="Select exercise category"
                  options={categoryOptions}
                  loading={loadingCategories}
                  style={{
                    borderRadius: "12px",
                    fontSize: "16px",
                  }}
                  //onChange={handleCategoryChange}
                  disabled
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                label="Level"
                name="level"
                rules={[{ required: true }]}
              >
                <InputNumber min={1} style={{ width: "100%" }} disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Timer (seconds)"
                name="timer"
                rules={[{ required: true }]}
              >
                <InputNumber min={10} style={{ width: "100%" }} disabled />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label={
              <Text
                style={{
                  color: "#374151",
                  fontWeight: 600,
                  fontSize: "14px",
                  marginBottom: "8px",
                  display: "block",
                }}
              >
                Instructions *
              </Text>
            }
            name="instructions"
            style={{ marginBottom: 12 }}
            rules={[
              {
                required: true,
                message: "Please enter instructions",
              },
            ]}
          >
            <RichTextEditor
              placeholder="Enter instructions, and guidelines for this exercise..."
              height={200}
            />
          </Form.Item>
          <Form.Item label="Assets" required>
            {selectedCategory === WORD_RECALL_ID ? (
              <Select
                mode="tags"
                style={{ width: "100%" }}
                placeholder="Add words"
                value={words}
                onChange={setWords}
                tokenSeparators={[","]}
              />
            ) : (
              <div>
                {problems.length > 0 && (
                  <Row gutter={8} style={{ marginBottom: 8, fontWeight: 600 }}>
                    <Col span={12}>Question</Col>
                    <Col span={8}>Answer</Col>
                    <Col span={4}></Col>
                  </Row>
                )}
                {problems.map((problem, idx) => (
                  <Row gutter={8} key={idx} style={{ marginBottom: 8 }}>
                    <Col span={12}>
                      <Input
                        placeholder="Question"
                        value={problem.question}
                        onChange={(e) =>
                          handleProblemChange(idx, "question", e.target.value)
                        }
                      />
                    </Col>
                    <Col span={8}>
                      <Input
                        placeholder="Answer"
                        value={problem.answer}
                        onChange={(e) =>
                          handleProblemChange(idx, "answer", e.target.value)
                        }
                        style={{ width: "100%" }}
                      />
                    </Col>
                    <Col span={4}>
                      <Button danger onClick={() => removeProblem(idx)}>
                        Remove
                      </Button>
                    </Col>
                  </Row>
                ))}
                <Button
                  type="dashed"
                  onClick={addProblem}
                  style={{ width: "100%" }}
                >
                  Add Problem
                </Button>
              </div>
            )}
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="Reward"
                name="reward_id"
                rules={[{ required: true, message: "Please select a reward" }]}
              >
                <Select
                  placeholder="Select reward"
                  options={rewardOptions}
                  loading={loadingRewards}
                  style={{
                    borderRadius: "12px",
                    fontSize: "16px",
                  }}
                  onChange={(value) => {
                    const selectedReward = rewardOptions.find(
                      (r) => r.value === value
                    );
                    if (selectedReward) {
                      form.setFieldsValue({ points: selectedReward.points });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Min Correct Answers"
                name="min_correct"
                rules={[{ required: true }]}
              >
                <InputNumber min={1} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="next_game_unlocked"
                valuePropName="checked"
                style={{ marginTop: 32 }}
              >
                <Checkbox>Next Game Unlocked</Checkbox>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Points"
                name="points"
                rules={[{ required: true }]}
              >
                <InputNumber min={0} style={{ width: "100%" }} disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Badge"
                name="badge_id"
                rules={[{ required: true, message: "Please select a badge" }]}
              >
                <Select
                  placeholder="Select badge"
                  options={badgeOptions}
                  loading={loadingBadges}
                  style={{
                    borderRadius: "12px",
                    fontSize: "16px",
                  }}
                  onChange={(value) => {
                    console.log(value);
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Row gutter={16} justify="end">
              <Col>
                <Button
                  style={{
                    height: "48px",
                    borderRadius: "12px",
                    border: "2px solid #e5e7eb",
                    background: "white",
                    color: "#6b7280",
                    fontSize: "16px",
                    fontWeight: 600,
                    transition: "all 0.3s ease",
                    minWidth: 120,
                  }}
                  onClick={onClose}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.borderColor = "#ef4444";
                    target.style.color = "#ef4444";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.borderColor = "#e5e7eb";
                    target.style.color = "#6b7280";
                  }}
                >
                  Cancel
                </Button>
              </Col>
              <Col>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  style={{
                    background:
                      "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    border: "none",
                    borderRadius: "12px",
                    height: "48px",
                    fontSize: "16px",
                    fontWeight: 600,
                    boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                    transition: "all 0.3s ease",
                    minWidth: 160,
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(-2px)";
                    target.style.boxShadow =
                      "0 8px 25px rgba(102, 126, 234, 0.5)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(0)";
                    target.style.boxShadow =
                      "0 4px 15px rgba(102, 126, 234, 0.4)";
                  }}
                >
                  Save Game Logic
                </Button>
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default ConfigureGameModal;
