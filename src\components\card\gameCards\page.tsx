import React, { useState } from "react";
import type { FC } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Button } from "antd";
import { EditOutlined } from "@ant-design/icons";
export interface GameCardProps {
  image: string;
  title: string;
  description: string;
  players?: number;
  id?: string;
  category_name?: string;
  onEdit?: (gameId: string) => void;
  onGameLogic?: (gameId: string) => void;
}

const GameCard: FC<GameCardProps> = ({
  image,
  title,
  description,
  players,
  id,
  category_name,
  onEdit,
  onGameLogic,
}) => {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const safeImage = image || "/assets/add-game.png";
  const safeTitle = title || "Untitled Exercise";
  const safeDescription = description || "No description available";
  const safePlayers = players ?? 0;
  const safeId = id ?? "";
  const safeCategoryName = category_name ?? "";

  const handleManageClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (isNavigating) return;

    setIsNavigating(true);

    const params = new URLSearchParams({ id: safeId });
    const url = `manageGames/gamesDetails?${params.toString()}`;

    try {
      await router.push(url);
    } catch {
      window.location.href = url;
    } finally {
      setIsNavigating(false);
    }
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onEdit && safeId) {
      onEdit(safeId);
    }
  };

  return (
    <Card
      hoverable
      onClick={(e) => e.stopPropagation()}
      cover={
        <div style={{ position: "relative", height: 180 }}>
          <img
            alt={safeTitle}
            src={safeImage}
            style={{
              height: "100%",
              width: "100%",
              objectFit: "cover",
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              cursor: "default",
              transition: "transform 0.3s ease",
               background: "rgba(255, 255, 255, 0.8)",
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/assets/add-game.png";
            }}
          />
          <div
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              height: "60px",
              background: "linear-gradient(transparent, rgba(0,0,0,0.3))",
              pointerEvents: "none",
            }}
          />
          {safeCategoryName && (
            <div
              style={{
                position: "absolute",
                top: "12px",
                right: "12px",
                background: "rgba(102, 126, 234, 0.9)",
                color: "white",
                padding: "4px 12px",
                borderRadius: "20px",
                fontSize: "12px",
                fontWeight: 600,
                backdropFilter: "blur(10px)",
                cursor: "default",
              }}
            >
              {safeCategoryName}
            </div>
          )}
        </div>
      }
      style={{
        width: "100%",
        maxWidth: 300, // 🟣 NEW: prevents ultra-narrow cards
        minWidth: 300, // 🟣 Optional but helpful
        height: 440,
        borderRadius: 20,
        background: "rgba(255, 255, 255, 0.9)",
        backdropFilter: "blur(10px)",
        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
        border: "1px solid rgba(255, 255, 255, 0.2)",
        transition: "all 0.3s ease",
        overflow: "hidden",
        margin: "0 auto", // 🟣 Optional: center-align if it's not inside a grid
      }}
      bodyStyle={{
        padding: 20,
        height: "calc(100% - 180px)", // Subtract image height
        display: "flex",
        flexDirection: "column",
      }}
      onMouseEnter={(e) => {
        const target = e.currentTarget as HTMLElement;
        target.style.transform = "translateY(-8px)";
        target.style.boxShadow = "0 16px 40px rgba(0, 0, 0, 0.15)";
        const img = target.querySelector("img") as HTMLImageElement;
        if (img) img.style.transform = "scale(1.05)";
      }}
      onMouseLeave={(e) => {
        const target = e.currentTarget as HTMLElement;
        target.style.transform = "translateY(0)";
        target.style.boxShadow = "0 8px 32px rgba(0, 0, 0, 0.1)";
        const img = target.querySelector("img") as HTMLImageElement;
        if (img) img.style.transform = "scale(1)";
      }}
    >
      {/* Main Content Area - Flex container */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          justifyContent: "space-between",
        }}
      >
        {/* Top Section - Title and Description */}
        <div style={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
          <h3
            style={{
              margin: 0,
              marginBottom: 12,
              fontSize: 18,
              fontWeight: 700,
              color: "#1f2937",
              cursor: "default",
              lineHeight: 1.3,
              minHeight: "1.3em", // Ensure consistent title height
            }}
          >
            {safeTitle}
          </h3>

          <p
            style={{
              margin: 0,
              fontSize: 14,
              color: "#6b7280",
              lineHeight: 1.5,
              height: "4.5em", // 3 lines x 1.5em = 4.5em
              overflow: "hidden",
              display: "-webkit-box",
              WebkitLineClamp: 3,
              WebkitBoxOrient: "vertical",
              cursor: "default",
              marginBottom: 16,
            }}
            title={safeDescription}
          >
            {safeDescription}
          </p>
        </div>

        {/* Middle Section - Info Cards */}
        <div style={{ marginBottom: 16 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              gap: "8px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                padding: "6px 12px",
                background: "rgba(102, 126, 234, 0.1)",
                borderRadius: "20px",
                flex: 1,
                cursor: "default",
                justifyContent: "center",
                minWidth: 0,
              }}
            >
              <span
                style={{
                  fontSize: "12px",
                  fontWeight: 600,
                  color: "#667eea",
                  textTransform: "uppercase",
                  letterSpacing: "0.5px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {safePlayers} Levels
              </span>
            </div>

            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={handleEditClick}
              style={{
                backgroundColor: "#8b5cf6",
                color: "#fff",
                border: "none",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                padding: "0 12px",
                height: 32,
                fontSize: 12,
                fontWeight: 500,
                display: "flex",
                alignItems: "center",
                gap: 4,
                borderRadius: "16px",
                flexShrink: 0, // Prevent button from shrinking
              }}
            >
              Edit
            </Button>
          </div>
        </div>

        {/* Bottom Section - Manage Button */}
        <div style={{ display: "flex", gap: 12 }}>
          <Button
            block
            loading={isNavigating}
            onClick={handleManageClick}
            disabled={isNavigating}
            style={{
              flex: 1,
              height: "44px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              color: "white",
              fontWeight: 600,
              fontSize: "14px",
              borderRadius: "12px",
              boxShadow: "0 4px 15px rgba(102, 126, 234, 0.3)",
              transition: "all 0.3s ease",
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.style.boxShadow = "0 6px 20px rgba(102, 126, 234, 0.4)";
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.3)";
            }}
          >
            {isNavigating ? "Loading..." : "Manage Exercise"}
          </Button>
          <Button
            type="primary"
            size="large"
            style={{
              flex: 1,
              height: "44px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              color: "white",
              fontWeight: 600,
              fontSize: "14px",
              borderRadius: "12px",
              boxShadow: "0 4px 15px rgba(102, 126, 234, 0.3)",
              transition: "all 0.3s ease",
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.style.boxShadow = "0 6px 20px rgba(102, 126, 234, 0.4)";
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.3)";
            }}
            onClick={() => onGameLogic && safeId && onGameLogic(safeId)}
          >
            Game Logic
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default GameCard;
