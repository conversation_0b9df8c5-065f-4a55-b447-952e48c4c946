import React, { useState } from "react";
import { <PERSON>, But<PERSON> } from "antd";
import type { FC } from "react";
import { useRouter } from "next/navigation";

export interface GameCardProps {
  image: string;
  title: string;
  description: string;
  players?: number;
  id?: string;
  category_name?: string;
}

const GameCard: FC<GameCardProps> = ({
  image,
  title,
  description,
  players,
  id,
  category_name,
}) => {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const safeImage = image || "/assets/add-game.png";
  const safeTitle = title || "Untitled Game";
  const safeDescription = description || "No description available";
  const safePlayers = players || 0;
  const safeId = id || "";
  const safeCategoryName = category_name || "";

  const handleManageClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isNavigating) return;

    setIsNavigating(true);

    const params = new URLSearchParams({ id: safeId });
    const url = `/gamesDetails?${params.toString()}`;

    try {
      await router.push(url);
    } catch {
      window.location.href = url;
    } finally {
      setIsNavigating(false);
    }
  };

  const truncateDescription = (text: string, maxLength = 100) =>
    text.length <= maxLength ? text : `${text.substring(0, maxLength)}...`;

  return (
    <Card
      hoverable
      onClick={(e) => e.stopPropagation()}
      cover={
        <div style={{
          position: "relative",
          overflow: "hidden",
        }}>
          <img
            alt={safeTitle}
            src={safeImage}
            style={{
              height: 200,
              width: "100%",
              objectFit: "cover",
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              cursor: "default",
              transition: "transform 0.3s ease",
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/assets/add-game.png";
            }}
          />
          {/* Gradient overlay */}
          <div
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              height: "60px",
              background: "linear-gradient(transparent, rgba(0,0,0,0.3))",
              pointerEvents: "none",
            }}
          />
          {/* Category badge */}
          {safeCategoryName && (
            <div
              style={{
                position: "absolute",
                top: "12px",
                right: "12px",
                background: "rgba(102, 126, 234, 0.9)",
                color: "white",
                padding: "4px 12px",
                borderRadius: "20px",
                fontSize: "12px",
                fontWeight: 600,
                backdropFilter: "blur(10px)",
              }}
            >
              {safeCategoryName}
            </div>
          )}
        </div>
      }
      style={{
        width: "100%",
        borderRadius: 20,
        height: 420,
        display: "flex",
        flexDirection: "column",
        background: "rgba(255, 255, 255, 0.9)",
        backdropFilter: "blur(10px)",
        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
        border: "1px solid rgba(255, 255, 255, 0.2)",
        transition: "all 0.3s ease",
        overflow: "hidden",
      }}
      bodyStyle={{
        padding: 20,
        display: "flex",
        flexDirection: "column",
        flexGrow: 1,
      }}
      onMouseEnter={(e) => {
        const target = e.currentTarget as HTMLElement;
        target.style.transform = "translateY(-8px)";
        target.style.boxShadow = "0 16px 40px rgba(0, 0, 0, 0.15)";

        // Scale the image
        const img = target.querySelector('img') as HTMLImageElement;
        if (img) {
          img.style.transform = "scale(1.05)";
        }
      }}
      onMouseLeave={(e) => {
        const target = e.currentTarget as HTMLElement;
        target.style.transform = "translateY(0)";
        target.style.boxShadow = "0 8px 32px rgba(0, 0, 0, 0.1)";

        // Reset image scale
        const img = target.querySelector('img') as HTMLImageElement;
        if (img) {
          img.style.transform = "scale(1)";
        }
      }}
    >
      <div style={{ flex: 1 }}>
        <h3
          style={{
            marginBottom: 12,
            fontSize: 18,
            fontWeight: 700,
            color: "#1f2937",
            cursor: "default",
            lineHeight: 1.3,
          }}
        >
          {safeTitle}
        </h3>
        <p
          style={{
            marginBottom: 16,
            fontSize: 14,
            color: "#6b7280",
            lineHeight: 1.5,
            cursor: "default",
          }}
        >
          {truncateDescription(safeDescription, 80)}
        </p>
      </div>

      <div style={{
        marginTop: "auto",
        paddingBottom: 16,
        cursor: "default",
        borderTop: "1px solid rgba(0,0,0,0.05)",
        paddingTop: 16,
      }}>
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 16,
        }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            padding: "6px 12px",
            background: "rgba(102, 126, 234, 0.1)",
            borderRadius: "20px",
          }}>
            <span style={{
              fontSize: "12px",
              fontWeight: 600,
              color: "#667eea",
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}>
              {safePlayers} Levels
            </span>
          </div>
        </div>
      </div>

      <Button
        block
        loading={isNavigating}
        onClick={handleManageClick}
        disabled={isNavigating}
        style={{
          height: "44px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          border: "none",
          color: "white",
          fontWeight: 600,
          fontSize: "14px",
          borderRadius: "12px",
          boxShadow: "0 4px 15px rgba(102, 126, 234, 0.3)",
          transition: "all 0.3s ease",
        }}
        onMouseEnter={(e) => {
          const target = e.target as HTMLElement;
          target.style.boxShadow = "0 6px 20px rgba(102, 126, 234, 0.4)";
        }}
        onMouseLeave={(e) => {
          const target = e.target as HTMLElement;
          target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.3)";
        }}
      >
        {isNavigating ? "Loading..." : "Manage Game"}
      </Button>
    </Card>
  );
};

export default GameCard;
