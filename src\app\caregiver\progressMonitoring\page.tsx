"use client";
import React from "react";
import { <PERSON>, Typography, <PERSON>, Col, Button } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Line, XAxis, YAxis, Tooltip } from "recharts";

const { Title, Text } = Typography;

// Dummy data for charts
const pieData = [
  { name: "Daily Active Users", value: 400 },
  { name: "Monthly Active Users", value: 600 },
];
const pieColors = ["#6366F1", "#F472B6"];

const lineData = [
  { name: "Day 1", Score: 80, "Times Played": 120, "Completion Rate": 60 },
  { name: "Day 2", Score: 90, "Times Played": 100, "Completion Rate": 80 },
  { name: "Day 3", Score: 70, "Times Played": 140, "Completion Rate": 50 },
  { name: "Day 4", Score: 100, "Times Played": 130, "Completion Rate": 90 },
  { name: "Day 5", Score: 60, "Times Played": 110, "Completion Rate": 70 },
];

const games = [
  {
    title: "Speed Racer",
    played: "Played 45 times",
    img: "/games/car1.jpg",
    lastPlayed: "Last played: 2 days ago",
  },
  {
    title: "Dragon Quest",
    played: "Played 32 times",
    img: "/games/dragon.jpg",
    lastPlayed: "Last played: 3 days ago",
  },
  {
    title: "Soccer Pro",
    played: "Played 19 times",
    img: "/games/soccer.jpg",
    lastPlayed: "Last played: 5 days ago",
  },
];

const AnalyticsPage: React.FC = () => (
  <div style={{ maxWidth: 1100, margin: "0 auto", padding: "32px 0 32px 0" }}>
    <Title level={2} style={{ marginBottom: 24 }}>Analytics</Title>

    {/* User Engagement */}
    <div style={{ marginBottom: 32 }}>
      <Title level={4} style={{ marginBottom: 16 }}>User Engagement</Title>
      <Card style={{ borderRadius: 12, marginBottom: 0 }}>
        <Title level={5} style={{ marginBottom: 24 }}>User Engagement Over Time</Title>
        <div style={{ width: "100%", height: 300, display: "flex", justifyContent: "center", alignItems: "center" }}>
          <ResponsiveContainer width="50%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                innerRadius={70}
                outerRadius={100}
                paddingAngle={2}
              >
                {pieData.map((entry, idx) => (
                  <Cell key={`cell-${idx}`} fill={pieColors[idx % pieColors.length]} />
                ))}
              </Pie>
              <Legend verticalAlign="middle" align="right" layout="vertical" />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </Card>
    </div>

    {/* Game Performance */}
    <div>
      <Title level={4} style={{ marginBottom: 16 }}>Game Performance</Title>
      <Card style={{ borderRadius: 12, marginBottom: 0 }}>
        <Title level={5} style={{ marginBottom: 24 }}>Game Performance Metrics</Title>
        <div style={{ width: "100%", height: 220 }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={lineData}>
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="Score" stroke="#6366F1" strokeWidth={2} />
              <Line type="monotone" dataKey="Times Played" stroke="#F472B6" strokeWidth={2} />
              <Line type="monotone" dataKey="Completion Rate" stroke="#34D399" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <Row gutter={24} style={{ marginTop: 32 }}>
          <Col xs={24} md={6}>
            <Text style={{ fontSize: 14, color: "#888" }}>Average Session Duration</Text>
            <div style={{ fontWeight: 600, fontSize: 20, margin: "8px 0" }}>1h 5m</div>
            <Text type="danger" style={{ fontSize: 13 }}>-5%</Text>
            <div style={{ fontSize: 12, color: "#888" }}>Compared to previous period</div>
          </Col>
          <Col xs={24} md={6}>
            <Text style={{ fontSize: 14, color: "#888" }}>Most Played Games</Text>
            {games.map((game) => (
              <Card
                key={game.title}
                style={{
                  margin: "12px 0",
                  borderRadius: 8,
                  padding: 0,
                  boxShadow: "none",
                  border: "1px solid #f0f0f0",
                }}
                bodyStyle={{ display: "flex", alignItems: "center", padding: 12 }}
              >
                <img
                  src={game.img}
                  alt={game.title}
                  style={{
                    width: 56,
                    height: 56,
                    borderRadius: 8,
                    objectFit: "cover",
                    marginRight: 16,
                  }}
                />
                <div>
                  <div style={{ fontWeight: 500 }}>{game.title}</div>
                  <div style={{ fontSize: 13, color: "#888" }}>{game.played}</div>
                  <div style={{ fontSize: 12, color: "#aaa" }}>{game.lastPlayed}</div>
                  <Button size="small" style={{ marginTop: 4 }}>Details</Button>
                </div>
              </Card>
            ))}
            <div style={{ fontSize: 12, color: "#888", marginTop: 8 }}>
              Compared to previous period
            </div>
          </Col>
          <Col xs={24} md={6}>
            <Text style={{ fontSize: 14, color: "#888" }}>Total Playlists</Text>
            <div style={{ fontWeight: 600, fontSize: 20, margin: "8px 0" }}>1,500</div>
            <Text type="danger" style={{ fontSize: 13 }}>-20%</Text>
            <div style={{ fontSize: 12, color: "#888" }}>Compared to previous period</div>
          </Col>
          <Col xs={24} md={6}>
            <Text style={{ fontSize: 14, color: "#888" }}>Total Wins</Text>
            <div style={{ fontWeight: 600, fontSize: 20, margin: "8px 0" }}>800</div>
            <Text type="danger" style={{ fontSize: 13 }}>-12%</Text>
            <div style={{ fontSize: 12, color: "#888" }}>Compared to previous period</div>
          </Col>
        </Row>
      </Card>
    </div>
  </div>
);

export default AnalyticsPage;