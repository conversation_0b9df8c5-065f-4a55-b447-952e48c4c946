"use client";

import React, { useEffect, useState } from "react";
import {
  message,
  Card,
  Typo<PERSON>,
  Row,
  Col,
  Button,
  Layout,
  Spin,
  Divider,
  Modal,
} from "antd";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
} from "recharts";
import {
  DaywiseStatRaw,
  MostPlayedGameRaw,
  ProgressMetricsPayload,
  WrappedResponse,
  FlatResponse,
  LineChartDatum,
  GameDisplay,
} from "@types";
import { dataProviderInstance } from "@providers/data-provider";
import { format } from "date-fns";
const { Title, Text } = Typography;
const { Content } = Layout;
const pieColors = ["#6366F1", "#F472B6"];
// --------- Helpers ---------
const formatDuration = (input?: string): string => {
  if (!input) return "00:00:00";
  const parts = input.split(".");
  return parts[0];
};

const extractPayload = (raw: unknown): ProgressMetricsPayload => {
  const maybeWrapped = raw as WrappedResponse;
  if (maybeWrapped?.data?.data) {
    return maybeWrapped.data.data;
  }
  const maybeFlat = raw as FlatResponse;
  if (maybeFlat?.data) {
    return maybeFlat.data;
  }
  return {};
};

// --------- Component ---------

const AnalyticsPage: React.FC = () => {
  const [analyticsLoading, setAnalyticsLoading] = useState<boolean>(false);
  const [pieData, setPieData] = useState<
    Array<{ name: string; value: number }>
  >([
    { name: "Daily Active Users", value: 0 },
    { name: "Monthly Active Users", value: 0 },
  ]);
  const [lineData, setLineData] = useState<LineChartDatum[]>([]);
  const [games, setGames] = useState<GameDisplay[]>([]);
  const [avgSessionDuration, setAvgSessionDuration] =
    useState<string>("00:00:00");
  const [totalWinCount, setTotalWinCount] = useState<number>(0);
  const [totalPlayCount, setTotalPlayCount] = useState<number>(0);
  const [selectedGame, setSelectedGame] = useState<GameDisplay | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const fetchDashboardData = async () => {
      const tenant_code = localStorage.getItem("tenant_code") ?? "";
      let userDetails: { id?: string } = {};
      try {
        userDetails = JSON.parse(localStorage.getItem("user") ?? "{}");
      } catch {
        // ignore malformed
      }

      const userId = userDetails.id;
      if (!userId) {
        message.error("Missing user ID; cannot load analytics.");
        return;
      }

      try {
        setAnalyticsLoading(true);
        const response = await dataProviderInstance.getProgressMetrics({
          user_id: userId,
          tenant_code,
        });

        const payload = extractPayload(response as unknown);

        // User engagement
        const dailyUsers =
          payload.user_engagement?.daily_active_user_details?.length ?? 0;
        const monthlyUsers =
          payload.user_engagement?.monthly_active_user_details?.length ?? 0;
        setPieData([
          { name: "Daily Active Users", value: dailyUsers },
          { name: "Monthly Active Users", value: monthlyUsers },
        ]);

        // Line chart aggregation
        const daywiseRaw: DaywiseStatRaw[] =
          payload.exercise_performance?.daywise_stats ?? [];

        type Grouped = {
          totalScore: number;
          count: number;
          timesPlayed: number;
          totalCompletion: number;
        };
        const grouped: Record<string, Grouped> = {};

        daywiseRaw.forEach((item) => {
          const dateKey = item.started_at
            ? item.started_at.slice(0, 10)
            : new Date().toISOString().slice(0, 10);
          const score = item.score ?? 0;
          const completionValue = item.completed ? 100 : 0;

          if (!grouped[dateKey]) {
            grouped[dateKey] = {
              totalScore: score,
              count: 1,
              timesPlayed: 1,
              totalCompletion: completionValue,
            };
          } else {
            grouped[dateKey].totalScore += score;
            grouped[dateKey].timesPlayed += 1;
            grouped[dateKey].totalCompletion += completionValue;
            grouped[dateKey].count += 1;
          }
        });

        const transformedLineData: LineChartDatum[] = Object.entries(grouped)
          .sort(([a], [b]) => (new Date(a) > new Date(b) ? 1 : -1))
          .map(([date, stats]) => ({
            // name: `Day ${new Date(date).getDate()}`,
            name: format(new Date(date), "MMM d"),
            Score: parseFloat((stats.totalScore / stats.count).toFixed(2)),
            "Times Played": stats.timesPlayed,
            "Completion Rate": parseFloat(
              (stats.totalCompletion / stats.count).toFixed(2)
            ),
          }));
        //       const transformedLineData: LineChartDatum[] = Object.entries(grouped)
        // .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
        // .map(([date, stats], index) => ({
        //   name: format(new Date(date), "MMM d"), // or `Day ${index + 1}`
        //   Score: parseFloat((stats.totalScore / stats.count).toFixed(2)),
        //   "Times Played": stats.timesPlayed,
        //   "Completion Rate": parseFloat((stats.totalCompletion / stats.count).toFixed(2)),
        // }));
        setLineData(transformedLineData);

        // Most played games
        const mostPlayedRaw: MostPlayedGameRaw[] =
          payload.exercise_performance?.most_played_games ?? [];
        const transformedGames: GameDisplay[] = mostPlayedRaw.map((game) => ({
          title: game.game_name,
          played: `Played ${game.play_count} times`,
          img: game.thumbnail_url,
          lastPlayed: "Recently played",
        }));
        setGames(transformedGames);

        // Other metrics
        const avgDurationRaw =
          payload.exercise_performance?.average_session_duration ?? "00:00:00";
        setAvgSessionDuration(formatDuration(avgDurationRaw));
        setTotalWinCount(payload.exercise_performance?.total_win_count ?? 0);
        setTotalPlayCount(payload.exercise_performance?.total_play_count ?? 0);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        message.error("Failed to load dashboard statistics");
      } finally {
        setAnalyticsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (analyticsLoading) {
    return (
      <div
        style={{
          minHeight: 300,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin tip="Loading analytics..." />
      </div>
    );
  }

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "48px",
            maxWidth: "800px",
          }}
        >
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            Progress Monitoring
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Track player performance and engagement analytics
          </Text>
        </div>

        <div style={{ width: "100%", maxWidth: "1200px" }}>
          {/* User Engagement */}
          <div style={{ marginBottom: "48px", width: "100%" }}>
            <Title
              level={3}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 600,
                marginBottom: "24px",
                fontSize: "24px",
              }}
            >
              User Engagement
            </Title>
            <Card
              style={{
                background: "rgba(255, 255, 255, 0.8)",
                backdropFilter: "blur(10px)",
                boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                borderRadius: "20px",
              }}
            >
              <Title
                level={4}
                style={{
                  marginBottom: "24px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                User Engagement Over Time
              </Title>
              <div
                style={{
                  width: "100%",
                  height: 300,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <ResponsiveContainer width="50%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      innerRadius={70}
                      outerRadius={100}
                      paddingAngle={2}
                    >
                      {pieData.map((entry, idx) => (
                        <Cell
                          key={`cell-${idx}`}
                          fill={pieColors[idx % pieColors.length]}
                        />
                      ))}
                    </Pie>
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>

          {/* Exercise Performance */}
          <div style={{ width: "100%" }}>
            <Title
              level={3}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 600,
                marginBottom: "24px",
                fontSize: "24px",
              }}
            >
              Exercise Performance
            </Title>
            <Card
              style={{
                background: "rgba(255, 255, 255, 0.8)",
                backdropFilter: "blur(10px)",
                boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                borderRadius: "20px",
              }}
            >
              <Title
                level={4}
                style={{
                  marginBottom: "24px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Exercise Performance Metrics
              </Title>

              <div style={{ width: "100%", height: 220 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={
                      lineData.length
                        ? lineData
                        : [
                            {
                              name: "No data",
                              Score: 0,
                              "Times Played": 0,
                              "Completion Rate": 0,
                            } as LineChartDatum,
                          ]
                    }
                  >
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="Score"
                      stroke="#6366F1"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="Times Played"
                      stroke="#F472B6"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="Completion Rate"
                      stroke="#34D399"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* <Row gutter={[32, 32]} style={{ marginTop: "32px" }}>
                <Col xs={24} sm={12} lg={6}>
                  <StatCard
                    title="Avg. Session Duration"
                    value={avgSessionDuration}
                    delta="-5%"
                  />
                </Col>

                <Col xs={24} sm={12} lg={6}>
                  <Card
                    style={{
                      background: "rgba(255, 255, 255, 0.6)",
                      borderRadius: "12px",
                      padding: 20,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        color: "#6b7280",
                        fontWeight: 500,
                        display: "block",
                        marginBottom: "16px",
                      }}
                    >
                      Most Played Exercises
                    </Text>
                    {games.map((game) => (
                      <Card
                        key={game.title}
                        style={{
                          margin: "12px 0",
                          borderRadius: "8px",
                          padding: 0,
                          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                          background: "rgba(255, 255, 255, 0.8)",
                        }}
                        bodyStyle={{
                          display: "flex",
                          alignItems: "center",
                          padding: 12,
                        }}
                      >
                        <img
                          src={game.img}
                          alt={game.title}
                          style={{
                            width: 56,
                            height: 56,
                            borderRadius: 8,
                            objectFit: "cover",
                            marginRight: 16,
                          }}
                        />
                        <div>
                          <div style={{ fontWeight: 600, color: "#374151" }}>
                            {game.title}
                          </div>
                          <div style={{ fontSize: 13, color: "#6b7280" }}>
                            {game.played}
                          </div>
                          <div style={{ fontSize: 12, color: "#9ca3af" }}>
                            {game.lastPlayed}
                          </div>
                          <Button
                            size="small"
                            style={{
                              marginTop: 4,
                              background:
                                "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                              border: "none",
                              color: "white",
                              borderRadius: "6px",
                            }}
                            onClick={() => {
                              setSelectedGame(game);
                              setIsModalOpen(true);
                            }}
                          >
                            Details
                          </Button>
                        </div>
                      </Card>
                    ))}
                    <div
                      style={{ fontSize: 12, color: "#6b7280", marginTop: 8 }}
                    >
                      Compared to previous period
                    </div>
                  </Card>
                </Col>

                <Col xs={24} sm={12} lg={6}>
                  <StatCard
                    title="Total Plays"
                    value={totalPlayCount}
                    delta="-20%"
                  />
                </Col>

                <Col xs={24} sm={12} lg={6}>
                  <StatCard
                    title="Total Wins"
                    value={totalWinCount}
                    delta="-12%"
                  />
                </Col>
              </Row> */}
              {/* Top metrics row */}
              <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
                <Col xs={24} sm={8}>
                  <StatCard
                    title="Avg. Session Duration"
                    value={avgSessionDuration}
                    delta="-5%"
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <StatCard
                    title="Total Plays"
                    value={totalPlayCount}
                    delta="-20%"
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <StatCard
                    title="Total Wins"
                    value={totalWinCount}
                    delta="-12%"
                  />
                </Col>
              </Row>

              {/* Most Played Exercises grid */}
              <div style={{ marginTop: 8 }}>
                <Title
                  level={5}
                  style={{
                    background:
                      "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    fontWeight: 600,
                    marginBottom: "16px",
                    fontSize: "18px",
                  }}
                >
                  Most Played Exercises
                </Title>

                <Row gutter={[24, 24]}>
                  {games.map((game) => (
                    <Col key={game.title} xs={24} sm={12} lg={8}>
                      <Card
                        style={{
                          borderRadius: 12,
                          padding: 0,
                          boxShadow: "0 4px 20px rgba(0,0,0,0.05)",
                          background: "rgba(255,255,255,0.9)",
                        }}
                        bodyStyle={{
                          display: "flex",
                          alignItems: "center",
                          padding: 16,
                          gap: 12,
                        }}
                      >
                        <img
                          src={game.img}
                          alt={game.title}
                          style={{
                            width: 64,
                            height: 64,
                            borderRadius: 8,
                            objectFit: "cover",
                            flexShrink: 0,
                          }}
                        />
                        <div style={{ flex: 1 }}>
                          <div
                            style={{
                              fontWeight: 600,
                              color: "#374151",
                              marginBottom: 4,
                            }}
                          >
                            {game.title}
                          </div>
                          <div style={{ fontSize: 13, color: "#6b7280" }}>
                            {game.played}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: "#9ca3af",
                              marginBottom: 8,
                            }}
                          >
                            {game.lastPlayed}
                          </div>
                          <Button
                            size="small"
                            style={{
                              background:
                                "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                              border: "none",
                              color: "white",
                              borderRadius: 6,
                              padding: "4px 12px",
                            }}
                            onClick={() => {
                              setSelectedGame(game);
                              setIsModalOpen(true);
                            }}
                          >
                            Details
                          </Button>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>

                <div style={{ fontSize: 12, color: "#6b7280", marginTop: 12 }}>
                  Compared to previous period
                </div>
              </div>
              <Modal
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                centered
                closable
                bodyStyle={{
                  borderRadius: 12,
                  padding: 16,
                  maxWidth: 320,
                  background: "rgba(255,255,255,0.95)",
                  boxShadow: "0 12px 40px rgba(0,0,0,0.08)",
                }}
                style={{ borderRadius: 16 }}
                width={340}
              >
                {selectedGame ? (
                  <div>
                    <div
                      style={{
                        display: "flex",
                        gap: 12,
                        marginBottom: 12,
                        alignItems: "center",
                      }}
                    >
                      <img
                        src={selectedGame.img}
                        alt={selectedGame.title}
                        style={{
                          width: 64,
                          height: 64,
                          borderRadius: 8,
                          objectFit: "cover",
                          flexShrink: 0,
                        }}
                      />
                      <div>
                        <div style={{ fontSize: 18, fontWeight: 700 }}>
                          {selectedGame.title}
                        </div>
                        <div style={{ fontSize: 12, color: "#6b7280" }}>
                          {selectedGame.lastPlayed}
                        </div>
                      </div>
                    </div>

                    <Divider style={{ margin: "8px 0" }} />

                    <div style={{ marginBottom: 8 }}>
                      <div
                        style={{
                          fontSize: 12,
                          color: "#6b7280",
                          fontWeight: 600,
                        }}
                      >
                        Play Count
                      </div>
                      <div style={{ fontSize: 14 }}>{selectedGame.played}</div>
                    </div>

                    {/* Add more fields if you have them */}
                    <div style={{ marginBottom: 8 }}>
                      <div
                        style={{
                          fontSize: 12,
                          color: "#6b7280",
                          fontWeight: 600,
                        }}
                      >
                        Status
                      </div>
                      <div style={{ fontSize: 14 }}>
                        {selectedGame.lastPlayed ? "Recently played" : "N/A"}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>No game selected.</div>
                )}
              </Modal>
            </Card>
          </div>
        </div>
      </Content>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .analytics-container {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>
    </Layout>
  );
};

const StatCard: React.FC<{
  title: string;
  value: string | number;
  delta?: string;
}> = ({ title, value, delta }) => (
  <div
    style={{
      padding: "20px",
      background: "rgb(226 240 226 / 60%)",
      borderRadius: "12px",
      border: "1px solid rgba(255, 255, 255, 0.3)",
      textAlign: "center",
    }}
  >
    <Text style={{ fontSize: 14, color: "#6b7280", fontWeight: 500 }}>
      {title}
    </Text>
    <div
      style={{
        fontWeight: 700,
        fontSize: 24,
        margin: "12px 0",
        color: "#374151",
      }}
    >
      {value}
    </div>
    {delta && (
      <>
        <Text type="danger" style={{ fontSize: 13, fontWeight: 600 }}>
          {delta}
        </Text>
        <div style={{ fontSize: 12, color: "#6b7280", marginTop: 4 }}>
          Compared to previous period
        </div>
      </>
    )}
  </div>
);

export default AnalyticsPage;
