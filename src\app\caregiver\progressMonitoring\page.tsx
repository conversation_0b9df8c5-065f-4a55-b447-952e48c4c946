"use client";
import React from "react";
import { <PERSON>, Typo<PERSON>, <PERSON>, Col, Button, Layout } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, Tooltip } from "recharts";

const { Title, Text } = Typography;
const { Content } = Layout;

// Dummy data for charts
const pieData = [
  { name: "Daily Active Users", value: 400 },
  { name: "Monthly Active Users", value: 600 },
];
const pieColors = ["#6366F1", "#F472B6"];

const lineData = [
  { name: "Day 1", Score: 80, "Times Played": 120, "Completion Rate": 60 },
  { name: "Day 2", Score: 90, "Times Played": 100, "Completion Rate": 80 },
  { name: "Day 3", Score: 70, "Times Played": 140, "Completion Rate": 50 },
  { name: "Day 4", Score: 100, "Times Played": 130, "Completion Rate": 90 },
  { name: "Day 5", Score: 60, "Times Played": 110, "Completion Rate": 70 },
];

const games = [
  {
    title: "Speed Racer",
    played: "Played 45 times",
    img: "/games/car1.jpg",
    lastPlayed: "Last played: 2 days ago",
  },
  {
    title: "Dragon Quest",
    played: "Played 32 times",
    img: "/games/dragon.jpg",
    lastPlayed: "Last played: 3 days ago",
  },
  {
    title: "Soccer Pro",
    played: "Played 19 times",
    img: "/games/soccer.jpg",
    lastPlayed: "Last played: 5 days ago",
  },
];

const AnalyticsPage: React.FC = () => (
  <Layout
    style={{
      maxWidth: 1280,
      minHeight: "100vh",
      background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
      margin: "0 auto",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      position: "relative",
      overflow: "hidden",
    }}
  >
    {/* Background decorative elements */}
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        height: "300px",
        background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
        borderRadius: "0 0 50% 50%",
        transform: "scale(1.5)",
        zIndex: 0,
      }}
    />
    <div
      style={{
        position: "absolute",
        top: "-50px",
        right: "-50px",
        width: "200px",
        height: "200px",
        background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
        borderRadius: "50%",
        zIndex: 0,
      }}
    />
    <div
      style={{
        position: "absolute",
        bottom: "-100px",
        left: "-100px",
        width: "300px",
        height: "300px",
        background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
        borderRadius: "50%",
        zIndex: 0,
      }}
    />

    <Content
      style={{
        padding: "40px 20px",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        zIndex: 1,
      }}
    >
      {/* Welcome Header */}
      <div style={{
        textAlign: "center",
        marginBottom: "48px",
        maxWidth: "800px",
      }}>
        <Title
          level={1}
          style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            fontWeight: 700,
            marginBottom: "16px",
            fontSize: "48px",
            letterSpacing: "-1px",
          }}
        >
          Progress Monitoring
        </Title>
        <Text
          style={{
            color: "#6b7280",
            fontSize: "18px",
            fontWeight: 500,
            display: "block",
            marginBottom: "32px",
          }}
        >
          Track player performance and engagement analytics
        </Text>
      </div>

      <div style={{ width: "100%", maxWidth: "1200px" }}>

        {/* User Engagement Section */}
        <div style={{ marginBottom: "48px", width: "100%" }}>
          <Title
            level={3}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 600,
              marginBottom: "24px",
              fontSize: "24px",
            }}
          >
            User Engagement
          </Title>
          <Card
            style={{
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              borderRadius: "20px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              marginBottom: 0
            }}
          >
            <Title
              level={4}
              style={{
                marginBottom: "24px",
                color: "#374151",
                fontWeight: 600,
              }}
            >
              User Engagement Over Time
            </Title>
        <div style={{ width: "100%", height: 300, display: "flex", justifyContent: "center", alignItems: "center" }}>
          <ResponsiveContainer width="50%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                innerRadius={70}
                outerRadius={100}
                paddingAngle={2}
              >
                {pieData.map((entry, idx) => (
                  <Cell key={`cell-${idx}`} fill={pieColors[idx % pieColors.length]} />
                ))}
              </Pie>
              <Legend verticalAlign="middle" align="right" layout="vertical" />
            </PieChart>
          </ResponsiveContainer>
            </div>
          </Card>
        </div>

        {/* Game Performance Section */}
        <div style={{ width: "100%" }}>
          <Title
            level={3}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 600,
              marginBottom: "24px",
              fontSize: "24px",
            }}
          >
            Game Performance
          </Title>
          <Card
            style={{
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              borderRadius: "20px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              marginBottom: 0
            }}
          >
            <Title
              level={4}
              style={{
                marginBottom: "24px",
                color: "#374151",
                fontWeight: 600,
              }}
            >
              Game Performance Metrics
            </Title>
        <div style={{ width: "100%", height: 220 }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={lineData}>
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="Score" stroke="#6366F1" strokeWidth={2} />
              <Line type="monotone" dataKey="Times Played" stroke="#F472B6" strokeWidth={2} />
              <Line type="monotone" dataKey="Completion Rate" stroke="#34D399" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>
            <Row gutter={[32, 32]} style={{ marginTop: "32px" }}>
              <Col xs={24} sm={12} lg={6}>
                <div style={{
                  padding: "20px",
                  background: "rgba(255, 255, 255, 0.6)",
                  borderRadius: "12px",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                  textAlign: "center",
                }}>
                  <Text style={{ fontSize: 14, color: "#6b7280", fontWeight: 500 }}>Average Session Duration</Text>
                  <div style={{ fontWeight: 700, fontSize: 24, margin: "12px 0", color: "#374151" }}>1h 5m</div>
                  <Text type="danger" style={{ fontSize: 13, fontWeight: 600 }}>-5%</Text>
                  <div style={{ fontSize: 12, color: "#6b7280", marginTop: "4px" }}>Compared to previous period</div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <div style={{
                  padding: "20px",
                  background: "rgba(255, 255, 255, 0.6)",
                  borderRadius: "12px",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                }}>
                  <Text style={{ fontSize: 14, color: "#6b7280", fontWeight: 500, display: "block", marginBottom: "16px" }}>Most Played Games</Text>
                  {games.map((game) => (
                    <Card
                      key={game.title}
                      style={{
                        margin: "12px 0",
                        borderRadius: "8px",
                        padding: 0,
                        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                        border: "1px solid rgba(255, 255, 255, 0.3)",
                        background: "rgba(255, 255, 255, 0.8)",
                      }}
                      styles={{ body: { display: "flex", alignItems: "center", padding: 12 } }}
                    >
                      <img
                        src={game.img}
                        alt={game.title}
                        style={{
                          width: 56,
                          height: 56,
                          borderRadius: 8,
                          objectFit: "cover",
                          marginRight: 16,
                        }}
                      />
                      <div>
                        <div style={{ fontWeight: 600, color: "#374151" }}>{game.title}</div>
                        <div style={{ fontSize: 13, color: "#6b7280" }}>{game.played}</div>
                        <div style={{ fontSize: 12, color: "#9ca3af" }}>{game.lastPlayed}</div>
                        <Button
                          size="small"
                          style={{
                            marginTop: 4,
                            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                            border: "none",
                            color: "white",
                            borderRadius: "6px",
                          }}
                        >
                          Details
                        </Button>
                      </div>
                    </Card>
                  ))}
                  <div style={{ fontSize: 12, color: "#6b7280", marginTop: 8 }}>
                    Compared to previous period
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <div style={{
                  padding: "20px",
                  background: "rgba(255, 255, 255, 0.6)",
                  borderRadius: "12px",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                  textAlign: "center",
                }}>
                  <Text style={{ fontSize: 14, color: "#6b7280", fontWeight: 500 }}>Total Playlists</Text>
                  <div style={{ fontWeight: 700, fontSize: 24, margin: "12px 0", color: "#374151" }}>1,500</div>
                  <Text type="danger" style={{ fontSize: 13, fontWeight: 600 }}>-20%</Text>
                  <div style={{ fontSize: 12, color: "#6b7280", marginTop: "4px" }}>Compared to previous period</div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <div style={{
                  padding: "20px",
                  background: "rgba(255, 255, 255, 0.6)",
                  borderRadius: "12px",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                  textAlign: "center",
                }}>
                  <Text style={{ fontSize: 14, color: "#6b7280", fontWeight: 500 }}>Total Wins</Text>
                  <div style={{ fontWeight: 700, fontSize: 24, margin: "12px 0", color: "#374151" }}>800</div>
                  <Text type="danger" style={{ fontSize: 13, fontWeight: 600 }}>-12%</Text>
                  <div style={{ fontSize: 12, color: "#6b7280", marginTop: "4px" }}>Compared to previous period</div>
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      </div>
    </Content>

    {/* CSS Animations */}
    <style jsx>{`
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      .analytics-container {
        animation: fadeInUp 0.8s ease-out;
      }
    `}</style>
  </Layout>
);

export default AnalyticsPage;