import React, { useState, useEffect } from "react";
import { Modal, Table, message, Spin } from "antd";
import { dataProviderInstance } from "@providers/data-provider";
import { CareGiverResponse } from "@types";

interface AssignCaregiverDialogProps {
  open: boolean;
  onClose: () => void;
  player: { id: number; name: string } | null;
}

const AssignCaregiverDialog: React.FC<AssignCaregiverDialogProps> = ({
  open,
  onClose,
  player,
}) => {
  const [caregivers, setCaregivers] = useState<CareGiverResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (open) {
      setLoading(true);
      const tenant_code = localStorage.getItem("tenant_code") || "";
      dataProviderInstance
        .listCareGiver({ tenant_code: tenant_code, party_id: String(player?.id), party_type_key: "CAREGIVER" })
        .then((res) => {
       const caregivers = res.data.data || [];
          setCaregivers(caregivers);
          setLoading(false);
        })
        .catch((err) => {
          console.error(err);
          message.error("Failed to load caregivers.");
          setLoading(false);
        });
    }
  }, [open]);

  useEffect(() => {
    if (!open) {
      setSelectedRowKeys([]);
    }
  }, [open]);

  const handleAssign = async () => {
    if (!player) return;
    if (selectedRowKeys.length === 0) {
      message.warning("Please select at least one caregiver.");
      return;
    }
    // setSubmitting(true);
    try {
      await dataProviderInstance.assignCareTaker({
        party_id: String(player.id),
        caregiver_ids: selectedRowKeys as string[],
      });
      message.success("Caregivers assigned successfully!");
      onClose();
    } catch (error) {
      console.log(error);
      message.error("Failed to assign caregivers.");
    } finally {
      // setSubmitting(false);
    }
  };

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
    },
    {
      title: "Description",
      dataIndex: "description",
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
  };

  return (
    <Modal
      title="Assign Caregiver"
      visible={open}
      onCancel={onClose}
      onOk={handleAssign}
      okText="Assign"
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={caregivers}
          rowKey="id"
          pagination={{ pageSize: 5 }}
        />
      </Spin>
    </Modal>
  );
};

export default AssignCaregiverDialog;
