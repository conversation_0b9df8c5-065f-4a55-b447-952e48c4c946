"use client";
import React from "react";
import { Button } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";

interface BackButtonProps {
  /** Custom onClick handler. If not provided, uses router.back() */
  onClick?: () => void;
  /** Custom route to navigate to. If provided, overrides onClick */
  to?: string;
  /** Button size - affects height and width */
  size?: "small" | "medium" | "large";
  /** Custom style overrides */
  style?: React.CSSProperties;
  /** Tooltip text */
  title?: string;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Custom class name */
  className?: string;
}

const BackButton: React.FC<BackButtonProps> = ({
  onClick,
  to,
  size = "medium",
  style = {},
  title = "Go Back",
  disabled = false,
  className,
}) => {
  const router = useRouter();

  // Size configurations
  const sizeConfig = {
    small: { height: "36px", width: "36px" },
    medium: { height: "44px", width: "44px" },
    large: { height: "52px", width: "52px" },
  };

  const handleClick = () => {
    if (disabled) return;
    
    if (to) {
      router.push(to);
    } else if (onClick) {
      onClick();
    } else {
      router.back();
    }
  };

  const defaultStyle: React.CSSProperties = {
    background: "rgba(102, 126, 234, 0.1)",
    border: "1px solid rgba(102, 126, 234, 0.2)",
    color: "#667eea",
    borderRadius: "12px",
    height: sizeConfig[size].height,
    width: sizeConfig[size].width,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: "all 0.3s ease",
    cursor: disabled ? "not-allowed" : "pointer",
    opacity: disabled ? 0.5 : 1,
    ...style,
  };

  return (
    <Button
      icon={<ArrowLeftOutlined />}
      style={defaultStyle}
      onClick={handleClick}
      title={title}
      disabled={disabled}
      className={className}
      onMouseEnter={(e) => {
        if (!disabled) {
          const target = e.target as HTMLElement;
          target.style.background = "#667eea";
          target.style.color = "white";
          target.style.transform = "scale(1.05)";
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled) {
          const target = e.target as HTMLElement;
          target.style.background = "rgba(102, 126, 234, 0.1)";
          target.style.color = "#667eea";
          target.style.transform = "scale(1)";
        }
      }}
    />
  );
};

export default BackButton;
