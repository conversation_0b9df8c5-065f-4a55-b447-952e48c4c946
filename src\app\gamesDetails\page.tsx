"use client";
import { useSearchParams } from "next/navigation";
import {
  Typography,
  Card,
  Image,
  Space,
  Spin,
  message,
  Row,
  Col,
  Button,
  Select,
  Input,
  DatePicker,
} from "antd";
import React, { useState, useEffect } from "react";
import { dataProviderInstance } from "@providers/data-provider";
import { Game, TenantOption } from "@types";
import dayjs from "dayjs";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";

const { Title, Text } = Typography;
const { TextArea } = Input;

const GameDetails = () => {
  const searchParams = useSearchParams();
  const gameId = searchParams.get("id") || "";

  const [gameDetails, setGameDetails] = useState<Game | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [tenantOptions, setTenantOptions] = useState<TenantOption[]>([]);
  const [saving, setSaving] = useState(false);
  const [selectedParties, setSelectedParties] = useState<string[]>([]);
  const [dueDate, setDueDate] = useState<string>("");
  const [notes, setNotes] = useState<string>("");

  useEffect(() => {
    if (gameId) {
      fetchGameDetails();
      fetchUnassignedTenants();
    }
  }, [gameId]);

  const fetchGameDetails = async () => {
    try {
      setLoading(true);

      const response = await dataProviderInstance.fetchGamedetails({
        game_id: gameId,
      });

      if (response && response.data) {
        setGameDetails(response.data);
      } else {
        throw new Error("No game data received");
      }
    } catch {
      message.error(ERROR_MESSAGES.load_gamedetails);
      setGameDetails(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchUnassignedTenants = async () => {
    try {
      setLoadingTenants(true);

      const response = await dataProviderInstance.listAllUnassignedTenantByGame(
        {
          game_id: gameId,
        }
      );

      if (response && response.data) {
        const tenants = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];

        const formattedTenants: TenantOption[] = tenants.map(
          (tenant: {
            id: string | number;
            tenant_name: string;
            name?: string;
          }) => ({
            label: tenant.name || tenant.tenant_name || `Tenant ${tenant.id}`,
            value: tenant.id.toString(),
          })
        );

        setTenantOptions(formattedTenants);
      } else {
        setTenantOptions([]);
      }
    } catch {
      message.error(ERROR_MESSAGES.load_unassigned_tenants);
      setTenantOptions([]);
    } finally {
      setLoadingTenants(false);
    }
  };

  const handleSave = async () => {
    if (!selectedParties.length || !gameId) {
      message.error(ERROR_MESSAGES.select_parties);
      return;
    }

    try {
      setSaving(true);

      await dataProviderInstance.assignGameToTenant({
        party_ids: selectedParties,
        game_id: gameId,
        due_date: dueDate,
        notes: notes,
      });

      message.success(SUCCESS_MESSAGES.selected_parties);
      setSelectedParties([]);

      setDueDate("");
      setNotes("");
    } catch {
      message.error(ERROR_MESSAGES.assign_game_to_parties);
    } finally {
      setSaving(false);
    }
  };

  if (loading || loadingTenants) {
    return (
      <div
        style={{
          maxWidth: 1280,
          margin: "0 auto",
          padding: "40px 24px",
          textAlign: "center",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (!gameDetails) {
    return (
      <div style={{ maxWidth: 1280, margin: "0 auto", padding: "40px 24px" }}>
        <Title level={3}>Game not found</Title>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: 1280, margin: "0 auto", padding: "40px 24px" }}>
      <Title level={3}>Game Details</Title>

      <Card
        cover={
          <Image
            src={gameDetails.thumbnail_url || "/assets/add-game.png"}
            alt={gameDetails.game_name}
            preview={false}
            style={{
              borderRadius: "8px 8px 0 0",
              height: 300,
              objectFit: "cover",
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/assets/add-game.png";
            }}
          />
        }
      >
        <Space direction="vertical" size={16} style={{ width: "100%" }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: 8 }}>
              {gameDetails.game_name}
            </Title>

            <Text style={{ fontSize: 16, lineHeight: 1.5 }}>
              Category Name: {gameDetails.category_name}
            </Text>
          </div>

          <Text style={{ fontSize: 16, lineHeight: 1.5 }}>
            Description: {gameDetails.description}
          </Text>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Space>
                <Text style={{ fontSize: 16, lineHeight: 1.5 }}>
                  Levels: {gameDetails.no_of_levels}
                </Text>
              </Space>
            </Col>
          </Row>
        </Space>

        <div style={{ marginTop: 32 }}>
          <Title level={4}>Assign to Tenant</Title>

          <Space direction="vertical" size={16} style={{ width: "100%" }}>
            <div>
              <Text
                style={{ display: "block", marginBottom: 8, fontWeight: 500 }}
              >
                Select Players <span style={{ color: "red" }}>*</span>
              </Text>
              <Select
                mode="multiple"
                placeholder="Select players"
                value={selectedParties}
                onChange={(values) => setSelectedParties(values)}
                loading={loadingTenants}
                style={{ width: "100%" }}
                notFoundContent={
                  loadingTenants ? <Spin size="small" /> : "No tenants found"
                }
              >
                {tenantOptions.map((tenant) => (
                  <Select.Option key={tenant.value} value={tenant.value}>
                    {tenant.label}
                  </Select.Option>
                ))}
              </Select>
            </div>

            <Row gutter={16}>
              <Col xs={24} sm={5}>
                <div>
                  <Text
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Due Date
                    <span style={{ color: "red" }}>*</span>
                  </Text>

                  <DatePicker
                    size="large"
                    style={{ width: "100%" }}
                    disabledDate={(current) =>
                      current && current < dayjs().startOf("day")
                    }
                    value={dueDate ? dayjs(dueDate) : null}
                    onChange={(_, dateString) => {
                      if (typeof dateString === "string")
                        setDueDate(dateString);
                      else setDueDate("");
                    }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={19}>
                <div>
                  <Text
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Notes <span style={{ color: "red" }}>*</span>
                  </Text>
                  <TextArea
                    placeholder="Enter notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={4}
                    style={{ width: "100%" }}
                  />
                </div>
              </Col>
            </Row>

            <div style={{ textAlign: "right", marginTop: 16 }}>
              <Button
                type="primary"
                size="large"
                style={{ width: "auto" }}
                onClick={handleSave}
                loading={saving}
                disabled={!selectedParties.length || !dueDate || !notes.trim()}
              >
                {saving ? "Saving..." : "Save"}
              </Button>
            </div>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default GameDetails;
