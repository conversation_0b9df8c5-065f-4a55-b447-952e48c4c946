"use client";
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Divider } from "antd";
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  UndoOutlined,
  RedoOutlined,
} from "@ant-design/icons";

interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value = "",
  onChange,
  placeholder = "Enter rules and instructions...",
  height = 200,
  disabled = false,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const executeCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      onChange?.(editorRef.current.innerHTML);
    }
    editorRef.current?.focus();
  };

  const handleInput = () => {
    if (editorRef.current) {
      onChange?.(editorRef.current.innerHTML);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const toolbarButtons = [
    {
      icon: <BoldOutlined />,
      command: "bold",
      tooltip: "Bold (Ctrl+B)",
    },
    {
      icon: <ItalicOutlined />,
      command: "italic",
      tooltip: "Italic (Ctrl+I)",
    },
    {
      icon: <UnderlineOutlined />,
      command: "underline",
      tooltip: "Underline (Ctrl+U)",
    },
    { divider: true },
    {
      icon: <UnorderedListOutlined />,
      command: "insertUnorderedList",
      tooltip: "Bullet List",
    },
    {
      icon: <OrderedListOutlined />,
      command: "insertOrderedList",
      tooltip: "Numbered List",
    },
    { divider: true },
    {
      icon: <AlignLeftOutlined />,
      command: "justifyLeft",
      tooltip: "Align Left",
    },
    {
      icon: <AlignCenterOutlined />,
      command: "justifyCenter",
      tooltip: "Align Center",
    },
    {
      icon: <AlignRightOutlined />,
      command: "justifyRight",
      tooltip: "Align Right",
    },
    { divider: true },
    {
      icon: <UndoOutlined />,
      command: "undo",
      tooltip: "Undo (Ctrl+Z)",
    },
    {
      icon: <RedoOutlined />,
      command: "redo",
      tooltip: "Redo (Ctrl+Y)",
    },
  ];

  return (
    <div
      style={{
        border: `2px solid ${isFocused ? "#667eea" : "#e5e7eb"}`,
        borderRadius: "14px",
        // background: "linear-gradient(135deg, #ffffff 0%, #fafbfc 100%)",
        background: "#ffffff",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        boxShadow: isFocused
          ? "0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 16px rgba(0, 0, 0, 0.08)"
          : "0 2px 8px rgba(0, 0, 0, 0.04)",
        transform: isFocused ? "translateY(-1px)" : "translateY(0)",
      }}
    >
      {/* Toolbar */}
      <div
        style={{
          padding: "12px 16px",
          borderBottom: "1px solid rgba(226, 232, 240, 0.6)",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          borderRadius: "12px 12px 0 0",
          display: "flex",
          alignItems: "center",
          gap: "4px",
          flexWrap: "wrap",
        }}
      >
        {toolbarButtons.map((button, index) => {
          if (button.divider) {
            return (
              <Divider
                key={index}
                type="vertical"
                style={{
                  height: "20px",
                  margin: "0 4px",
                  borderColor: "rgba(148, 163, 184, 0.4)",
                }}
              />
            );
          }

          return (
            <Tooltip key={index} title={button.tooltip}>
              <Button
                type="text"
                icon={button.icon}
                size="small"
                disabled={disabled}
                style={{
                  width: "32px",
                  height: "32px",
                  borderRadius: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#64748b",
                  transition: "all 0.2s ease",
                }}
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => executeCommand(button.command ?? "")}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "rgba(102, 126, 234, 0.1)";
                  target.style.color = "#667eea";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "transparent";
                  target.style.color = "#64748b";
                }}
              />
            </Tooltip>
          );
        })}
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onFocus={handleFocus}
        onBlur={handleBlur}
        style={{
          minHeight: `${height}px`,
          padding: "16px",
          outline: "none",
          fontSize: "16px",
          lineHeight: "1.6",
          color: "#374151",
          fontFamily:
            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        }}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* Custom styles for placeholder and content */}
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          font-style: italic;
          pointer-events: none;
        }

        [contenteditable] ul,
        [contenteditable] ol {
          margin: 8px 0;
          padding-left: 24px;
        }

        [contenteditable] li {
          margin: 4px 0;
        }

        [contenteditable] p {
          margin: 8px 0;
        }

        [contenteditable]:focus {
          outline: none;
        }

        [contenteditable] strong {
          font-weight: 600;
        }

        [contenteditable] em {
          font-style: italic;
        }

        [contenteditable] u {
          text-decoration: underline;
        }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
