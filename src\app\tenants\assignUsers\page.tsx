"use client";

import React, { useState } from "react";
import { CommonTable } from "@components/common/Table";
import { Input, Button, Avatar, Card, Typography } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { caregiversDatas, usersData } from "@utils/supabase/constants";

const { Title, Text } = Typography;

// Sample data for users

export default function ViewUsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  
  // Filter data based on search term
  const filteredUsers = usersData.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const filteredCaregivers = caregiversDatas.filter(caregiver => 
    caregiver.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // User table columns
  const userColumns = [
    {
      title: "User Name",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text>{text}</Text>
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      align: "center" as const,
      width: 80,
      render: () => (
        <Avatar
          src="/assets/placeholder.png"
          icon={<UserOutlined />}
          style={{ backgroundColor: "#f56a00" }}
        />
      )
    },
    {
      title: "Actions",
      key: "actions",
      align: "center" as const,
      width: 100,
      render: (_: unknown, record: unknown) => {
        const user = record as { id: string };
        return (
          <Button
            type="primary"
            style={{
              backgroundColor: user.id && parseInt(user.id) % 2 === 0 ? "#6366f1" : "#6366f1",
              color: "white",
              border: "none",
              width: "140px"
            }}
          >
            {user.id && parseInt(user.id) % 2 === 0 ? "View Profile" : "Assign Wellness Facilitator"}
          </Button>
        );
      }
    },
  ];

  // Caregiver table columns
  const caregiverColumns = [
    {
      title: "Wellness Facilitator Name",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text>{text}</Text>
    },
    {
      title: "Gender",
      dataIndex: "gender",
      key: "gender",
      align: "center" as const,
      width: 80,
      render: () => (
        <Avatar
          src=""
          icon={<UserOutlined />}
          style={{ backgroundColor: "#f56a00" }}
        />
      )
    },
    {
      title: "Actions",
      key: "actions",
      align: "center" as const,
      width: 100,
      render: (_: unknown, record: unknown) => {
        const caregiver = record as { id: string };
        return (
          <Button
            type="primary"
            style={{
              backgroundColor: "#6366f1",
              color: "white",
              border: "none",
              width: "140px"
            }}
          >
            {caregiver.id && parseInt(caregiver.id) % 2 === 0 ? "View Profile" : "Assign"}
          </Button>
        );
      }
    },
  ];

  return (
    <div style={{ padding: "0 16px" }}>
      {/* Image Section */}
      <div
        style={{
          maxWidth: 1280,
          margin: "24px auto 0",
          background: "#fff",
          borderRadius: 8,
          boxShadow: "0 1px 4px rgba(0,0,0,0.06)",
          padding: 24,
        }}
      >
        
        {/* Main Content Section */}
        <div style={{ paddingTop: 24 }}>
          <div style={{ display: "flex", alignItems: "center", marginBottom: "16px" }}>
            <Input
              placeholder="Search user..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: "200px", marginRight: "16px" }}
            />
            <Text strong>FILTER BY:</Text>
          </div>

          {/* Users Table */}
          <Card 
            title={<Title level={5} style={{ margin: 0 }}>Users</Title>}
            style={{ marginBottom: "24px" }}
            bodyStyle={{ padding: 0 }}
          >
            <CommonTable
              columns={userColumns}
              dataSource={filteredUsers}
              loading={false}
              rowKey="id"
              pagination={false}
              bordered={false}
            />
          </Card>

          {/* Caregivers Table */}
          <Card 
            title={<Title level={5} style={{ margin: 0 }}>Wellness Facilitators</Title>}
            bodyStyle={{ padding: 0 }}
          >
            <CommonTable
              columns={caregiverColumns}
              dataSource={filteredCaregivers}
              loading={false}
              rowKey="id"
              pagination={false}
              bordered={false}
            />
          </Card>
        </div>
      </div>
    </div>
  );
}
