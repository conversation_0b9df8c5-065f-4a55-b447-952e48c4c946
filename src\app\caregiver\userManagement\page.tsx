"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Input,
  Button,
  Typography,
  DatePicker,
  Select,
  Upload,
  Switch,
  Row,
  Col,
  message,
  Spin,
  Layout,
  Card,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { dataProviderInstance } from "@/providers/data-provider"; // Adjust the import path as needed
import { useNotification } from "@refinedev/core";
import { ManageUserFormValues, Game } from "@types";
import { supabaseClient } from "@utils/supabase/client";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
import dayjs from "dayjs";
const { Title, Text } = Typography;
const { TextArea } = Input;
const { Content } = Layout;
export default function UserCreatePage() {
  const [form] = Form.useForm();
  // const [loginRequired, setLoginRequired] = useState(true);
  const [loginRequired] = useState(true);
  const { open } = useNotification();
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [gameOptions, setGameOptions] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);
  const [gameNotes, setGameNotes] = useState<string>("");
  const [gameDueDate, setGameDueDate] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [userRoleId, setUserRoleId] = useState<string>("");
  const [selectedRoleName, setSelectedRoleName] = useState<string>("");
  const [selectedGameId, setSelectedGameId] = useState<string>("");

  const tenantCode = localStorage.getItem("tenant_code");
  const party_id = localStorage.getItem("party_id");
  const tenant_id = localStorage.getItem("tenant_id");

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await dataProviderInstance.listRoles();
        const playerRole = response.data.find(
          (role: { role_name: string }) =>
            role.role_name.toLowerCase() === "player"
        );
        if (playerRole) {
          setUserRoleId(playerRole.id);
          setSelectedRoleName(playerRole.role_name);
          console.log(selectedRoleName);
        }
      } catch (error) {
        console.error("Failed to fetch roles:", error);
      }
    };
    fetchRoles();
    //setLoginRequired(true);
  }, []);
  const fetchUnassignedGames = useCallback(async () => {
    if (!party_id) return;

    try {
      setLoadingGames(true);
      console.log("🎮 Fetching unassigned games for party_id:", party_id);

      const response = await dataProviderInstance.listUnAssignedGames({
        tenant_id: tenant_id || "",
        party_id: party_id,
      });

      if (response && response.data) {
        const games = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];
        setGameOptions(games);
        console.log("✅ Unassigned games fetched:", games);
      } else {
        setGameOptions([]);
      }
    } catch (error) {
      console.error("❌ Error fetching unassigned games:", error);
      message.error(ERROR_MESSAGES.load_games);
      setGameOptions([]);
    } finally {
      setLoadingGames(false);
    }
  }, [party_id]);

  // Fetch unassigned games when party_id is available
  useEffect(() => {
    if (party_id) {
      fetchUnassignedGames();
    }
  }, [party_id, fetchUnassignedGames]);
  const handleFinish = async (values: ManageUserFormValues) => {
    let thumbnailUrl = uploadedImageUrl;
    if (imageFile) {
      const fileName = `players/${Date.now()}_${imageFile.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, imageFile, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) {
        open?.({
          type: "error",
          message: "Failed to upload image.",
        });
        return;
      }

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 157680000); // 1 hour expiry

      if (signedUrlData && signedUrlData.signedUrl) {
        thumbnailUrl = signedUrlData.signedUrl;
      } else {
        console.log(signedUrlError);
        throw new Error("Failed to retrieve signed URL for uploaded image.");
      }
    }

    try {
      let authUserId = null;
      // 1. Call signup API first if login is required
      if (loginRequired) {
        const signUpResponse = await dataProviderInstance.signUpUser({
          email: values.email!,
          password: values.password!,
          first_name: values.firstName!,
          last_name: values.lastName!,
          phone_number: `${values.countryCode!} ${values.phone!}`,
          username: values.username!,
          tenant_code: tenantCode!,
          party_id: party_id!,
          rolename: "PLAYER",
          avatar_url: null,
          party_type_key: "PLAYER",
        });
        if (!signUpResponse.data?.user?.id) {
          throw new Error("Failed to create user. No user ID returned.");
        }
        authUserId = signUpResponse.data.user.id;
        console.log("Auth user created:", authUserId);
      }
      // 3. Prepare params for manageUser, using partyId
      const params = {
        first_name: values.firstName as string,
        last_name: values.lastName as string,
        date_of_birth: values.dob
          ? typeof values.dob === "string"
            ? values.dob
            : values.dob.format("YYYY-MM-DD")
          : null,
        gender: values.gender as string,
        bio: values.bio as string,
        role_id: userRoleId ?? "",
        phone_number: `${values.countryCode} ${values.phone}`,
        tenant_code: tenantCode ?? "",
        profile_url: thumbnailUrl as string,
        is_login_enabled: loginRequired,
        party_id: authUserId ?? "",
        party_type_key: "PLAYER",
      };

      // 4. Call manageUser API
      const result = await dataProviderInstance.manageUser(params);
      // Update auth user metadata if login was enabled
      // if (authUserId && loginRequired) {
      //   const { error: updateError } = await supabaseClient.auth.updateUser({
      //     data: {
      //       role_id: userRoleId,
      //       role_name: 'PLAYER',
      //       party_id: party_id,
      //       tenant_code: tenantCode,
      //     },
      //   });

      //   if (updateError) {
      //     console.error("User update error:", updateError);
      //     // Don't throw here as the main user creation was successful
      //     console.warn(
      //       "Failed to update user metadata, but player was created successfully"
      //     );
      //   } else {
      //     console.log("User metadata updated successfully");
      //   }
      // }
      // Success notification will be shown after game assignment (if any)
      if (!selectedGameId) {
        // Only show this if no game is being assigned
        open?.({
          type: "success",
          message: "PLAYER Created Successfully!",
        });
      }
      // Auto-assign game if one is selected
      if (selectedGameId && authUserId) {
        try {
          await dataProviderInstance.assignGame({
            // party_ids: authUserId ? [authUserId] : [],
            // game_id: selectedGameId,
            // due_date: gameDueDate || undefined,
            // notes: gameNotes || undefined,
            due_date: gameDueDate || null,
            game_ids: selectedGameId ? [selectedGameId] : ([] as string[]),
            notes: gameNotes || null,
            party_ids: authUserId ? [authUserId] : [],
          });
          message.success("Player created and game assigned successfully!");
          // Reset game assignment form
          setSelectedGameId("");
          setGameDueDate("");
          setGameNotes("");
          // Refresh unassigned games list
          fetchUnassignedGames();
        } catch (gameError) {
          console.error("❌ Error assigning game:", gameError);
          message.error(
            "Player created successfully, but failed to assign game"
          );
        }
      } else {
        console.log("ℹ️ No game selected, skipping game assignment");
      }
      // Reset form
      form.resetFields();
      open?.({
        type: "success",
        message: SUCCESS_MESSAGES.create_player,
      });
      console.log("Player created:", result.data);
      //router.push("/dashboard");
    } catch (error) {
      open?.({
        type: "error",
        message: ERROR_MESSAGES.create_player,
      });
      console.error("Failed to create player:", error);
    }
  };
  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          maxWidth: "800px",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            Create New Player
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Add a new player to your care with detailed information and game assignments
          </Text>
        </div>

        {/* Main Form Container */}
        <Card
          style={{
            width: "100%",
            maxWidth: "1000px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "16px",
          }}
          styles={{ body: { padding: "32px" } }}
        >
          {/* Header */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "40px",
              paddingBottom: "24px",
              borderBottom: "1px solid rgba(226, 232, 240, 0.5)",
            }}
          >
            <Title
              level={2}
              style={{
                margin: 0,
                fontWeight: 600,
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
               Player Information
            </Title>
            <Button
              type="text"
              onClick={() => window.history.back()}
              size="large"
              style={{
                color: "#6b7280",
                borderRadius: "12px",
                padding: "8px 16px",
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.background = "rgba(107, 114, 128, 0.1)";
                target.style.color = "#374151";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.background = "transparent";
                target.style.color = "#6b7280";
              }}
            >
              Cancel
            </Button>
          </div>

          {/* Form */}
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFinish}
            style={{ width: "100%" }}
          >
            <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                name="firstName"
                label="First Name"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "First Name is required" }]}
              >
                <Input size="large" placeholder="First Name" />
              </Form.Item>
              <Form.Item
                name="dob"
                label="Date of Birth"
                style={{ marginBottom: 16 }}
                rules={[
                  { required: true, message: "Date of Birth is required" },
                ]}
              >
                <DatePicker
                  size="large"
                  style={{ width: "100%" }}
                  format="YYYY-MM-DD"
                  disabledDate={(current) =>
                    current &&
                    current > dayjs().subtract(18, "year").endOf("day")
                  }
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="lastName"
                label="Last Name"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "Last Name is required" }]}
              >
                <Input size="large" placeholder="Last Name" />
              </Form.Item>
              <Form.Item
                name="gender"
                label="Gender"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "Gender is required" }]}
              >
                <Select size="large" placeholder="Select Gender">
                  <Select.Option value="male">Male</Select.Option>
                  <Select.Option value="female">Female</Select.Option>
                  <Select.Option value="other">Other</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label="Phone Number"
                style={{ marginBottom: 24 }}
                required
              >
                <div style={{ display: "flex" }}>
                  <Form.Item
                    name="countryCode"
                    noStyle
                    rules={[
                      { required: true, message: "Country code is required" },
                    ]}
                    initialValue="+91"
                  >
                    <Select
                      size="large"
                      style={{
                        width: 120,
                        borderRadius: "6px 0 0 6px",
                        fontSize: 16,
                      }}
                    >
                      <Select.Option value="+1">+1 (US)</Select.Option>
                      <Select.Option value="+44">+44 (UK)</Select.Option>
                      <Select.Option value="+91">+91 (IN)</Select.Option>
                      <Select.Option value="+61">+61 (AU)</Select.Option>
                      <Select.Option value="+81">+81 (JP)</Select.Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="phone"
                    noStyle
                    rules={[
                      { required: true, message: "Phone number is required" },
                      {
                        pattern: /^\d{7,14}$/,
                        message: "Enter a valid phone number (7-14 digits)",
                      },
                    ]}
                  >
                    <Input
                      size="large"
                      style={{
                        width: "calc(100% - 120px)",
                        borderRadius: "0 6px 6px 0",
                      }}
                      placeholder="Phone Number"
                      maxLength={14}
                      minLength={7}
                      type="tel"
                    />
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="profileImage"
                label="Profile Image"
                style={{ marginBottom: 16 }}
              >
                <Upload {...uploadProps}>
                  <Button icon={<UploadOutlined />}>Upload</Button>
                </Upload>
                {/* Show progress bar if image is uploading */}
                {imageFile && !uploadedImageUrl && (
                  <div style={{ marginTop: 8 }}>
                    <div
                      style={{
                        width: "100%",
                        background: "#f0f0f0",
                        borderRadius: 4,
                        height: 8,
                        overflow: "hidden",
                      }}
                    >
                      <div
                        style={{
                          width: "100%",
                          height: "100%",
                          background: "#6366f1",
                          animation: "progressBar 1.2s linear infinite",
                        }}
                      />
                    </div>
                    <style>
                      {`
                        @keyframes progressBar {
                          0% { width: 0%; }
                          100% { width: 100%; }
                        }
                        `}
                    </style>
                  </div>
                )}
                {/* Show preview if uploaded */}
                {uploadedImageUrl && (
                  <div
                    style={{
                      marginTop: 12,
                      display: "flex",
                      justifyContent: "flex-start",
                    }}
                  >
                    <div
                      style={{
                        width: 128,
                        height: 128,
                        border: "2px solid #d1d5db",
                        borderRadius: 8,
                        overflow: "hidden",
                        backgroundColor: "#f9fafb",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                      }}
                    >
                      <img
                        src={uploadedImageUrl}
                        alt="Profile Preview"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                    </div>
                  </div>
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item  style={{ marginBottom: 16 }}>
            {/* <Switch checked={loginRequired} onChange={setLoginRequired} /> */}
             <Switch
              checked={true}
              disabled={true}
              //onChange={setLoginRequired}
            />
             <Text style={{
                  margin: 0,
                  fontWeight: 500,
                  color: "#10b981",
                  fontSize: "14px",
                  marginLeft: 16
                }}>
                  Login Enabled
                </Text>
           
          </Form.Item>
          
          {loginRequired && (
            <Row gutter={24}>
              <Col xs={24} md={8}>
                <Form.Item
                  name="username"
                  label="Username"
                  style={{ marginBottom: 16 }}
                  rules={[{ required: true, message: "Username is required" }]}
                >
                  <Input size="large" placeholder="Username" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  name="email"
                  label="Email"
                  style={{ marginBottom: 16 }}
                  rules={[
                    { required: true, message: "Email is required" },
                    {
                      type: "email",
                      message: "Please enter a valid email address",
                    },
                  ]}
                >
                  <Input size="large" placeholder="Email" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  name="password"
                  label="Password"
                  style={{ marginBottom: 16 }}
                  rules={[{ required: true, message: "Password is required" }]}
                >
                  <Input.Password size="large" placeholder="Password" />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Form.Item
            label="Player Bio"
            name="bio"
            rules={[{ required: true, message: "Please enter player bio" }]}
            style={{ marginBottom: 24 }}
          >
            <Input.TextArea placeholder="Add bio..." rows={4} />
          </Form.Item>

          {/* Game Assignment Section */}
          <div style={{ marginTop: "32px", paddingTop: "24px", borderTop: "1px solid rgba(226, 232, 240, 0.5)" }}>
            <Title level={4} style={{ marginBottom: "8px", color: "#374151", fontWeight: 600 }}>
              Game Assignment (Optional)
            </Title>
            <Text
              type="secondary"
              style={{ marginBottom: "24px", display: "block" }}
            >
              Select a game to automatically assign to the player after creation
            </Text>

            <Row gutter={16}>
              <Col xs={24} md={24}>
                <div style={{ marginBottom: 16 }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Select Game
                  </label>
                  <Select
                    size="large"
                    placeholder="Select a game to assign"
                    value={selectedGameId}
                    onChange={(value) => {
                      console.log("🎮 Game selected:", value);
                      setSelectedGameId(value);
                    }}
                    loading={loadingGames}
                    style={{ width: "100%" }}
                    options={gameOptions.map((game) => ({
                      label: game.game_name,
                      value: game.game_id,
                    }))}
                    notFoundContent={
                      loadingGames ? (
                        <Spin size="small" />
                      ) : (
                        "No games available"
                      )
                    }
                  />
                </div>
              </Col>

              <Col xs={24} md={5}>
                <div style={{ marginBottom: 16 }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Due Date
                  </label>
                  <DatePicker
                    size="large"
                    style={{ width: "100%" }}
                    placeholder="Select due date"
                    value={gameDueDate ? dayjs(gameDueDate) : null}
                    onChange={(_, dateString) => {
                      if (typeof dateString === "string")
                        setGameDueDate(dateString);
                      else setGameDueDate("");
                    }}
                    disabledDate={(current) =>
                      current && current < dayjs().startOf("day")
                    }
                  />
                </div>
              </Col>
              <Col xs={24} sm={19}>
                <div>
                  <Text
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Notes <span style={{ color: "red" }}>*</span>
                  </Text>
                  <TextArea
                    placeholder="Enter notes"
                    value={gameNotes}
                    onChange={(e) => setGameNotes(e.target.value)}
                    rows={4}
                    style={{ width: "100%" }}
                  />
                </div>
              </Col>
            </Row>
          </div>

          <div style={{ textAlign: "center", marginTop: "48px" }}>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                style={{
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                  height: "56px",
                  fontSize: "16px",
                  fontWeight: 600,
                  borderRadius: "12px",
                  boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                  transition: "all 0.3s ease",
                  minWidth: "200px",
                  padding: "0 32px",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-2px)";
                  target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                }}
              >
                 Create Player
              </Button>
            </div>
          </Form>
        </Card>
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .form-container {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>
    </Layout>
  );
}
