"use client";
import { dataProvider } from "@refinedev/supabase";
import {
  ManageUserRequest,
  AddTenantRequest,
  AssignGamesRequest,
  DashboardSummaryParams,
  GameFormData,
  CreateRewardRequest,
  SignUpUserParams,
  SignUpUserResponse,
  ListTenantUsersRequest,
  ListTenantGamesRequest,
  GetUserDashboardRequest,
} from "@types";
import { supabaseClient } from "@utils/supabase/client";
import { RPC, VIEWS } from "../../../apiConfig";

export const dataProviderInstance = {
  ...dataProvider(supabaseClient),

  manageUser: async (params: ManageUserRequest) => {
    const { data, error } = await supabaseClient.rpc(RPC.manageUser, params);

    if (error) {
      throw error;
    }
    return { data };
  },
  listTenants: async () => {
    const { data, error } = await supabaseClient.rpc(RPC.listTenant);

    if (error) {
      throw error;
    }
    return { data };
  },
  listAllUnassignedTenantByGame: async (params: { game_id: string }) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.listAllUnassignedTenantByGame,
      params
    );
    if (error) {
      throw error;
    }
    return { data };
  },
  getUserInfo: async (userId: string) => {
    const { data, error } = await supabaseClient.rpc(RPC.getUserInfo, {
      user_id: userId,
    });
    if (error) {
      throw error;
    }
    return { data };
  },

  getUser: async () => {
    try {
      const {
        data: { user },
        error,
      } = await supabaseClient.auth.getUser();

      if (error) {
        throw error;
      }
      return { data: user };
    } catch (error) {
      throw error;
    }
  },

  getProfileDetails: async (params: { party_id: string }) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.getProfileDetails,
      params
    );

    if (error) {
      throw error;
    }
    return { data };
  },
  updateProfileInfo: async (params: {
    user_id: string;
    avatar_url: string;
  }) => {
    const { data, error } = await supabaseClient.rpc(RPC.updateProfileInfo, {
      user_id: params.user_id,
      avatar_url: params.avatar_url,
    });

    if (error) {
      console.error("Supabase error in updateProfileInfo:", error);
      throw error;
    }
    return { data };
  },

  listGameCategories: async () => {
    const { data, error } = await supabaseClient
      .from(VIEWS.listGameCategories)
      .select("id, name");

    if (error) {
      throw error;
    }
    return { data };
  },

  listRewardCategories: async () => {
    const { data, error } = await supabaseClient
      .from(VIEWS.listRewardCategories)
      .select("*");

    if (error) {
      console.error("Supabase error in listRewardCategories:", error);
      throw error;
    }
    return { data };
  },

  createReward: async (params: CreateRewardRequest) => {
    const { data, error } = await supabaseClient.rpc(RPC.createReward, params);

    if (error) {
      console.error("Supabase error in createReward:", error);
      throw error;
    }
    return { data };
  },

  listRewards: async () => {
    const { data, error } = await supabaseClient
      .from(VIEWS.listRewards)
      .select("*");
    if (error) {
      console.error("Supabase error in listRewards:", error);
      throw error;
    }
    return { data };
  },

  insertGame: async (params: GameFormData) => {
    const { data, error } = await supabaseClient.rpc(RPC.insertGame, params);
    if (error) throw error;
    return { data };
  },
  getUserDashboard: async (params: GetUserDashboardRequest) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.getUserDashboard,
      params
    );

    if (error) {
      console.error("Supabase error in getUserDashboard:", error);
      throw error;
    }
    return { data };
  },
  uploadImage: async (file: File, fileName: string) => {
    const { data, error } = await supabaseClient.storage
      .from("image-bucket")
      .upload(fileName, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) {
      throw error;
    }

    const { data: publicUrlData } = supabaseClient.storage
      .from("image-bucket")
      .getPublicUrl(fileName);

    return {
      data: {
        ...data,
        publicUrl: publicUrlData.publicUrl,
      },
    };
  },
  getNextTenantCode: async () => {
    const { data, error } = await supabaseClient.rpc(RPC.getNextTenantCode);
    if (error) {
      throw error;
    }
    return { data };
  },
  addTenent: async (params: AddTenantRequest) => {
    const { data, error } = await supabaseClient.rpc(RPC.addTenant, params);
    if (error) {
      throw error;
    }
    return { data };
  },
  getAdminDashboardData: async () => {
    const { data, error } = await supabaseClient.rpc(RPC.adminDashboard);
    if (error) {
      throw error;
    }
    return { data };
  },

  getTenantDashboardSummary: async (params: DashboardSummaryParams) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.getTenantDashboardSummary,
      params
    );
    if (error) {
      throw error;
    }
    return { data };
  },
  listGames: async () => {
    const { data, error } = await supabaseClient.rpc(RPC.listGame);
    if (error) {
      throw error;
    }
    return { data };
  },
  listTenantGames: async (params: ListTenantGamesRequest) => {
    const { data, error } = await supabaseClient.rpc(RPC.listTenantGames, params);
    if (error) {
      throw error;
    }
    return { data };
  },
  listUsers: async () => {
    const { data, error } = await supabaseClient.rpc(RPC.listUsers);

    if (error) {
      throw error;
    }

    return { data };
  },
  listTenantUsers: async (params: ListTenantUsersRequest) => {
    const { data, error } = await supabaseClient.rpc(RPC.listTenantUsers, params);

    if (error) {
      throw error;
    }

    return { data };
  },
  assignGame: async (params: AssignGamesRequest) => {
    const { data, error } = await supabaseClient.rpc(RPC.assignGames, params);
    if (error) throw error;
    return { data };
  },

  assignGameToTenant: async (params: {
    party_ids: string[];
    game_id: string;
    due_date?: string;
    notes?: string;
  }) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.assignGameToTenant,
      params
    );
    if (error) throw error;
    return { data };
  },

  fetchGamedetails: async (params: { game_id: string }) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.fetchGamedetails,
      params
    );
    if (error) {
      throw error;
    }
    return { data };
  },
  listRoles: async () => {
    const { data, error } = await supabaseClient.from(VIEWS.listRoles).select();

    if (error) {
      throw error;
    }
    return { data };
  },
  listCareGiver: async (params: {
    tenant_code: string;
    party_id: string;
    party_type_key: string;
  }) => {
    const { data, error } = await supabaseClient.rpc(RPC.listCareGiver, params);
    if (error) {
      throw error;
    }
    return { data };
  },
  assignCareTaker: async (params: {
    party_id: string;
    caregiver_ids: string[];
  }) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.assignCareTaker,
      params
    );
    if (error) throw error;
    return { data };
  },

  signUpUser: async (
  params: SignUpUserParams
): Promise<{ data: SignUpUserResponse }> => {
  const response = await fetch(RPC.signUp, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
      Authorization: "Bearer " + localStorage.getItem("ACCESS_TOKEN"),
    },
    body: JSON.stringify({
      email: params.email,
      password: params.password,
      data: {
        first_name: params.first_name,
        last_name: params.last_name,
        phone_number: params.phone_number,
        username: params.username,
        tenant_code: params.tenant_code,
        party_id: params.party_id,
        avatar_url: params.avatar_url,
        party_type_key: params.party_type_key,
        rolename: params.rolename,
      },
    }),
  });

  if (!response.ok) {
    if (response.status === 500) {
      throw new Error("User domain not supported! Please contact admin.");
    } else {
      throw new Error("User already exists or cannot be created.");
    }
  }

  const data: SignUpUserResponse = await response.json();
  return { data };
},

 
  listUnAssignedGames: async (params: { tenant_id: string, party_id: string }) => {
    const { data, error } = await supabaseClient.rpc(
      RPC.listUnAssignedGames,
      params
    );
    if (error) {
      throw error;
    }
    return { data };
  },
};

export type DataProvider = typeof dataProviderInstance;
