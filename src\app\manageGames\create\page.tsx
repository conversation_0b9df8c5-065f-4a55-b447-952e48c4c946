"use client";
import React, { useState, useEffect } from "react";
import { DeleteOutlined } from "@ant-design/icons";
import {
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  DatePicker,
  Spin,
  message,
  Upload,
  Layout,
  Table,
  InputNumber,
} from "antd";
import Image from "next/image";
import {
  CloseOutlined,
  EditOutlined,
  PlayCircleOutlined,
  TeamOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { supabaseClient } from "@utils/supabase/client";
import { BackButton } from "@components/common";
import {
  CategoryItem,
  CategoryOption,
  GameFormData,
  ListTenantResponse,
  ListTenantsResponse,
  TenantOption,
} from "@types";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
import { dataProviderInstance } from "@providers/data-provider";
import { TableColumnsType } from "antd";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import RichTextEditor from "@components/RichTextEditor";
const { Text, Title } = Typography;
const { TextArea } = Input;
const { Content } = Layout;
interface LevelInput {
  level_title?: string;
  level_order?: number;
  time_limit?: number;
  score_required?: number;
  duration?: number;
  difficulty_level?: string;
  badge_id?: string;
  errors?: {
    [field: string]: string;
  };
}
export default function AddGamePage() {
  const [form] = Form.useForm();
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [tenantData, setTenantData] = useState<ListTenantResponse>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [tenantOptions, setTenantOptions] = useState<TenantOption[]>([]);
  const [loadingBadges, setLoadingBadges] = useState(true);
  const [selectedParties, setSelectedParties] = useState<string[]>([]);
  const [notes, setNotes] = useState<string>("");
  const [dueDate, setDueDate] = useState<string>("");
  const [badgeOptions, setBadgeOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [levelsData, setLevelsData] = useState<LevelInput[]>([]);
  const [levelCount, setLevelCount] = useState(0);
  const router = useRouter();
  const difficultyOptions = [
    { label: "Easy", value: "easy" },
    { label: "Medium", value: "medium" },
    { label: "Hard", value: "hard" },
  ];

  const levelColumns: TableColumnsType<LevelInput> = [
    {
      title: "Level No",
      dataIndex: "level_no",
      render: (_: unknown, __: LevelInput, index: number) => index + 1,
    },
    {
      title: "Level Title",
      dataIndex: "level_title",
      render: (_: unknown, record: LevelInput, index: number) => (
        <Input
          placeholder="Enter title"
          value={record.level_title}
          onChange={(e) =>
            handleLevelChange(index, "level_title", e.target.value)
          }
          status={!record.level_title ? "error" : undefined}
        />
      ),
    },
    {
      title: "Level Order",
      dataIndex: "level_order",
      width: 130,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Input
          type="number"
          placeholder="Order"
          value={record.level_order}
          onChange={(e) =>
            handleLevelChange(index, "level_order", Number(e.target.value))
          }
          status={!record.level_order ? "error" : undefined}
        />
      ),
    },
    {
      title: "Duration",
      dataIndex: "duration",
      key: "duration",
      width: 130,
      responsive: ["md"],

      render: (_: unknown, record: LevelInput, index: number) => (
        <InputNumber
          min={0}
          value={record.duration}
          status={!record.duration ? "error" : undefined}
          onChange={(value) => handleLevelChange(index, "duration", value || 0)}
        />
      ),
    },
    {
      title: "Difficulty Level",
      dataIndex: "difficulty_level",
      key: "difficulty_level",
      width: 150,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Select
          options={difficultyOptions}
          value={record.difficulty_level}
          status={!record.difficulty_level ? "error" : undefined}
          onChange={(value) =>
            handleLevelChange(index, "difficulty_level", value)
          }
        />
      ),
    },
    {
      title: "Score Required",
      dataIndex: "score_required",
      width: 150,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Input
          type="number"
          placeholder="Score"
          value={record.score_required}
          status={!record.score_required ? "error" : undefined}
          onChange={(e) =>
            handleLevelChange(index, "score_required", Number(e.target.value))
          }
        />
      ),
    },
    {
      title: "Badge",
      dataIndex: "badge_id",
      key: "badge_id",
      width: 160,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Select
          placeholder="Select badge"
          options={badgeOptions}
          status={!record.badge_id ? "error" : undefined}
          loading={loadingBadges}
          onChange={(value) => handleLevelChange(index, "badge_id", value)}
          style={{
            borderRadius: 12,
            fontSize: 16,
            width: "100%",
          }}
        />
      ),
    },
    {
      title: "",
      key: "action",
      width: 50, // Optional: limits the column width
      render: (_: unknown, __: LevelInput, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeLevel(index)}
        />
      ),
    },
  ];
  useEffect(() => {
    setLoadingTenants(true);
    dataProviderInstance
      .listTenants()
      .then((response: ListTenantsResponse) => {
        const tenants = response.data.data;
        setTenantData(tenants);

        const formattedTenants: TenantOption[] = tenants.map(
          (tenant: {
            id: string | number;
            tenant_name: string;
            name?: string;
          }) => ({
            label:
              tenant.name || tenant.tenant_name || `Organization ${tenant.id}`,
            value: tenant.id.toString(),
          })
        );

        setTenantOptions(formattedTenants);
      })
      .catch((error: Error) => {
        console.error("Error fetching organizations:", error);

        message.error(ERROR_MESSAGES.failed_loadtenants);
        setTenantData([]);
        setTenantOptions([]);
      })
      .finally(() => {
        setLoadingTenants(false);
      });
  }, []);

  useEffect(() => {
    setLoadingCategories(true);
    dataProviderInstance
      .listGameCategories()
      .then((response: { data: CategoryItem[] }) => {
        if (!response || !response.data) {
          throw new Error("No category data received");
        }

        const formatted: CategoryOption[] = response.data.map(
          (item: CategoryItem) => ({
            label: item.name,
            value: item.id,
          })
        );

        setCategoryOptions(formatted);
      })
      .catch((error: Error) => {
        console.error("Error fetching categories:", error);
        message.error(ERROR_MESSAGES.load_categories);
        setCategoryOptions([]);
      })
      .finally(() => {
        setLoadingCategories(false);
      });
    setLoadingBadges(true);
    dataProviderInstance
      .listBadges()
      .then((response: { data: { id: string; name: string }[] }) => {
        if (!response || !response.data)
          throw new Error("No badge data received");
        setBadgeOptions(
          response.data.map((reward) => ({
            label: reward.name,
            value: reward.id,
          }))
        );
      })
      .catch(() => {
        message.error("Failed to load rewards");
        setBadgeOptions([]);
      })
      .finally(() => setLoadingBadges(false));
  }, []);
  const validateLevels = (): boolean => {
    const hasErrors = levelsData.some((level, index) => {
      const missingFields = [];

      if (!level.level_title) missingFields.push("Level Title");
      if (level.level_order === undefined || level.level_order === null)
        missingFields.push("Level Order");
      if (level.duration === undefined || level.duration === null)
        missingFields.push("Duration");
      if (!level.difficulty_level) missingFields.push("Difficulty Level");
      if (level.score_required === undefined || level.score_required === null)
        missingFields.push("Score Required");
      if (!level.badge_id) missingFields.push("Badge");

      if (missingFields.length > 0) {
        message.error(
          `Level ${index + 1} is missing: ${missingFields.join(", ")}`
        );
        return true;
      }
      return false;
    });

    return !hasErrors;
  };
  const handleSubmit = async (values: GameFormData) => {
    try {
      if (!validateLevels()) return;
      setSubmitting(true);

      // Use existing URL or default
      let thumbnailUrl = uploadedImageUrl;

      // If a new file is selected, upload it
      if (imageFile) {
        const fileName = `games/${Date.now()}_${imageFile.name}`;

        const { error: uploadError } = await supabaseClient.storage
          .from("image-bucket")
          .upload(fileName, imageFile, {
            cacheControl: "3600",
            upsert: true,
          });

        if (uploadError) {
          throw new Error(`Image upload failed: ${uploadError.message}`);
        }

        const { data: signedUrlData, error: signedUrlError } =
          await supabaseClient.storage
            .from("image-bucket")
            .createSignedUrl(fileName, 60 * 60 * 24 * 365); // 1 year

        if (signedUrlError || !signedUrlData?.signedUrl) {
          throw new Error("Failed to generate signed image URL.");
        }

        thumbnailUrl = signedUrlData.signedUrl;
      }

      // Final fallback if still missing
      if (!thumbnailUrl) {
        thumbnailUrl =
          "https://www.shutterstock.com/shutterstock/photos/1432985741/display_1500/stock-vector-counting-games-for-kids-and-adults-educational-math-game-result-crossword-for-social-networks-1432985741.jpg";
      }

      // Prepare final payload
      const gameData = {
        category_id: values.category,
        name: values.title,
        description: values.description,
        rules: values.rules ?? "",
        // difficulty_level: values.difficulty ?? "medium",
        // estimated_duration_sec: values.duration,
        thumbnail_url: thumbnailUrl,
        levels: levelsData.map((level, idx) => ({
          level_number: idx + 1,
          title: level.level_title,
          level_order: parseInt(level.level_order?.toString() ?? "0", 10),
          duration: parseInt(level.duration?.toString() ?? "0", 10),
          difficulty_level: level.difficulty_level,
          badge_id: level.badge_id,
          target_score: parseInt(level.score_required?.toString() ?? "0", 10),
        })),
        valid_from: values.valid_from.format("YYYY-MM-DDTHH:mm:ssZ"),
        valid_to: values.valid_upto.format("YYYY-MM-DDTHH:mm:ssZ"),
      };
      // Call the database RPC function
      const { data: responseData, error } = await supabaseClient.rpc(
        "fn_insert_game",
        gameData
      );

      // ✅ After successful insert, assign game to tenants
      if (error || !responseData?.game_id) {
        throw new Error(
          `Database error: ${error?.message ?? "Game ID not returned"}`
        );
      } else if (!selectedParties.length) {
        message.error(ERROR_MESSAGES.select_parties);
        return;
      } else {
        const gameId = responseData.game_id;
        try {
          setSubmitting(true);

          await dataProviderInstance.assignGameToTenant({
            party_ids: selectedParties,
            game_id: gameId, // use the returned ID
            due_date: dueDate,
            notes: notes,
          });
          message.success(SUCCESS_MESSAGES.selected_parties);
          setSelectedParties([]);

          setDueDate("");
          setNotes("");
        } catch {
          message.error(ERROR_MESSAGES.assign_game_to_parties);
        } finally {
          setSubmitting(false);
        }
      }

      // Success flow
      message.success(SUCCESS_MESSAGES.create_game);
      router.push("/manageGames");

      // Reset form state
      form.resetFields();
      form.setFieldsValue({ valid_from: dayjs() });
      setUploadedImageUrl("");
      setImageFile(null);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create game.";
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  const handleLevelChange = <K extends keyof LevelInput>(
    index: number,
    field: K,
    value: LevelInput[K]
  ) => {
    const updated = [...levelsData];
    updated[index][field] = value;
    setLevelsData(updated);
  };

  const removeLevel = (index: number) => {
    const updated = [...levelsData];
    updated.splice(index, 1);
    setLevelsData(updated);
    setLevelCount(updated.length);
  };
  const addNewLevel = () => {
    setLevelsData([
      ...levelsData,
      {
        level_title: "",
        level_order: levelCount + 1,
        time_limit: 0,
        score_required: 0,
        duration: 0,
        difficulty_level: "Easy",
        badge_id: "",
      },
    ]);
    setLevelCount(levelCount + 1);
  };

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "450px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          maxWidth: 1280,
          margin: "0 auto",
          width: "100%",
          position: "relative",
          zIndex: 1,
        }}
      >
        <Content
          style={{
            maxWidth: 1280,
            margin: "0 auto",
            padding: "20px 20px",
            position: "relative",
            zIndex: 1,
          }}
        >
          {/* Header Section */}
          <div
            style={{
              textAlign: "center",
              marginBottom: "48px",
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              borderRadius: "24px",
              padding: "20px 20px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "16px",
                marginBottom: "16px",
              }}
            >
              <BackButton
                to="/manageGames"
                title="Back to Exercise Management"
              />
              <Title
                level={1}
                style={{
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  fontWeight: 700,
                  margin: 0,
                  fontSize: "36px",
                  letterSpacing: "-1px",
                }}
              >
                Create New Exercise
              </Title>
            </div>
            <Text
              style={{
                color: "#6b7280",
                fontSize: "18px",
                fontWeight: 500,
                display: "block",
                lineHeight: "1.6",
              }}
            >
              Design and configure a new exercise for your platform
            </Text>
          </div>

          {/* Main Form Container */}
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "24px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              padding: "20px",
              position: "relative",
            }}
          >
            <Form
              form={form}
              layout="vertical"
              size="large"
              onFinish={handleSubmit}
              initialValues={{
                valid_from: dayjs(),
              }}
            >
              <div
                style={{
                  marginBottom: 20,
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "20px",
                  padding: "20px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "12px",
                    marginBottom: "20px",
                  }}
                >
                  <div
                    style={{
                      width: "48px",
                      height: "48px",
                      borderRadius: "12px",
                      background:
                        "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <PlayCircleOutlined
                      style={{ color: "white", fontSize: "20px" }}
                    />
                  </div>
                  <Title
                    level={3}
                    style={{
                      margin: 0,
                      color: "#374151",
                      fontWeight: 700,
                      fontSize: "24px",
                    }}
                  >
                    Exercise Information
                  </Title>
                </div>

                <Row gutter={32}>
                  {/* Left Column - Form Fields */}
                  <Col xs={24} lg={14}>
                    {/* First Row: Category and Title */}
                    <Row gutter={[24, 8]}>
                      <Col xs={24} md={12}>
                        <Form.Item
                          label={
                            <Text
                              style={{
                                color: "#374151",
                                fontWeight: 600,
                                fontSize: "14px",
                                marginBottom: "8px",
                                display: "block",
                              }}
                            >
                              Select Category
                            </Text>
                          }
                          name="category"
                          style={{ marginBottom: 12 }}
                          rules={[
                            {
                              required: true,
                              message: "Please select category",
                            },
                          ]}
                        >
                          <Select
                            placeholder="Select exercise category"
                            options={categoryOptions}
                            loading={loadingCategories}
                            style={{
                              borderRadius: "12px",
                              height: "50px",
                              fontSize: "16px",
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col xs={24} md={12}>
                        <Form.Item
                          label={
                            <Text
                              style={{
                                color: "#374151",
                                fontWeight: 600,
                                fontSize: "14px",
                                marginBottom: "8px",
                                display: "block",
                              }}
                            >
                              Title
                            </Text>
                          }
                          name="title"
                          style={{ marginBottom: 12 }}
                          rules={[
                            {
                              required: true,
                              message: "Please enter exercise title",
                            },
                          ]}
                        >
                          <Input
                            placeholder="Enter exercise title"
                            style={{
                              borderRadius: "12px",
                              height: "50px",
                              border: "2px solid #e5e7eb",
                              fontSize: "16px",
                              transition: "all 0.3s ease",
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = "#667eea";
                              e.target.style.boxShadow =
                                "0 0 0 3px rgba(102, 126, 234, 0.1)";
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = "#e5e7eb";
                              e.target.style.boxShadow = "none";
                            }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[24, 8]}>
                      <Col xs={24} md={12}>
                        <Form.Item
                          label={
                            <Text
                              style={{
                                color: "#374151",
                                fontWeight: 600,
                                fontSize: "14px",
                                marginBottom: "8px",
                                display: "block",
                              }}
                            >
                              Valid From Date
                            </Text>
                          }
                          name="valid_from"
                          style={{ marginBottom: 12 }}
                          rules={[
                            {
                              required: true,
                              message: "Please select valid from date",
                            },
                          ]}
                        >
                          <DatePicker
                            placeholder="Select valid from date"
                            style={{
                              width: "100%",
                              height: "50px",
                              borderRadius: "12px",
                              border: "2px solid #e5e7eb",
                              fontSize: "16px",
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col xs={24} md={12}>
                        <Form.Item
                          label={
                            <Text
                              style={{
                                color: "#374151",
                                fontWeight: 600,
                                fontSize: "14px",
                                marginBottom: "8px",
                                display: "block",
                              }}
                            >
                              Valid Until Date
                            </Text>
                          }
                          name="valid_upto"
                          style={{ marginBottom: 12 }}
                          rules={[
                            {
                              required: true,
                              message: "Please select valid until date",
                            },
                            ({ getFieldValue }) => ({
                              validator(_, value) {
                                const validFrom = getFieldValue("valid_from");
                                if (
                                  !value ||
                                  !validFrom ||
                                  value.isAfter(validFrom)
                                ) {
                                  return Promise.resolve();
                                }
                                return Promise.reject(
                                  new Error(
                                    "Valid upto date must be after valid from date"
                                  )
                                );
                              },
                            }),
                          ]}
                        >
                          <DatePicker
                            placeholder="Select valid until date"
                            style={{
                              width: "100%",
                              height: "50px",
                              borderRadius: "12px",
                              border: "2px solid #e5e7eb",
                              fontSize: "16px",
                            }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row>
                      <Col xs={24}>
                        <Form.Item
                          label={
                            <Text
                              style={{
                                color: "#374151",
                                fontWeight: 600,
                                fontSize: "14px",
                                marginBottom: "8px",
                                display: "block",
                              }}
                            >
                              Description
                            </Text>
                          }
                          name="description"
                          style={{ marginBottom: 12 }}
                          rules={[
                            {
                              required: true,
                              message: "Please enter exercise description",
                            },
                          ]}
                        >
                          <Input.TextArea
                            placeholder="Enter detailed exercise description"
                            rows={4}
                            style={{
                              width: "100%",
                              borderRadius: "12px",
                              border: "2px solid #e5e7eb",
                              fontSize: "16px",
                              resize: "none",
                              transition: "all 0.3s ease",
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = "#667eea";
                              e.target.style.boxShadow =
                                "0 0 0 3px rgba(102, 126, 234, 0.1)";
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = "#e5e7eb";
                              e.target.style.boxShadow = "none";
                            }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>

                  <Col xs={24} lg={10}>
                    <div
                      style={{
                        background:
                          "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                        borderRadius: "16px",
                        padding: "20px",
                        border: "1px solid rgba(226, 232, 240, 0.8)",
                        boxShadow: "0 12px 24px rgba(0, 0, 0, 0.04)",
                        height: "95%",
                        minHeight: "360px",
                        position: "relative",
                        overflow: "hidden",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",

                          gap: "12px",
                          marginBottom: "20px",

                          position: "relative",
                          zIndex: 1,
                        }}
                      >
                        <div
                          style={{
                            width: "38px",
                            height: "38px",
                            borderRadius: "10px",
                            background:
                              "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            boxShadow: "0 8px 16px rgba(102, 126, 234, 0.2)",
                          }}
                        >
                          <UploadOutlined
                            style={{ color: "white", fontSize: "16px" }}
                          />
                        </div>
                        <div>
                          <Title
                            level={5}
                            style={{
                              margin: 0,
                              color: "#1f2937",
                              fontWeight: 700,
                              fontSize: "16px",
                              marginBottom: "2px",
                            }}
                          >
                            Exercise Thumbnail
                          </Title>
                          <Text
                            style={{
                              color: "#6b7280",
                              fontSize: "13px",
                              fontWeight: 500,
                            }}
                          >
                            Upload an engaging preview image
                          </Text>
                        </div>
                      </div>

                      <div
                        style={{
                          height: 180,
                          borderRadius: "12px",
                          position: "relative",

                          background: uploadedImageUrl
                            ? "transparent"
                            : "linear-gradient(135deg, #f8fafc, #f1f5f9)",
                          overflow: "hidden",
                          border: uploadedImageUrl
                            ? "2px solid #10b981"
                            : "2px dashed #cbd5e1",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          zIndex: 1,

                          marginBottom: "16px",
                          transition: "all 0.3s ease",
                        }}
                        onMouseEnter={(e) => {
                          if (!uploadedImageUrl) {
                            e.currentTarget.style.borderColor = "#667eea";
                            e.currentTarget.style.background =
                              "linear-gradient(135deg, #f0f4ff, #e0e7ff)";
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!uploadedImageUrl) {
                            e.currentTarget.style.borderColor = "#cbd5e1";
                            e.currentTarget.style.background =
                              "linear-gradient(135deg, #f8fafc, #f1f5f9)";
                          }
                        }}
                      >
                        {uploadedImageUrl ? (
                          <>
                            <Image
                              src={uploadedImageUrl}
                              alt="Exercise Thumbnail"
                              fill
                              style={{ objectFit: "cover" }}
                              priority
                            />
                          </>
                        ) : (
                          <div
                            style={{ textAlign: "center", color: "#64748b" }}
                          >
                            <div
                              style={{
                                width: "60px",
                                height: "60px",
                                background:
                                  "linear-gradient(135deg, #e2e8f0, #cbd5e1)",
                                borderRadius: "50%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                margin: "0 auto 10px",
                              }}
                            >
                              <UploadOutlined
                                style={{ fontSize: "22px", color: "#94a3b8" }}
                              />
                            </div>
                            <Text
                              style={{
                                display: "block",
                                fontSize: "15px",
                                fontWeight: 600,
                                marginBottom: "4px",
                                color: "#475569",
                              }}
                            >
                              Drop your image here
                            </Text>
                            <Text
                              style={{ fontSize: "13px", color: "#94a3b8" }}
                            >
                              or click to browse
                            </Text>
                          </div>
                        )}

                        <Upload {...uploadProps}>
                          <Button
                            icon={<EditOutlined />}
                            style={{
                              position: "absolute",
                              top: "14px",
                              right: "14px",
                              background: "#fff",
                              backdropFilter: "blur(8px)",
                              border: "1px solid rgba(102, 126, 234, 0.3)",
                              color: "#667eea",
                              borderRadius: "12px",
                              height: "38px",
                              fontWeight: 600,
                              fontSize: "13px",
                              boxShadow: "0 4px 16px rgba(0, 0, 0, 0.1)",
                              zIndex: 10,
                              transition: "all 0.3s ease",
                            }}
                            onMouseEnter={(e) => {
                              const target = e.currentTarget;
                              target.style.background = "#667eea";
                              target.style.color = "white";
                              target.style.transform = "translateY(-1px)";
                              target.style.boxShadow =
                                "0 6px 18px rgba(102, 126, 234, 0.4)";
                              const icon = target.querySelector("svg");
                              if (icon) icon.style.color = "white";
                            }}
                            onMouseLeave={(e) => {
                              const target = e.currentTarget;
                              target.style.background = "#fff";
                              target.style.color = "#667eea";
                              target.style.transform = "translateY(0)";
                              target.style.boxShadow =
                                "0 4px 16px rgba(0, 0, 0, 0.1)";
                              const icon = target.querySelector("svg");
                              if (icon) icon.style.color = "#667eea";
                            }}
                          ></Button>
                        </Upload>
                      </div>

                      <div
                        style={{
                          padding: "10px",
                          background: "rgba(59, 130, 246, 0.04)",
                          borderRadius: "14px",
                          border: "1px solid rgba(59, 130, 246, 0.08)",
                          position: "relative",
                          zIndex: 1,
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                            marginBottom: "4px",
                          }}
                        >
                          <div
                            style={{
                              width: "6px",
                              height: "6px",
                              background: "#3b82f6",
                              borderRadius: "50%",
                            }}
                          />
                          <Text
                            style={{
                              color: "#059669",
                              fontSize: "12px",
                              fontWeight: 600,
                            }}
                          >
                            Recommended: 1200×600px
                          </Text>
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                          }}
                        >
                          <div
                            style={{
                              width: "6px",
                              height: "6px",
                              background: "#10b981",
                              borderRadius: "50%",
                            }}
                          />
                          <Text
                            style={{
                              color: "#059669",
                              fontSize: "12px",
                              fontWeight: 600,
                            }}
                          >
                            Max size: 5MB • JPG, PNG, WebP
                          </Text>
                        </div>
                      </div>
                    </div>
                  </Col>
                </Row>

                <Row style={{ marginTop: 24 }}>
                  <Col xs={24}>
                    <Form.Item
                      label={
                        <span
                          style={{
                            color: "#374151",
                            fontWeight: 600,
                            fontSize: "14px",
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                          }}
                        >
                          Exercise Rules & Instructions
                        </span>
                      }
                      name="rules"
                      style={{ marginBottom: 24 }}
                      rules={[
                        {
                          required: true,
                          message:
                            "Please enter exercise rules and instructions",
                        },
                      ]}
                    >
                      <RichTextEditor
                        placeholder="Enter detailed rules, instructions, and guidelines for this exercise..."
                        height={180}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col xs={24} md={24}>
                    <Form.Item
                      label={
                        <Text
                          style={{
                            color: "#374151",
                            fontWeight: 600,
                            fontSize: "14px",
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          Game Levels
                        </Text>
                      }
                    >
                      {levelsData.length === 0 && (
                        <Button
                          type="dashed"
                          onClick={addNewLevel}
                          block
                          icon="+"
                        >
                          Add Level
                        </Button>
                      )}
                      {levelsData.length > 0 && (
                        <Table
                          dataSource={levelsData}
                          columns={levelColumns}
                          pagination={false}
                          rowKey={(_, index) => index!.toString()}
                          style={{ marginTop: 16, minWidth: 200 }}
                          //scroll={{ x: "max-content" }}
                        />
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              <div
                style={{
                  marginBottom: 20,
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "20px",
                  padding: "20px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "12px",
                    marginBottom: "20px",
                  }}
                >
                  <div
                    style={{
                      width: "48px",
                      height: "48px",
                      borderRadius: "12px",
                      background:
                        "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <TeamOutlined
                      style={{ color: "white", fontSize: "20px" }}
                    />
                  </div>
                  <Title
                    level={3}
                    style={{
                      margin: 0,
                      color: "#374151",
                      fontWeight: 700,
                      fontSize: "24px",
                    }}
                  >
                    Assign to Organizations
                  </Title>
                </div>
                <Row gutter={32}>
                  <Col xs={24}>
                    <Form.Item
                      label={
                        <Text
                          style={{
                            color: "#374151",
                            fontWeight: 600,
                            fontSize: "14px",
                          }}
                        >
                          Assign to Organizations
                        </Text>
                      }
                      name="tenants"
                      style={{ marginBottom: 12 }}
                      // rules={[
                      //   {
                      //     required: true,
                      //     message: "Please select at least one organization",
                      //   },
                      // ]}
                    >
                      <Select
                        mode="multiple"
                        placeholder="Select Organizations to assign this exercise"
                        options={tenantOptions}
                        onChange={(values) => setSelectedParties(values)}
                        loading={loadingTenants}
                        style={{
                          minHeight: "48px",
                        }}
                        notFoundContent={
                          loadingTenants ? (
                            <Spin size="small" />
                          ) : (
                            "No organizations found"
                          )
                        }
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Text
                  style={{
                    color: "#6b7280",
                    fontSize: "12px",
                    fontStyle: "italic",

                    marginTop: "8px",
                    display: "block",
                  }}
                >
                  💡 You can select multiple organizations. Use the search
                  function to find specific organizations quickly.
                </Text>
                <Form.Item shouldUpdate style={{ marginBottom: 0 }}>
                  {() => {
                    const selectedIds: string[] =
                      form.getFieldValue("tenants") || [];

                    const removeTenant = (idToRemove: string) => {
                      const updated = selectedIds.filter(
                        (id: string) => id !== idToRemove
                      );
                      form.setFieldsValue({ tenants: updated });
                    };

                    return selectedIds.length > 0 ? (
                      <div
                        style={{
                          marginTop: "12px",

                          padding: "16px",

                          background: "rgba(102, 126, 234, 0.05)",
                          borderRadius: "12px",
                          border: "1px solid rgba(102, 126, 234, 0.1)",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: "14px",
                            fontWeight: 600,
                            color: "#374151",
                            display: "block",
                            marginBottom: "12px",
                          }}
                        >
                          Selected organizations ({selectedIds.length}):
                        </Text>
                        <div
                          style={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: "8px",
                          }}
                        >
                          {selectedIds.map((id: string) => {
                            const tenant = tenantData.find((t) => t.id === id);
                            const name =
                              tenant?.tenant_name || `Organization ${id}`;

                            return (
                              <div
                                key={id}
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "8px",
                                  padding: "6px 12px",
                                  background: "white",
                                  borderRadius: "20px",
                                  border: "1px solid rgba(102, 126, 234, 0.2)",
                                  fontSize: "12px",
                                  fontWeight: 500,
                                }}
                              >
                                <span style={{ color: "#374151" }}>{name}</span>
                                <CloseOutlined
                                  onClick={() => removeTenant(id)}
                                  style={{
                                    cursor: "pointer",
                                    color: "#ef4444",
                                    fontSize: "10px",
                                    padding: "2px",
                                    borderRadius: "50%",
                                    transition: "all 0.2s ease",
                                  }}
                                  onMouseEnter={(e) => {
                                    const target = e.target as HTMLElement;
                                    target.style.background = "#ef4444";
                                    target.style.color = "white";
                                  }}
                                  onMouseLeave={(e) => {
                                    const target = e.target as HTMLElement;
                                    target.style.background = "transparent";
                                    target.style.color = "#ef4444";
                                  }}
                                />
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ) : null;
                  }}
                </Form.Item>
                <Row gutter={16}>
                  <Col xs={24} sm={8}>
                    <div>
                      <Text
                        style={{
                          display: "block",
                          marginBottom: 12,
                          fontWeight: 500,
                          color: "#374151",
                          fontSize: "15px",
                        }}
                      >
                        Due Date <span style={{ color: "#ef4444" }}>*</span>
                      </Text>

                      <DatePicker
                        size="large"
                        style={{
                          width: "100%",
                          borderRadius: "12px",
                          border: "1px solid rgba(209, 213, 219, 0.8)",
                        }}
                        disabledDate={(current) =>
                          current && current < dayjs().startOf("day")
                        }
                        value={dueDate ? dayjs(dueDate) : null}
                        onChange={(_, dateString) => {
                          if (typeof dateString === "string")
                            setDueDate(dateString);
                          else setDueDate("");
                        }}
                        placeholder="Select due date"
                      />
                    </div>
                  </Col>
                  <Col xs={24} sm={16}>
                    <div>
                      <Text
                        style={{
                          display: "block",
                          marginBottom: 12,
                          fontWeight: 500,
                          color: "#374151",
                          fontSize: "15px",
                        }}
                      >
                        Notes <span style={{ color: "#ef4444" }}>*</span>
                      </Text>
                      <TextArea
                        placeholder="Enter assignment notes and instructions"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        rows={4}
                        style={{
                          width: "100%",
                          borderRadius: "12px",
                          border: "1px solid rgba(209, 213, 219, 0.8)",
                          background: "rgba(255, 255, 255, 0.9)",
                        }}
                      />
                    </div>
                  </Col>
                </Row>
              </div>

              <div
                style={{
                  textAlign: "center",
                  marginTop: 48,
                  padding: "20px",
                  background: "rgba(255, 255, 255, 0.8)",
                  borderRadius: "20px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}
              >
                <Row gutter={24} justify="center">
                  <Col xs={24} sm={12} md={8}>
                    <Button
                      style={{
                        width: "100%",
                        height: "48px",
                        borderRadius: "12px",
                        border: "2px solid #e5e7eb",
                        background: "white",
                        color: "#6b7280",
                        fontSize: "16px",
                        fontWeight: 600,
                        transition: "all 0.3s ease",
                      }}
                      onClick={() => router.push("/manageGames")}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.borderColor = "#ef4444";
                        target.style.color = "#ef4444";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.color = "#6b7280";
                      }}
                    >
                      Cancel
                    </Button>
                  </Col>
                  <Col xs={24} sm={12} md={8}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                      style={{
                        width: "100%",
                        background:
                          "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        border: "none",
                        borderRadius: "12px",
                        height: "48px",
                        fontSize: "16px",
                        fontWeight: 600,
                        boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                        transition: "all 0.3s ease",
                      }}
                      onMouseEnter={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(-2px)";
                        target.style.boxShadow =
                          "0 8px 25px rgba(102, 126, 234, 0.5)";
                      }}
                      onMouseLeave={(e) => {
                        const target = e.target as HTMLElement;
                        target.style.transform = "translateY(0)";
                        target.style.boxShadow =
                          "0 4px 15px rgba(102, 126, 234, 0.4)";
                      }}
                    >
                      {submitting ? "Creating Exercise..." : "Create Exercise"}
                    </Button>
                  </Col>
                </Row>
              </div>
            </Form>
          </div>
        </Content>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .game-form-container {
          animation: fadeInUp 0.6s ease-out;
        }
      `}</style>
    </Layout>
  );
}
