"use client";
import { AuthPage as AuthPageBase } from "@refinedev/antd";
import type { AuthPageProps } from "@refinedev/core";

export const AuthPage = (props: AuthPageProps) => {
  return (
    <AuthPageBase
      {...props}
      title={
        <h1 style={{ fontWeight: "bold", fontSize: 32, color: "#1677ff", marginBottom: 24 }}>
          recallloop
        </h1>
      }
      formProps={{
        initialValues: {
          email: "<EMAIL>",
          password: "refine-supabase",
        },
      }}
    />
  );
};
