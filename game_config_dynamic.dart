class GameConfig {
  final String id;
  final String title;
  final String category;
  final int level;
  final int? timer;
  final String instructions;
  final GameAssets assets;
  final GameLogic logic;
  final GameRewards rewards;
  final int? targetScore;
  final String? levelId;

  GameConfig({
    required this.id,
    required this.title,
    required this.category,
    required this.level,
    this.timer,
    required this.instructions,
    required this.assets,
    required this.logic,
    required this.rewards,
    this.levelId,
    this.targetScore,
  });

  factory GameConfig.fromJson(Map<String, dynamic> json) {
    return GameConfig(
      id: json['id'],
      title: json['title'],
      category: json['category'],
      level: json['level'],
      timer: json['timer'],
      instructions: json['instructions'],
      assets: GameAssets.fromJson(json['assets'] ?? {}),
      logic: GameLogic.fromJson(json['logic']),
      rewards: GameRewards.fromJson(json['rewards']),
      targetScore: json['target_score'],
      levelId: json['level_id'],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'category': category,
    'level': level,
    'timer': timer,
    'instructions': instructions,
    'assets': assets.toJson(),
    'logic': logic.toJson(),
    'rewards': rewards.toJson(),
    'target_score': targetScore,
    'level_id': levelId,
  };
}

class GameAssets {
  final Map<String, dynamic> _data;

  GameAssets(this._data);

  factory GameAssets.fromJson(Map<String, dynamic> json) {
    return GameAssets(Map<String, dynamic>.from(json));
  }

  Map<String, dynamic> toJson() => Map<String, dynamic>.from(_data);

  // Generic getter for any property
  T? get<T>(String key) {
    final value = _data[key];
    if (value is T) return value;
    return null;
  }

  // Generic setter for any property
  void set<T>(String key, T value) {
    _data[key] = value;
  }

  // Check if a property exists
  bool has(String key) => _data.containsKey(key);

  // Get all keys
  Iterable<String> get keys => _data.keys;

  // Get all values
  Iterable<dynamic> get values => _data.values;

  // Convenience getters for common properties (optional - for backward compatibility)
  List<String>? get words => get<List<dynamic>>('words')?.cast<String>();
  
  List<Map<String, dynamic>>? get problems => 
    get<List<dynamic>>('problems')?.map((e) => Map<String, dynamic>.from(e)).toList();
  
  List<Map<String, dynamic>>? get patterns => 
    get<List<dynamic>>('patterns')?.map((e) => Map<String, dynamic>.from(e)).toList();
  
  List<Map<String, dynamic>>? get targetShapes => 
    get<List<dynamic>>('target_shapes')?.map((e) => Map<String, dynamic>.from(e)).toList();
  
  List<Map<String, dynamic>>? get distractorShapes => 
    get<List<dynamic>>('distractor_shapes')?.map((e) => Map<String, dynamic>.from(e)).toList();
  
  String? get gridSize => get<String>('grid_size');
  
  List<String>? get shapes => get<List<dynamic>>('shapes')?.cast<String>();
  
  List<String>? get colors => get<List<dynamic>>('colors')?.cast<String>();
  
  List<List<int>>? get positions => 
    get<List<dynamic>>('positions')?.map((pos) => List<int>.from(pos)).toList();
  
  int? get sequenceLength => get<int>('sequence_length');
  
  List<int>? get sequence => get<List<dynamic>>('sequence')?.cast<int>();
  
  int? get memorizationTime => get<int>('memorization_time');

  // Convenience setters for common properties
  set words(List<String>? value) => set('words', value);
  set problems(List<Map<String, dynamic>>? value) => set('problems', value);
  set patterns(List<Map<String, dynamic>>? value) => set('patterns', value);
  set targetShapes(List<Map<String, dynamic>>? value) => set('target_shapes', value);
  set distractorShapes(List<Map<String, dynamic>>? value) => set('distractor_shapes', value);
  set gridSize(String? value) => set('grid_size', value);
  set shapes(List<String>? value) => set('shapes', value);
  set colors(List<String>? value) => set('colors', value);
  set positions(List<List<int>>? value) => set('positions', value);
  set sequenceLength(int? value) => set('sequence_length', value);
  set sequence(List<int>? value) => set('sequence', value);
  set memorizationTime(int? value) => set('memorization_time', value);
}

class GameLogic {
  final int minCorrect;
  final dynamic nextGameUnlocked; // to handle both bool and null

  GameLogic({required this.minCorrect, required this.nextGameUnlocked});

  factory GameLogic.fromJson(Map<String, dynamic> json) {
    return GameLogic(
      minCorrect: json['min_correct'],
      nextGameUnlocked: json['next_game_unlocked'],
    );
  }

  Map<String, dynamic> toJson() => {
    'min_correct': minCorrect,
    'next_game_unlocked': nextGameUnlocked,
  };
}

class GameRewards {
  final int points;
  final String? badge;
  final String? badgeId;
  final String? badgeName;

  GameRewards({required this.points, this.badge, this.badgeId, this.badgeName});

  factory GameRewards.fromJson(Map<String, dynamic> json) {
    return GameRewards(
      points: json['points'],
      badge: json['badge'],
      badgeId: json['badge_id'],
      badgeName: json['badge_name'],
    );
  }

  Map<String, dynamic> toJson() => {
    'points': points,
    'badge': badge,
    'badge_id': badgeId,
    'badge_name': badgeName,
  };
}
