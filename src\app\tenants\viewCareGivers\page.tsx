"use client";

import React, { useState } from "react";
import { CommonTable } from "@components/common/Table";
import { Input, Button, Avatar, Card, Typography } from "antd";
import { UserOutlined, PlusOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";

const { Text } = Typography;

// Sample data for users
const usersData = [
  {
    id: "001",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/john.jpg",
  },
  {
    id: "002",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/jane.jpg",
  },
  {
    id: "003",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/michael.jpg",
  },
  {
    id: "004",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/emily.jpg",
  },
  {
    id: "005",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/david.jpg",
  },
  {
    id: "006",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/sarah.jpg",
  },
  {
    id: "007",
    name: "Kevin Lee",
    email: "<EMAIL>",
    avatar: "/avatars/kevin.jpg",
  },
];

export default function ViewUsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();
  // Filter data based on search term
  const filteredUsers = usersData.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.includes(searchTerm)
  );

  // User table columns
  const userColumns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
      render: (text: string) => <Text style={{ fontWeight: 500 }}>{text}</Text>,
    },
    {
      title: "Avatar",
      dataIndex: "avatar",
      key: "avatar",
      width: 80,
      align: "center" as const,
      render: (
        _: unknown,
        record: { id: string; name: string; email: string; avatar: string }
      ) => (
        <Avatar
          src={record.avatar}
          icon={<UserOutlined />}
          style={{ backgroundColor: "#6366f1" }}
          size={40}
        />
      ),
    },
    {
      title: "Customer",
      dataIndex: "name",
      key: "name",
      render: (text: string) => <Text style={{ fontWeight: 500 }}>{text}</Text>,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      render: (text: string) => <Text style={{ color: "#666" }}>{text}</Text>,
    },
    {
      title: "Actions",
      key: "actions",
      width: 100,
      align: "center" as const,
      render: () => (
        <Button
          type="primary"
          size="small"
          style={{
            backgroundColor: "#6366f1",
            color: "white",
            border: "none",
            borderRadius: "4px",
            fontSize: "12px",
            height: "28px",
            minWidth: "60px",
          }}
        >
          View
        </Button>
      ),
    },
  ];

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 8,

        padding: 24,
      }}
    >
      {/* Header Section */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "24px",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
          <Text style={{ color: "#666", fontSize: "14px" }}>Filter by:</Text>
          <Input
            placeholder="Status"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: "150px",
              borderRadius: "4px",
              border: "1px solid #d9d9d9",
            }}
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          style={{
            backgroundColor: "#6366f1",
            borderColor: "#6366f1",
            borderRadius: "15px",
            height: "36px",
            fontSize: "14px",
            fontWeight: 500,
          }}
          onClick={() => {
            router.push("/tenants/create")
          
          }}
        >
          Add CareGiver
        </Button>
      </div>

      {/* Users Table */}
      <Card
        style={{
          borderRadius: "8px",
          border: "1px solid #f0f0f0",
        }}
        bodyStyle={{ padding: 0 }}
      >
        <CommonTable
          columns={userColumns}
          dataSource={filteredUsers}
          loading={false}
          rowKey="id"
          pagination={{
            pageSize: 7,
            showSizeChanger: false,
            showQuickJumper: false,
            showTotal: (total: number, range: [number, number]) =>
              `${range[0]}-${range[1]} of ${total} items`,
            style: {
              padding: "16px 24px",
              borderTop: "1px solid #f0f0f0",
            },
          }}
          bordered={false}
          style={{
            ".ant-table-thead > tr > th": {
              backgroundColor: "#fafafa",
              borderBottom: "1px solid #f0f0f0",
              padding: "12px 16px",
              fontSize: "14px",
              fontWeight: 500,
              color: "#666",
            },
            ".ant-table-tbody > tr > td": {
              padding: "16px",
              borderBottom: "1px solid #f0f0f0",
            },
            ".ant-table-tbody > tr:hover > td": {
              backgroundColor: "#fafafa",
            },
          }}
        />
      </Card>
    </div>
  );
}
