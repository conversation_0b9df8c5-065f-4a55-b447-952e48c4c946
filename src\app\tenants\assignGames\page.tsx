"use client";
import React, { useEffect, useState } from "react";
import {
  Typography,
  Checkbox,
  Input,
  Button,
  message,
  Select,
  DatePicker,
} from "antd";

import { FetchGamesResponse, Game, ListTenantGamesRequest } from "@types";
import { dataProviderInstance } from "@providers/data-provider";
import { CommonTable } from "@components/common-table";
import dayjs from "dayjs";
import { CAREGIVER, PLAYER, USER } from "@utils/supabase/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
const { Title } = Typography;

export default function AssignGamesPage() {
  const [gameData, setGames] = useState<Game[]>([]);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [usersData, setUsersData] = useState<
    Array<{ id: number; name: string; email: string, type: string }>
  >([]);
  const [dueDate, setDueDate] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [gameSearch, setGameSearch] = useState("");

  useEffect(() => {
    const fetchGames = async () => {
      try {
        setLoading(true);
        const para = {
          tenant_code: localStorage.getItem("tenant_code")
        };
        const response: FetchGamesResponse =
          await dataProviderInstance.listTenantGames(
            para as ListTenantGamesRequest
          );
        setGames(response.data);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
        setGames([]);
      } finally {
        setLoading(false);
      }
    };
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const party_id = localStorage.getItem("party_id") || "";
        //const party_type_key = localStorage.getItem("role") || "";
        const tenant_code = localStorage.getItem("tenant_code") || "";
        const inputPara = {
          party_id: party_id,
          tenant_code: tenant_code,
          party_type_key: "TENANTADM",
        };
        const response = await dataProviderInstance.listTenantUsers(inputPara);
        const users = [
          ...(response.data.data.users || []).map(
            (u: { id: number; name: string; email: string }) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              type: USER,
            })
          ),
          ...(response.data.data.caregivers || []).map(
            (u: { id: number; name: string; email: string; }) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              type: CAREGIVER,
            })
          ),
          ...(response.data.data.players || []).map(
            (u: { id: number; name: string, email: string }) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              type: PLAYER,
            })
          ),
        ];
        setUsersData(users);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
        setGames([]);
      } finally {
        setLoading(false);
      }
    };
    fetchGames();
    fetchUsers();
  }, []);

  const handleGameSelection = (gameId: string, checked: boolean) => {
    setGames((prevGames) =>
      prevGames.map((game) =>
        game.game_id === gameId ? { ...game, selected: checked } : game
      )
    );
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const selectedGames = gameData.filter((game) => game.selected);
      const game_ids = selectedGames.map((game) => String(game.game_id));
      const party_ids = selectedUserIds.map((id) => String(id));
      let formattedDueDate: string | undefined = undefined;
      if (dueDate) {
        const date = new Date(dueDate);
        formattedDueDate = date.toISOString();
      }

      const reqBody = {
        party_ids,
        game_ids,
        due_date: formattedDueDate as string,
        notes: notes || "",
      };
      debugger;
      const response = await dataProviderInstance.assignGame(reqBody);

      const { skipped = [], inserted = [] } = response.data.data || {};

      if (skipped.length > 0 && inserted.length > 0) {
        message.success(ERROR_MESSAGES.some_users_already_assigned);
      } else if (skipped.length > 0 && inserted.length === 0) {
        message.error(ERROR_MESSAGES.all_users_assigned);
      } else if (inserted.length > 0 && skipped.length === 0) {
        message.success(SUCCESS_MESSAGES.assign_games);
      } else {
        message.info(ERROR_MESSAGES.assign_games);
      }
    } catch (error) {
      console.log(error);
      message.error(ERROR_MESSAGES.assign_games);
    } finally {
      setLoading(false);
    }
  };

  const filteredGames = gameData.filter((game) =>
    game.game_name.toLowerCase().includes(gameSearch.toLowerCase())
  );

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 8,
        padding: 24,
        boxSizing: "border-box",
        width: "100%",
      }}
    >
      <style>
        {`
        @media (max-width: 768px) {
        .assign-games-title {
          font-size: 1.3rem !important;
        }
        .assign-games-table {
          overflow-x: auto;
        }
        .assign-games-flex {
          flex-direction: column !important;
          gap: 8px !important;
        }
        .assign-games-date {
          max-width: 100% !important;
        }
        .assign-games-btn {
          min-width: 100% !important;
          width: 100% !important;
          margin-top: 16px;
        }
        }
      `}
      </style>
      <div
        style={{
          textAlign: "center",
          marginBottom: "32px",
        }}
      >
        <Title
          level={2}
          style={{ margin: 0, fontWeight: 700 }}
          className="assign-games-title"
        >
          Assign Games
        </Title>
      </div>

      <div style={{ marginBottom: 16 }}>
        <Input.Search
          placeholder="Search games"
          value={gameSearch}
          onChange={(e) => setGameSearch(e.target.value)}
          style={{ maxWidth: 320 }}
          allowClear
        />
      </div>

      {/* Games Selection Section */}
      <div style={{ marginBottom: "16px" }} className="assign-games-table">
        <CommonTable
          rowKey="id"
          dataSource={filteredGames}
          loading={loading}
          pagination={{ pageSize: 8 }}
          columns={[
            {
              title: "",
              dataIndex: "selected",
              key: "selected",
              width: 48,
              render: (_: unknown, record: Game & { selected?: boolean }) => (
                <Checkbox
                  checked={!!record.selected}
                  onChange={(e) =>
                    handleGameSelection(record.game_id, e.target.checked)
                  }
                />
              ),
            },
            {
              title: "Image",
              dataIndex: "thumbnail_url",
              key: "thumbnail_url",
              width: 80,
              render: (url: string) => (
                <img
                  src={url || "/assets/add-game.png"}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/assets/placeholder.png";
                  }}
                  alt="thumbnail"
                  style={{
                    width: 32,
                    height: 32,
                    objectFit: "cover",
                  }}
                />
              ),
            },
            {
              title: "Game Name",
              dataIndex: "game_name",
              key: "game_name",
              render: (text: string) => (
                <span style={{ fontWeight: 600, fontSize: 16, color: "#222" }}>
                  {text}
                </span>
              ),
            },
            {
              title: "Description",
              dataIndex: "description",
              key: "description",
              render: (text: string) => (
                <span style={{ color: "#888", fontSize: 15 }}>{text}</span>
              ),
            },
            {
              title: "No of levels",
              dataIndex: "no_of_levels",
              key: "no_of_levels",
              render: (text: string) => (
                <span style={{ color: "#888", fontSize: 15 }}>{text}</span>
              ),
            },
            {
              title: "Valid From",
              dataIndex: "valid_from",
              key: "valid_from",
              render: (valid_from: string) => (
                <span style={{ color: "#888", fontSize: 15 }}>
                  {valid_from
                    ? new Date(valid_from).toISOString().slice(0, 10)
                    : "-"}
                </span>
              ),
            },
            {
              title: "Valid To",
              dataIndex: "valid_to",
              key: "valid_to",
              render: (valid_to: string) => (
                <span style={{ color: "#888", fontSize: 15 }}>
                  {valid_to
                    ? new Date(valid_to).toISOString().slice(0, 10)
                    : "-"}
                </span>
              ),
            },
          ]}
        />
      </div>

      {/* Search Section */}
      <div style={{ marginBottom: "16px" }}>
        <Select
          mode="multiple"
          size="large"
          placeholder="Select users"
          options={usersData.map((user) => ({
            label: user.name + "  -  " + user.email,
            value: user.id,
          }))}
          value={selectedUserIds}
          onChange={(values: number[]) => {
            setSelectedUserIds(values);
          }}
          style={{ width: "100%" }}
          allowClear
          showSearch
          filterOption={(input, option) =>
            (option?.label as string)
              .toLowerCase()
              .includes(input.toLowerCase())
          }
        />
      </div>
      <div
        style={{ display: "flex", gap: 16, marginBottom: 16 }}
        className="assign-games-flex"
      >
        <div
          style={{ display: "flex", flexDirection: "column", maxWidth: 240 }}
          className="assign-games-date"
        >
          <label style={{ marginBottom: 4, fontWeight: 500 }}>
            Due date <span style={{ color: "red" }}>*</span>
          </label>
          <DatePicker
            size="large"
            style={{ width: "100%" }}
            disabledDate={(current) =>
              current && current < dayjs().startOf("day")
            }
            value={dueDate ? dayjs(dueDate) : null}
            onChange={(_, dateString) => {
              if (typeof dateString === "string") setDueDate(dateString);
              else setDueDate("");
            }}
          />
        </div>
        <div style={{ display: "flex", flexDirection: "column", flex: 1 }}>
          <label style={{ marginBottom: 4, fontWeight: 500 }}>
            Note (optional)
          </label>
          <Input.TextArea
            rows={1}
            size="large"
            placeholder="Add a note (optional)"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </div>
      </div>

      {/* Save Button */}
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
        }}
      >
        <Button
          type="primary"
          onClick={async () => {
            let hasError = false;
            const selectedGames = gameData.filter((game) => game.selected);
            if (selectedGames.length === 0) {
              message.error(ERROR_MESSAGES.select_game);
              hasError = true;
            }
            if (selectedUserIds.length === 0) {
              message.error(ERROR_MESSAGES.select_user);
              hasError = true;
            }
            if (!dueDate) {
              message.error(ERROR_MESSAGES.select_due_date);
              hasError = true;
            }
            if (hasError) return;
            await handleSave();
          }}
          loading={loading}
          style={{
            background: "#6366f1",
            border: "none",
            borderRadius: 10,
            height: 48,
            minWidth: 160,
            fontSize: 18,
            fontWeight: 600,
            boxShadow: "0 2px 8px 0 rgba(99,102,241,0.10)",
          }}
          className="assign-games-btn"
        >
          Save
        </Button>
      </div>
    </div>
  );
}
