"use client";
import React, { useEffect, useState } from "react";
import {
  Typography,
  Checkbox,
  Input,
  Button,
  message,
  Select,
  DatePicker,
  Layout,
  Card,
  Row,
  Col,
} from "antd";

import { FetchGamesResponse, Game, ListTenantGamesRequest } from "@types";
import { dataProviderInstance } from "@providers/data-provider";
import { CommonTable } from "@components/common/Table";
import dayjs from "dayjs";
import { CAREGIVER, PLAYER, USER } from "@utils/supabase/constants";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
const { Title, Text } = Typography;
const { Content } = Layout;

export default function AssignGamesPage() {
  const [gameData, setGames] = useState<Game[]>([]);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [usersData, setUsersData] = useState<
    Array<{ id: number; name: string; email: string, type: string }>
  >([]);
  const [dueDate, setDueDate] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [gameSearch, setGameSearch] = useState("");

  useEffect(() => {
    const fetchGames = async () => {
      try {
        setLoading(true);
        const para = {
          tenant_code: localStorage.getItem("tenant_code")
        };
        const response: FetchGamesResponse =
          await dataProviderInstance.listTenantGames(
            para as ListTenantGamesRequest
          );
        setGames(response.data);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
        setGames([]);
      } finally {
        setLoading(false);
      }
    };
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const party_id = localStorage.getItem("party_id") || "";
        //const party_type_key = localStorage.getItem("role") || "";
        const tenant_code = localStorage.getItem("tenant_code") || "";
        const inputPara = {
          party_id: party_id,
          tenant_code: tenant_code,
          party_type_key: "TENANTADM",
        };
        const response = await dataProviderInstance.listTenantUsers(inputPara);
        const users = [
          ...(response.data.data.users || []).map(
            (u: { id: number; name: string; email: string }) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              type: USER,
            })
          ),
          ...(response.data.data.caregivers || []).map(
            (u: { id: number; name: string; email: string; }) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              type: CAREGIVER,
            })
          ),
          ...(response.data.data.players || []).map(
            (u: { id: number; name: string, email: string }) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              type: PLAYER,
            })
          ),
        ];
        setUsersData(users);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
        setGames([]);
      } finally {
        setLoading(false);
      }
    };
    fetchGames();
    fetchUsers();
  }, []);

  const handleGameSelection = (gameId: string, checked: boolean) => {
    setGames((prevGames) =>
      prevGames.map((game) =>
        game.game_id === gameId ? { ...game, selected: checked } : game
      )
    );
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const selectedGames = gameData.filter((game) => game.selected);
      const game_ids = selectedGames.map((game) => String(game.game_id));
      const party_ids = selectedUserIds.map((id) => String(id));
      let formattedDueDate: string | undefined = undefined;
      if (dueDate) {
        const date = new Date(dueDate);
        formattedDueDate = date.toISOString();
      }

      const reqBody = {
        party_ids,
        game_ids,
        due_date: formattedDueDate as string,
        notes: notes || "",
      };
      debugger;
      const response = await dataProviderInstance.assignGame(reqBody);

      const { skipped = [], inserted = [] } = response.data.data || {};

      if (skipped.length > 0 && inserted.length > 0) {
        message.success(ERROR_MESSAGES.some_users_already_assigned);
      } else if (skipped.length > 0 && inserted.length === 0) {
        message.error(ERROR_MESSAGES.all_users_assigned);
      } else if (inserted.length > 0 && skipped.length === 0) {
        message.success(SUCCESS_MESSAGES.assign_games);
      } else {
        message.info(ERROR_MESSAGES.assign_games);
      }
    } catch (error) {
      console.log(error);
      message.error(ERROR_MESSAGES.assign_games);
    } finally {
      setLoading(false);
    }
  };

  const filteredGames = gameData.filter((game) =>
    game.game_name.toLowerCase().includes(gameSearch.toLowerCase())
  );

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          maxWidth: "800px",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            Assign Exercises
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Select exercise and assign them to users with due dates and notes
          </Text>
        </div>

        {/* Main Content Container */}
        <Card
          style={{
            width: "100%",
            maxWidth: "1200px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "16px",
          }}
          styles={{ body: { padding: "32px" } }}
        >

          {/* Search Section */}
          <div style={{
            background: "rgba(248, 250, 252, 0.6)",
            borderRadius: "16px",
            padding: "24px",
            marginBottom: "32px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Title
              level={4}
              style={{
                marginBottom: "16px",
                color: "#374151",
                fontWeight: 600,
              }}
            >
              Search Exercises
            </Title>
            <Input.Search
              placeholder="Search exercises by name..."
              value={gameSearch}
              onChange={(e) => setGameSearch(e.target.value)}
              size="large"
              style={{
                maxWidth: 400,
                borderRadius: "12px",
              }}
              allowClear
            />
          </div>

          {/* Games Selection Section */}
          <div style={{
            background: "rgba(248, 250, 252, 0.6)",
            borderRadius: "16px",
            padding: "24px",
            marginBottom: "32px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Title
              level={4}
              style={{
                marginBottom: "24px",
                color: "#374151",
                fontWeight: 600,
              }}
            >
              Select Exercises to Assign
            </Title>
            <div style={{
              background: "rgba(255, 255, 255, 0.9)",
              borderRadius: "12px",
              overflow: "hidden",
              border: "1px solid rgba(226, 232, 240, 0.5)",
            }}>
              <CommonTable
                rowKey="id"
                dataSource={filteredGames}
                loading={loading}
                pagination={{
                  pageSize: 8,
                  showSizeChanger: false,
                  showQuickJumper: true,
                }}
                columns={[
                  {
                    title: "",
                    dataIndex: "selected",
                    key: "selected",
                    width: 48,
                    render: (_: unknown, record: Game & { selected?: boolean }) => (
                      <Checkbox
                        checked={!!record.selected}
                        onChange={(e) =>
                          handleGameSelection(record.game_id, e.target.checked)
                        }
                        style={{
                          transform: "scale(1.2)",
                        }}
                      />
                    ),
                  },
                  {
                    title: "Image",
                    dataIndex: "thumbnail_url",
                    key: "thumbnail_url",
                    width: 80,
                    render: (url: string) => (
                      <img
                        src={url || "/assets/add-game.png"}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/assets/placeholder.png";
                        }}
                        alt="thumbnail"
                        style={{
                          width: 40,
                          height: 40,
                          objectFit: "cover",
                          borderRadius: "8px",
                          border: "1px solid rgba(226, 232, 240, 0.5)",
                        }}
                      />
                    ),
                  },
                  {
                    title: "Exercise Name",
                    dataIndex: "game_name",
                    key: "game_name",
                    render: (text: string) => (
                      <Text style={{
                        fontWeight: 600,
                        fontSize: 16,
                        color: "#374151"
                      }}>
                        {text}
                      </Text>
                    ),
                  },
                  {
                    title: "Description",
                    dataIndex: "description",
                    key: "description",
                    render: (text: string) => (
                      <Text style={{
                        color: "#6b7280",
                        fontSize: 14
                      }}>
                        {text}
                      </Text>
                    ),
                  },
                  {
                    title: "Levels",
                    dataIndex: "no_of_levels",
                    key: "no_of_levels",
                    render: (text: string) => (
                      <Text style={{
                        color: "#6b7280",
                        fontSize: 14,
                        fontWeight: 500,
                      }}>
                        {text}
                      </Text>
                    ),
                  },
                  {
                    title: "Valid From",
                    dataIndex: "valid_from",
                    key: "valid_from",
                    render: (valid_from: string) => (
                      <Text style={{
                        color: "#6b7280",
                        fontSize: 14
                      }}>
                        {valid_from
                          ? new Date(valid_from).toISOString().slice(0, 10)
                          : "-"}
                      </Text>
                    ),
                  },
                  {
                    title: "Valid To",
                    dataIndex: "valid_to",
                    key: "valid_to",
                    render: (valid_to: string) => (
                      <Text style={{
                        color: "#6b7280",
                        fontSize: 14
                      }}>
                        {valid_to
                          ? new Date(valid_to).toISOString().slice(0, 10)
                          : "-"}
                      </Text>
                    ),
                  },
                ]}
              />
            </div>
          </div>

          {/* User Selection Section */}
          <div style={{
            background: "rgba(248, 250, 252, 0.6)",
            borderRadius: "16px",
            padding: "24px",
            marginBottom: "32px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Title
              level={4}
              style={{
                marginBottom: "16px",
                color: "#374151",
                fontWeight: 600,
              }}
            >
              Select Users
            </Title>
            <Select
              mode="multiple"
              size="large"
              placeholder="Select users to assign exercises to..."
              options={usersData.map((user) => ({
                label: `${user.name} - ${user.email}`,
                value: user.id,
              }))}
              value={selectedUserIds}
              onChange={(values: number[]) => {
                setSelectedUserIds(values);
              }}
              style={{
                width: "100%",
                borderRadius: "12px",
              }}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.label as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </div>

          {/* Assignment Details Section */}
          <div style={{
            background: "rgba(248, 250, 252, 0.6)",
            borderRadius: "16px",
            padding: "24px",
            marginBottom: "32px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Title
              level={4}
              style={{
                marginBottom: "24px",
                color: "#374151",
                fontWeight: 600,
              }}
            >
              Assignment Details
            </Title>

            <Row gutter={24}>
              <Col xs={24} md={8}>
                <div style={{ marginBottom: 24 }}>
                  <Text style={{
                    display: "block",
                    marginBottom: 8,
                    fontWeight: 500,
                    color: "#374151",
                  }}>
                    Due Date <span style={{ color: "#ef4444" }}>*</span>
                  </Text>
                  <DatePicker
                    size="large"
                    style={{
                      width: "100%",
                      borderRadius: "12px",
                      border: "1px solid rgba(209, 213, 219, 0.8)",
                      background: "rgba(255, 255, 255, 0.9)",
                    }}
                    placeholder="Select due date"
                    disabledDate={(current) =>
                      current && current < dayjs().startOf("day")
                    }
                    value={dueDate ? dayjs(dueDate) : null}
                    onChange={(_, dateString) => {
                      if (typeof dateString === "string") setDueDate(dateString);
                      else setDueDate("");
                    }}
                  />
                </div>
              </Col>
              <Col xs={24} md={16}>
                <div style={{ marginBottom: 0 }}>
                  <Text style={{
                    display: "block",
                    marginBottom: 8,
                    fontWeight: 500,
                    color: "#374151",
                  }}>
                    Notes (Optional)
                  </Text>
                  <Input.TextArea
                    rows={3}
                    size="large"
                    placeholder="Add assignment notes..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    style={{
                      borderRadius: "12px",
                      resize: "none",
                      border: "1px solid rgba(209, 213, 219, 0.8)",
                      background: "rgba(255, 255, 255, 0.9)",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </div>

          {/* Action Button */}
          <div style={{
            display: "flex",
            gap: "16px",
            marginTop: "40px",
            paddingTop: "32px",
            justifyContent: "center",
            borderTop: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <Button
              type="primary"
              onClick={async () => {
                let hasError = false;
                const selectedGames = gameData.filter((game) => game.selected);
                if (selectedGames.length === 0) {
                  message.error(ERROR_MESSAGES.select_game);
                  hasError = true;
                }
                if (selectedUserIds.length === 0) {
                  message.error(ERROR_MESSAGES.select_user);
                  hasError = true;
                }
                if (!dueDate) {
                  message.error(ERROR_MESSAGES.select_due_date);
                  hasError = true;
                }
                if (hasError) return;
                await handleSave();
              }}
              loading={loading}
              size="large"
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                borderColor: "transparent",
                borderRadius: "12px",
                height: "48px",
                fontSize: "16px",
                fontWeight: 600,
                paddingLeft: "32px",
                paddingRight: "32px",
                boxShadow: "0 4px 12px rgba(102, 126, 234, 0.4)",
                transition: "all 0.3s ease",
                minWidth: "200px",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(-2px)";
                target.style.boxShadow = "0 6px 20px rgba(102, 126, 234, 0.6)";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(0)";
                target.style.boxShadow = "0 4px 12px rgba(102, 126, 234, 0.4)";
              }}
            >
              {loading ? "Assigning Exercise..." : "Assign Exercise"}
            </Button>
          </div>
        </Card>
      </Content>
    </Layout>
  );
}
