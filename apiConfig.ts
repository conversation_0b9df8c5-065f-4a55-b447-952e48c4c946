export const RPC = {
  listTenant: "fn_list_all_tenants",
  updateGame: "fn_update_game",
  manageUser: "fn_manage_user",
  insertGame: "fn_insert_game",
  game_config: "fn_update_game_config",
  getUserInfo: "fn_get_user_info",
  getPlayerStatistics:"ms_fn_get_player_statistics",
  addTenant: "fn_add_tenant",
  deleteUser: "fn_delete_user",
  deleteReward: "fn_delete_reward",
  deleteTenant: "fn_delete_tenant",
  updateTenant: "fn_update_tenant",
  updateReward: "fn_update_reward",
  adminDashboard: "fn_get_superadmin_dashboard_summary",
  getTenantDashboardSummary: "fn_get_tenant_dashboard_summary",
  listGame: "fn_fetch_games",
  listTenantGames: "fn_fetch_tenant_games",
  assignGameToTenant: "fn_assign_game_to_tenant",
  listUsers: "fn_list_users",
  listTenantUsers: "fn_list_users_under_tenant",
  assignGames: "fn_game_assign_to_parties",
  createReward: "fn_create_reward",
  getNextTenantCode: "fn_get_next_tenant_code",
  fetchGamedetails: "fn_fetch_game_details",
  listAllUnassignedTenantByGame: "fn_list_all_unassigned_tenants",
  getProfileDetails: "get_profile_details",
  assignCareTaker: "fn_add_parent_player",
  updateProfileInfo: "fn_update_profile_info",
  listCareGiver: "fn_list_all_unassigned_users",
  getUserDashboard: "fn_get_user_dashboard",
  signUp: process.env.NEXT_PUBLIC_SUPABASE_URL + "/auth/v1/signup",
  listUnAssignedGames: "fn_list_all_unassigned_games",
  updatePartyInfo: "fn_update_party_info",
  getProgressMetrics:"fn_get_progress_metrics"
};
export const VIEWS = {
  listGameCategories: "v_list_game_category",
  listRewardCategories: "v_list_reward_category",
  listRewards: "v_list_rewards",
  listBadges:"v_list_reward_badges",
  listRoles: "v_list_roles",
  // listCareGiver: "v_get_caregivers_list"
};
