"use client";
import { dataProviderInstance } from "@providers/data-provider";
import {
  CAREGIVER,
  SUPERADMIN,
  TEN<PERSON><PERSON>DM,
  TENANT,
} from "@utils/supabase/constants";
import { Menu } from "antd";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const superAdminItems = [
  { key: "dashboard", label: "Dashboard", path: "/dashboard" },
  { key: "games", label: "Manage Exercises", path: "/manageGames" },
  { key: "rewards", label: "Rewards Management", path: "/rewards" },
  { key: "tenants", label: "Organization Management", path: "/tenants" },
];

// Helper to get selected key, defaulting to "dashboard" if none matches

const tenantItems = [
  { key: "dashboard", label: "Dashboard", path: "/tenants/dashboard" },
  // { key: "assignUsers", label: "Assign Users", path: "/tenants/assignUsers" },
  { key: "viewUsers", label: "View Users", path: "/tenants/viewUsers" },
  // {
  //   key: "viewCaregivers",
  //   label: "View Caregivers",
  //   path: "/tenants/viewCareGivers",
  // },
  {
    key: "assignGames",
    label: "Assign Exercises",
    path: "/tenants/assignGames",
  },
];

const caregiverItems = [
  { key: "dashboard", label: "Dashboard", path: "/caregiver/dashboard" },
  {
    key: "userManagement",
    label: "User Management",
    path: "/caregiver/userManagement",
  },
  {
    key: "progressMonitoring",
    label: "Progress Monitoring",
    path: "/caregiver/progressMonitoring",
  },
  // Add more caregiver-specific items here
];

export default function NavigationMenu({
  selectedKey: propSelectedKey,
}: {
  selectedKey?: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const [navigationItems, setNavigationItems] = useState(tenantItems);

  useEffect(() => {
    const userData = JSON.parse(localStorage.getItem("user") || "{}");
    const currentUserId = userData.id;
    dataProviderInstance
      .getUserInfo(currentUserId)
      .then((response) => {
        const userRole = response.data.data.party_type_key;
        if (userRole.toLowerCase() === SUPERADMIN) {
          setNavigationItems(superAdminItems);
        } else if (
          userRole.toLowerCase() === TENANTADM ||
          userRole.toLowerCase() === TENANT
        ) {
          setNavigationItems(tenantItems);
        } else if (userRole.toLowerCase() === CAREGIVER) {
          setNavigationItems(caregiverItems);
        }
      })
      .catch((error) => {
        console.error("Error fetching user info:", error);
      });
  }, []);

  const selectedKey =
    propSelectedKey ||
    navigationItems.find((item) => pathname?.startsWith(item.path))?.key ||
    "dashboard";

  return (
    <Menu
      mode="horizontal"
      selectedKeys={selectedKey ? [selectedKey] : []}
      items={navigationItems.map((item) => ({
        key: item.key,
        label: item.label,
      }))}
      onClick={({ key }) => {
        const item = navigationItems.find((i) => i.key === key);
        if (item) {
          router.push(item.path);
        }
      }}
      style={{
        border: "none",
        backgroundColor: "transparent",
        fontSize: "14px",
        minWidth: 600,
        flex: 1,
      }}
    />
  );
}
