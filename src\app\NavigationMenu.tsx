"use client";
import { Menu } from "antd";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useUser } from "@contexts/user-context";


const superAdminItems = [
  { key: "dashboard", label: "Dashboard", path: "/dashboard" },
  { key: "games", label: "Manage Games", path: "/manageGames" },
  { key: "rewards", label: "Rewards Management", path: "/rewards" },
  { key: "tenants", label: "Tenant Management", path: "/tenants" },
];

const tenantItems = [
  { key: "dashboard", label: "Dashboard", path: "/tenants/dashboard" },
  // { key: "assignUsers", label: "Assign Users", path: "/tenants/assignUsers" },
  { key: "viewUsers", label: "View Users", path: "/tenants/viewUsers" },
  {
    key: "viewCaregivers",
    label: "View Caregivers",
    path: "/tenants/viewCareGivers",
  },
  { key: "assignGames", label: "Assign Games", path: "/tenants/assignGames" },
];

const caregiverItems = [
  { key: "dashboard", label: "Dashboard", path: "/caregiver/dashboard" },
  {
    key: "userManagement",
    label: "User Management",
    path: "/caregiver/userManagement",
  },
  {
    key: "progressMonitoring",
    label: "Progress Monitoring",
    path: "/caregiver/progressMonitoring",
  },
  // Add more caregiver-specific items here
];

export default function NavigationMenu() {
  const router = useRouter();
  const pathname = usePathname();
  const { role } = useUser();
  const [navigationItems, setNavigationItems] = useState(tenantItems);

  useEffect(() => {
    if (role === "SUPERADMIN") {
      setNavigationItems(superAdminItems);
    } else if (role === "tenant") {
      setNavigationItems(tenantItems);
    } else if (role === "caregiver") {
      setNavigationItems(caregiverItems);
    }
  }, []);

  const selectedKey = navigationItems.find((item) =>
    pathname?.startsWith(item.path)
  )?.key;

  return (
    <Menu
      mode="horizontal"
      selectedKeys={selectedKey ? [selectedKey] : []}
      items={navigationItems.map((item) => ({
        key: item.key,
        label: item.label,
      }))}
      onClick={({ key }) => {
        const item = navigationItems.find((i) => i.key === key);
        if (item) {
          router.push(item.path);
        }
      }}
      style={{
        border: "none",
        backgroundColor: "transparent",
        fontSize: "14px",
      }}
    />
  );
}
