"use client";

import {
  <PERSON><PERSON>,
  But<PERSON>,
  Typo<PERSON>,
  Row,
  Col,
  Progress,
  Spin,
  message,
  Layout,
  Modal,
} from "antd";
import {
  MailOutlined,
  UserOutlined,
  EditOutlined,
  TrophyOutlined,
  PlayCircleOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { LineChart, Line, ResponsiveContainer } from "recharts";
import { dataProviderInstance } from "@providers/data-provider";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { AssignedGame, ProfileData, UserData, RoleType } from "@types";
import LogoutDialog from "@components/dialog/logoutDialog";
import { authProviderClient } from "@providers/auth-provider/auth-provider.client";
import { EditUserModal } from "@components/modal/EditUserModal";

const { Title, Text } = Typography;
const { Content } = Layout;

const assignedGamesChartData = [
  { value: 20 },
  { value: 15 },
  { value: 10 },
  { value: 5 },
  { value: 12 },
  { value: 10 },
  { value: 12 },
];

const Profile: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [assignedGames, setAssignedGames] = useState<AssignedGame[]>([]);
  const [isContactModalVisible, setIsContactModalVisible] = useState(false);

  // EditUserModal states
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const [roles, setRoles] = useState<RoleType[]>([]);
  const [currentUserRole, setCurrentUserRole] = useState<string>("");

  // Function to map party_type_key to role_name
  const mapPartyTypeToRoleName = (partyTypeKey: string): string => {
    const roleMapping: { [key: string]: string } = {
      SUPERADMIN: "Tenant Admin",
      PLAYER: "Player",
      CAREGIVER: "Caregiver",

      TENANTADM: "Tenant Admin",
    };
    return roleMapping[partyTypeKey] || partyTypeKey;
  };

  const fetchProfileData = async () => {
    try {
      setLoading(true);

      // Fetch roles for the modal
      const rolesResponse = await dataProviderInstance.listRoles();
      setRoles(rolesResponse.data);

      const userResponse = await dataProviderInstance.getUser();
      const userId = userResponse.data?.id;

      if (!userId) {
        throw new Error("User not found. Please login again.");
      }

      if (!userResponse.data || !userResponse.data?.user_metadata) {
        throw new Error("Incomplete user data received from backend.");
      }

      setUserData({
        id: userResponse.data.id,
        email: userResponse.data.email || "",
        user_metadata: {
          first_name: userResponse.data.user_metadata?.first_name || "",
          last_name: userResponse.data.user_metadata?.last_name || "",
          username: userResponse.data.user_metadata?.username || "",
          email: userResponse.data.user_metadata?.email || "",
        },
      });

      const profileResponse = await dataProviderInstance.getProfileDetails({
        party_id: userId,
      });

      const profile = profileResponse?.data?.data;

      if (profileResponse.data.status !== "success" || !profile) {
        throw new Error("Failed to fetch profile details");
      }

      setProfileData(profile);

      const userInfoResponse = await dataProviderInstance.getUserInfo(userId);
      const currentUserRole = userInfoResponse.data.data.party_type_key;

      // Store current user role for EditUserModal
      setCurrentUserRole(currentUserRole);

      const tenant_code = localStorage.getItem("tenant_code");

      let gamesResponse;

      if (currentUserRole === "SUPERADMIN") {
        const allGamesResponse = await dataProviderInstance.listGames();

        if (!allGamesResponse?.data) {
          throw new Error("No games found");
        }

        const assignedGamesOnly = allGamesResponse.data.filter(
          (game: AssignedGame) =>
            Array.isArray(game.assigned_parties) &&
            game.assigned_parties.length > 0
        );

        gamesResponse = { data: assignedGamesOnly };
      } else {
        if (!tenant_code) {
          throw new Error("Tenant code not found in localStorage");
        }

        gamesResponse = await dataProviderInstance.listTenantGames({
          tenant_code,
        });
      }

      if (!gamesResponse?.data) {
        throw new Error("No assigned exercises found");
      }

      setAssignedGames(gamesResponse.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Error:", errorMessage);
      message.error(ERROR_MESSAGES.load_profile);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfileData();
  }, []);

  const handleLogoutClick = async () => {
    try {
      authProviderClient.logout({});
      localStorage.clear();
      sessionStorage.clear();
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      router.push("/login");
    }
  };
  // Handle Edit Profile Modal
  const handleEditProfile = () => {
    setIsEditModalVisible(true);
  };

  const handleEditModalClose = () => {
    setIsEditModalVisible(false);
  };
  const handleContactClick = () => {
    setIsContactModalVisible(true);
  };

  const handleContactModalClose = () => {
    setIsContactModalVisible(false);
  };

  type EditFormValues = {
    avatar_url?: string;
    firstName: string;
    lastName: string;
    gender: string;
    email?: string;
    bio?: string;
    countryCode?: string;
    phone?: string;
  };

  const handleEditSubmit = async (values: EditFormValues) => {
    try {
      setEditLoading(true);

      if (!userData?.id) {
        message.error("User ID not found");
        return;
      }

      const updateParams = {
        user_id: userData.id,
        avatar_url: values.avatar_url || profileData?.avatar_url || "",
        first_name: values.firstName,
        last_name: values.lastName,
        gender: values.gender,
        email: values.email || userData.email,
        bio: values.bio ?? "",
        phone_number:
          values.countryCode && values.phone
            ? `${values.countryCode}${values.phone}`
            : values.phone || "",
      };

      await dataProviderInstance.updateProfileInfo(updateParams);
      message.success("Profile updated successfully!");

      // Refresh profile data
      await fetchProfileData();
      setIsEditModalVisible(false);
    } catch (error) {
      console.error("Error updating profile:", error);
      message.error("Failed to update profile. Please try again.");
    } finally {
      setEditLoading(false);
    }
  };
  if (loading) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "20px",
            padding: "40px",
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
          }}
        >
          <Spin size="large" />
          <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
            Loading your profile...
          </Text>
        </div>
      </Layout>
    );
  }

  if (!profileData || !userData) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "20px",
            padding: "40px",
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
          }}
        >
          <Text style={{ color: "#ef4444", fontSize: "18px", fontWeight: 600 }}>
            Failed to load profile data
          </Text>
          <Button
            type="primary"
            onClick={() => window.location.reload()}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              borderRadius: "12px",
              height: "44px",
              paddingLeft: "24px",
              paddingRight: "24px",
              fontWeight: 600,
            }}
          >
            Retry
          </Button>
        </div>
      </Layout>
    );
  }

  const stats = [
    {
      title: "Assigned Exercises",
      value: assignedGames.length,
      change: 70,
      chartType: "line" as const,
      data: assignedGamesChartData,
    },
    {
      title: "Exercises Played",
      value: profileData.games_played_count,
      change: 78,
      chartType: "progress" as const,
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress" as const,
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress" as const,
    },
  ];

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1200,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Profile Header Card */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            overflow: "hidden",
            marginBottom: "32px",
          }}
        >
          <div
            style={{
              height: "200px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <img
              alt="banner"
              src="/assets/profilebanner.jpeg"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />

            <div
              style={{
                position: "absolute",
                top: "20px",
                right: "20px",
                zIndex: 10,
                backgroundColor: "#ef4444",
                padding: "10px 16px",
                borderRadius: "8px",
              }}
            >
              <LogoutDialog
                color="#ef4444"
                onLogout={handleLogoutClick}
                size="small"
              />
            </div>
          </div>

          {/* Profile Info Section */}
          <div
            style={{
              textAlign: "center",
              marginTop: "-80px",
              position: "relative",
              padding: "0 40px 40px",
            }}
          >
            {/* Avatar Section */}
            <div style={{ position: "relative", display: "inline-block" }}>
              <Avatar
                size={160}
                src={profileData.avatar_url}
                style={{
                  backgroundColor:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "6px solid white",
                  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.15)",
                }}
                icon={
                  !profileData.avatar_url && (
                    <UserOutlined style={{ fontSize: "48px" }} />
                  )
                }
              />
            </div>
            {/* User Info */}
            <div style={{ marginTop: "24px", marginBottom: "32px" }}>
              <Title
                level={2}
                style={{
                  margin: "0 0 8px 0",
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  fontWeight: 700,
                  fontSize: "32px",
                }}
              >
                {`${profileData.first_name} ${profileData.last_name}`}
              </Title>
              <Text
                style={{
                  color: "#6b7280",
                  fontSize: "16px",
                  fontWeight: 500,
                }}
              >
                {userData.user_metadata.email}
              </Text>
            </div>

            {/* Action Buttons */}
            <Row gutter={16} justify="center">
              <Col xs={24} sm={12} md={8}>
                <Button
                  icon={<MailOutlined />}
                  style={{
                    width: "100%",
                    height: "48px",
                    borderRadius: "12px",
                    border: "2px solid #e5e7eb",
                    background: "white",
                    color: "#6b7280",
                    fontSize: "16px",
                    fontWeight: 600,
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.borderColor = "#667eea";
                    target.style.color = "#667eea";
                    target.style.boxShadow =
                      "0 0 0 3px rgba(102, 126, 234, 0.1)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.borderColor = "#e5e7eb";
                    target.style.color = "#6b7280";
                    target.style.boxShadow = "none";
                  }}
                  onClick={handleContactClick}
                >
                  Contact
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  icon={<EditOutlined />}
                  type="primary"
                  onClick={handleEditProfile}
                  style={{
                    width: "100%",
                    background:
                      "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    border: "none",
                    borderRadius: "12px",
                    height: "48px",
                    fontSize: "16px",
                    fontWeight: 600,
                    boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(-2px)";
                    target.style.boxShadow =
                      "0 8px 25px rgba(102, 126, 234, 0.5)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(0)";
                    target.style.boxShadow =
                      "0 4px 15px rgba(102, 126, 234, 0.4)";
                  }}
                >
                  Edit Profile
                </Button>
              </Col>
            </Row>
          </div>
        </div>

        {/* Statistics Section */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
            marginBottom: "32px",
          }}
        >
          <Title
            level={3}
            style={{
              textAlign: "center",
              marginBottom: "40px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              fontSize: "28px",
            }}
          >
            📊 Activity Statistics
          </Title>

          <Row gutter={[24, 24]}>
            {stats.map((item, index) => {
              const getStatIcon = (title: string) => {
                if (title.includes("Exercises") && title.includes("Assigned"))
                  return <PlayCircleOutlined />;
                if (title.includes("Exercises") && title.includes("Played"))
                  return <TrophyOutlined />;
                if (title.includes("Workshops")) return <CalendarOutlined />;
                return <TrophyOutlined />;
              };

              const getGradientColor = (index: number) => {
                const gradients = [
                  "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
                  "linear-gradient(135deg, #ef4444 0%, #dc2626 100%)",
                ];
                return gradients[index % gradients.length];
              };

              return (
                <Col xs={24} sm={12} lg={6} key={index}>
                  <div
                    style={{
                      background: "rgba(255, 255, 255, 0.8)",
                      borderRadius: "20px",
                      padding: "24px",
                      border: "1px solid rgba(226, 232, 240, 0.5)",
                      transition: "all 0.3s ease",
                      // cursor: "pointer",
                      minHeight: "270px",
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                    }}
                  >
                    <div style={{ marginBottom: "16px" }}>
                      <div
                        style={{
                          width: "48px",
                          height: "48px",
                          borderRadius: "12px",
                          background: getGradientColor(index),
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          marginBottom: "16px",
                        }}
                      >
                        <span style={{ color: "white", fontSize: "20px" }}>
                          {getStatIcon(item.title)}
                        </span>
                      </div>
                      <Text
                        style={{
                          color: "#6b7280",
                          fontSize: "14px",
                          fontWeight: 500,
                          display: "block",
                          marginBottom: "8px",
                        }}
                      >
                        {item.title}
                      </Text>
                      <Title
                        level={2}
                        style={{
                          margin: 0,
                          color: "#374151",
                          fontWeight: 700,
                          fontSize: "32px",
                        }}
                      >
                        {item.value}
                      </Title>
                    </div>

                    {item.chartType === "progress" ? (
                      <div style={{ textAlign: "center" }}>
                        <Progress
                          type="circle"
                          percent={Math.abs(item.change)}
                          strokeColor={getGradientColor(index)}
                          size={80}
                          strokeWidth={8}
                        />
                        <Text
                          style={{
                            display: "block",
                            marginTop: "8px",
                            color: item.change < 0 ? "#ef4444" : "#10b981",
                            fontSize: "12px",
                            fontWeight: 600,
                          }}
                        >
                          {item.change < 0 ? "▼" : "▲"} {Math.abs(item.change)}%
                        </Text>
                      </div>
                    ) : (
                      <div>
                        <Text
                          style={{
                            color: item.change < 0 ? "#ef4444" : "#10b981",
                            fontSize: "12px",
                            fontWeight: 600,
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          {item.change < 0 ? "▼" : "▲"} {Math.abs(item.change)}%
                        </Text>
                        <div style={{ height: 80 }}>
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={item.data}>
                              <Line
                                type="monotone"
                                dataKey="value"
                                stroke={item.change < 0 ? "#ef4444" : "#10b981"}
                                strokeWidth={3}
                                dot={false}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    )}
                  </div>
                </Col>
              );
            })}
          </Row>
        </div>
      </Content>

      {/* Edit Profile Modal */}
      <EditUserModal
        visible={isEditModalVisible}
        user={
          profileData && userData
            ? {
                id: Number(userData.id),
                name: `${profileData.first_name || ""} ${
                  profileData.last_name || ""
                }`.trim(),
                first_name: profileData.first_name || "",
                last_name: profileData.last_name || "",
                email: profileData.email,
                //  role: roles.length > 0 ? roles[0].display_name || roles[0].role_name : '',
                role: mapPartyTypeToRoleName(currentUserRole) || "",
                gender: profileData.gender,
                bio: profileData.bio || "",
                phone_number: profileData.phone,
                avatar: profileData.avatar_url || "",
              }
            : null
        }
        roles={roles}
        loading={editLoading}
        onCancel={handleEditModalClose}
        onSubmit={handleEditSubmit}
        isProfileEdit={true}
      />
      <Modal
        title={null}
        visible={isContactModalVisible}
        onCancel={handleContactModalClose}
        footer={null}
        centered
        bodyStyle={{
          padding: "40px",
          borderRadius: "20px",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        }}
        style={{
          borderRadius: 20,
        }}
      >
        <div style={{ textAlign: "center" }}>
          <MailOutlined
            style={{
              fontSize: "40px",
              color: "#4f46e5",
              marginBottom: "20px",
            }}
          />
          <Title
            level={3}
            style={{
              marginBottom: "24px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
            }}
          >
            Contact Information
          </Title>

          <div style={{ textAlign: "left", marginBottom: "20px" }}>
            <p style={{ fontSize: "16px", marginBottom: 10 }}>
              <strong style={{ color: "#4b5563" }}>Email:</strong>{" "}
              <a
                href={`mailto:${userData?.email}`}
                style={{ color: "#4b5563" }}
              >
                {userData?.email || "Not available"}
              </a>
            </p>
            <p style={{ fontSize: "16px" }}>
              <strong style={{ color: "#4b5563" }}>Phone:</strong>{" "}
              {profileData?.phone ? (
                <a
                  href={`tel:${profileData.phone}`}
                  style={{ color: "#4b5563" }}
                >
                  {profileData.phone}
                </a>
              ) : (
                "Not available"
              )}
            </p>
          </div>

          <Button
            type="primary"
            size="large"
            onClick={handleContactModalClose}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              borderRadius: "12px",
              height: "44px",
              fontWeight: 600,
              padding: "0 24px",
            }}
          >
            Close
          </Button>
        </div>
      </Modal>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .profile-container {
          animation: fadeInUp 0.6s ease-out;
        }
      `}</style>
    </Layout>
  );
};

export default Profile;
