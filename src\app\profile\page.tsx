"use client";

import {
  <PERSON><PERSON>,
  Button,
  Typography,
  Row,
  Col,
  Progress,
  Spin,
  message,
  Upload,
  Modal,
  Layout,
} from "antd";
import {
  MailOutlined,
  UserOutlined,
  EditOutlined,
  CameraOutlined,
  TrophyOutlined,
  PlayCircleOutlined,
  CalendarOutlined,
  LogoutOutlined,
} from "@ant-design/icons";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { LineChart, Line, ResponsiveContainer } from "recharts";
import { dataProviderInstance } from "@providers/data-provider";
import { supabaseClient } from "@utils/supabase/client";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { AssignedGame, ProfileData, UserData } from "@types";
import LogoutDialog from "@components/dialog/logoutDialog";
import { authProviderClient } from "@providers/auth-provider/auth-provider.client";

const { Title, Text } = Typography;
const { Content } = Layout;

const assignedGamesChartData = [
  { value: 20 },
  { value: 15 },
  { value: 10 },
  { value: 5 },
  { value: 12 },
  { value: 10 },
  { value: 12 },
];

const Profile: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [assignedGames, setAssignedGames] = useState<AssignedGame[]>([]);

  const fetchProfileData = async () => {
    try {
      setLoading(true);

      const userResponse = await dataProviderInstance.getUser();
      const userId = userResponse.data?.id;

      if (!userId) {
        throw new Error("User not found. Please login again.");
      }

      if (!userResponse.data || !userResponse.data?.user_metadata) {
        throw new Error("Incomplete user data received from backend.");
      }

      setUserData({
        id: userResponse.data.id,
        email: userResponse.data.email || "",
        user_metadata: {
          first_name: userResponse.data.user_metadata?.first_name || "",
          last_name: userResponse.data.user_metadata?.last_name || "",
          username: userResponse.data.user_metadata?.username || "",
          email: userResponse.data.user_metadata?.email || "",
        },
      });

      const profileResponse = await dataProviderInstance.getProfileDetails({
        party_id: userId,
      });

      const profile = profileResponse?.data?.data;

      if (profileResponse.data.status !== "success" || !profile) {
        throw new Error("Failed to fetch profile details");
      }

      setProfileData(profile);

      const userInfoResponse = await dataProviderInstance.getUserInfo(userId);
      const currentUserRole = userInfoResponse.data.data.party_type_key;

      const tenant_code = localStorage.getItem("tenant_code");

      let gamesResponse;

      if (currentUserRole === "SUPERADMIN") {
        const allGamesResponse = await dataProviderInstance.listGames();

        if (!allGamesResponse?.data) {
          throw new Error("No games found");
        }

        const assignedGamesOnly = allGamesResponse.data.filter(
          (game: AssignedGame) =>
            Array.isArray(game.assigned_parties) &&
            game.assigned_parties.length > 0
        );

        gamesResponse = { data: assignedGamesOnly };
      } else {
        if (!tenant_code) {
          throw new Error("Tenant code not found in localStorage");
        }

        gamesResponse = await dataProviderInstance.listTenantGames({
          tenant_code,
        });
      }

      if (!gamesResponse?.data) {
        throw new Error("No assigned games found");
      }

      setAssignedGames(gamesResponse.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Error:", errorMessage);
      message.error(ERROR_MESSAGES.load_profile);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfileData();
  }, []);

  const handleLogoutClick = async () => {
    try {
      authProviderClient.logout({});
      localStorage.clear();
      sessionStorage.clear();
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      router.push("/login");
    }
  };

  const handleAvatarUpload = async (file: File) => {
    try {
      setUploadingAvatar(true);
      const fileName = `avatars/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 24 * 365);

      if (signedUrlError) throw signedUrlError;

      const avatarUrl = signedUrlData?.signedUrl;
      if (!avatarUrl) throw new Error("Failed to get avatar URL");

      await dataProviderInstance.updateProfileInfo({
        user_id: userData!.id,
        avatar_url: avatarUrl,
      });

      setProfileData((prev) =>
        prev ? { ...prev, avatar_url: avatarUrl } : null
      );

      message.success("Avatar updated successfully!");
      setShowAvatarModal(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update avatar";
      message.error(errorMessage);
    } finally {
      setUploadingAvatar(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        return false;
      }

      handleAvatarUpload(file);
      return false;
    },
    showUploadList: false,
  };

  if (loading) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "20px",
            padding: "40px",
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
          }}
        >
          <Spin size="large" />
          <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
            Loading your profile...
          </Text>
        </div>
      </Layout>
    );
  }

  if (!profileData || !userData) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "20px",
            padding: "40px",
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
          }}
        >
          <Text style={{ color: "#ef4444", fontSize: "18px", fontWeight: 600 }}>
            Failed to load profile data
          </Text>
          <Button
            type="primary"
            onClick={() => window.location.reload()}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              borderRadius: "12px",
              height: "44px",
              paddingLeft: "24px",
              paddingRight: "24px",
              fontWeight: 600,
            }}
          >
            Retry
          </Button>
        </div>
      </Layout>
    );
  }

  const stats = [
    {
      title: "Assigned Games",
      value: assignedGames.length,
      change: 70,
      chartType: "line" as const,
      data: assignedGamesChartData,
    },
    {
      title: "Games Played",
      value: profileData.games_played_count,
      change: 78,
      chartType: "progress" as const,
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress" as const,
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress" as const,
    },
  ];

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1200,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Profile Header Card */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            overflow: "hidden",
            marginBottom: "32px",
          }}
        >
          {/* Banner Image */}
          <div
            style={{
              height: "200px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <img
              alt="banner"
              src="/assets/profilebanner.jpeg"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
                opacity: 0.8,
              }}
            />
            <div
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: "linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%)",
              }}
            />
          </div>
          {/* Profile Info Section */}
          <div
            style={{
              textAlign: "center",
              marginTop: "-80px",
              position: "relative",
              padding: "0 40px 40px",
            }}
          >
            {/* Avatar Section */}
            <div style={{ position: "relative", display: "inline-block" }}>
              <Avatar
                size={160}
                src={profileData.avatar_url}
                style={{
                  backgroundColor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "6px solid white",
                  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.15)",
                }}
                icon={!profileData.avatar_url && <UserOutlined style={{ fontSize: "48px" }} />}
              />
              <Button
                shape="circle"
                icon={<CameraOutlined />}
                style={{
                  position: "absolute",
                  bottom: 8,
                  right: 8,
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "3px solid white",
                  color: "white",
                  width: "44px",
                  height: "44px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 4px 12px rgba(102, 126, 234, 0.4)",
                  transition: "all 0.3s ease",
                }}
                onClick={() => setShowAvatarModal(true)}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "scale(1.1)";
                  target.style.boxShadow = "0 6px 20px rgba(102, 126, 234, 0.6)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "scale(1)";
                  target.style.boxShadow = "0 4px 12px rgba(102, 126, 234, 0.4)";
                }}
              />
            </div>

            {/* User Info */}
            <div style={{ marginTop: "24px", marginBottom: "32px" }}>
              <Title
                level={2}
                style={{
                  margin: "0 0 8px 0",
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  fontWeight: 700,
                  fontSize: "32px",
                }}
              >
                {profileData.name ||
                  `${profileData.first_name} ${profileData.last_name}`}
              </Title>
              <Text
                style={{
                  color: "#6b7280",
                  fontSize: "16px",
                  fontWeight: 500,
                }}
              >
                {userData.user_metadata.email}
              </Text>
            </div>

            {/* Action Buttons */}
            <Row gutter={16} justify="center">
              <Col xs={24} sm={12} md={8}>
                <Button
                  icon={<MailOutlined />}
                  style={{
                    width: "100%",
                    height: "48px",
                    borderRadius: "12px",
                    border: "2px solid #e5e7eb",
                    background: "white",
                    color: "#6b7280",
                    fontSize: "16px",
                    fontWeight: 600,
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.borderColor = "#667eea";
                    target.style.color = "#667eea";
                    target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.borderColor = "#e5e7eb";
                    target.style.color = "#6b7280";
                    target.style.boxShadow = "none";
                  }}
                >
                  Contact
                </Button>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button
                  icon={<EditOutlined />}
                  type="primary"
                  style={{
                    width: "100%",
                    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    border: "none",
                    borderRadius: "12px",
                    height: "48px",
                    fontSize: "16px",
                    fontWeight: 600,
                    boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(-2px)";
                    target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(0)";
                    target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                  }}
                >
                  Edit Profile
                </Button>
              </Col>
            </Row>
          </div>
        </div>

        {/* Statistics Section */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
            marginBottom: "32px",
          }}
        >
          <Title
            level={3}
            style={{
              textAlign: "center",
              marginBottom: "40px",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              fontSize: "28px",
            }}
          >
            📊 Activity Statistics
          </Title>

          <Row gutter={[24, 24]}>
            {stats.map((item, index) => {
              const getStatIcon = (title: string) => {
                if (title.includes("Games") && title.includes("Assigned")) return <PlayCircleOutlined />;
                if (title.includes("Games") && title.includes("Played")) return <TrophyOutlined />;
                if (title.includes("Workshops")) return <CalendarOutlined />;
                return <TrophyOutlined />;
              };

              const getGradientColor = (index: number) => {
                const gradients = [
                  "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
                  "linear-gradient(135deg, #ef4444 0%, #dc2626 100%)",
                ];
                return gradients[index % gradients.length];
              };

              return (
                <Col xs={24} sm={12} lg={6} key={index}>
                  <div
                    style={{
                      background: "rgba(255, 255, 255, 0.8)",
                      borderRadius: "20px",
                      padding: "24px",
                      border: "1px solid rgba(226, 232, 240, 0.5)",
                      transition: "all 0.3s ease",
                      cursor: "pointer",
                    }}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.transform = "translateY(-4px)";
                      target.style.boxShadow = "0 12px 30px rgba(0, 0, 0, 0.15)";
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.transform = "translateY(0)";
                      target.style.boxShadow = "none";
                    }}
                  >
                    <div style={{ marginBottom: "16px" }}>
                      <div
                        style={{
                          width: "48px",
                          height: "48px",
                          borderRadius: "12px",
                          background: getGradientColor(index),
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          marginBottom: "16px",
                        }}
                      >
                        <span style={{ color: "white", fontSize: "20px" }}>
                          {getStatIcon(item.title)}
                        </span>
                      </div>
                      <Text
                        style={{
                          color: "#6b7280",
                          fontSize: "14px",
                          fontWeight: 500,
                          display: "block",
                          marginBottom: "8px",
                        }}
                      >
                        {item.title}
                      </Text>
                      <Title
                        level={2}
                        style={{
                          margin: 0,
                          color: "#374151",
                          fontWeight: 700,
                          fontSize: "32px",
                        }}
                      >
                        {item.value}
                      </Title>
                    </div>

                    {item.chartType === "progress" ? (
                      <div style={{ textAlign: "center" }}>
                        <Progress
                          type="circle"
                          percent={Math.abs(item.change)}
                          strokeColor={getGradientColor(index)}
                          size={80}
                          strokeWidth={8}
                        />
                        <Text
                          style={{
                            display: "block",
                            marginTop: "8px",
                            color: item.change < 0 ? "#ef4444" : "#10b981",
                            fontSize: "12px",
                            fontWeight: 600,
                          }}
                        >
                          {item.change < 0 ? "▼" : "▲"} {Math.abs(item.change)}%
                        </Text>
                      </div>
                    ) : (
                      <div>
                        <Text
                          style={{
                            color: item.change < 0 ? "#ef4444" : "#10b981",
                            fontSize: "12px",
                            fontWeight: 600,
                            marginBottom: "8px",
                            display: "block",
                          }}
                        >
                          {item.change < 0 ? "▼" : "▲"} {Math.abs(item.change)}%
                        </Text>
                        <div style={{ height: 50 }}>
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={item.data}>
                              <Line
                                type="monotone"
                                dataKey="value"
                                stroke={item.change < 0 ? "#ef4444" : "#10b981"}
                                strokeWidth={3}
                                dot={false}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    )}
                  </div>
                </Col>
              );
            })}
          </Row>
        </div>

        {/* Logout Section */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
            textAlign: "center",
          }}
        >
          <Title
            level={4}
            style={{
              marginBottom: "24px",
              color: "#374151",
              fontWeight: 600,
            }}
          >
            Account Actions
          </Title>
          <Button
            icon={<LogoutOutlined />}
            style={{
              width: "100%",
              maxWidth: "300px",
              height: "48px",
              borderRadius: "12px",
              border: "2px solid #ef4444",
              background: "white",
              color: "#ef4444",
              fontSize: "16px",
              fontWeight: 600,
              transition: "all 0.3s ease",
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.style.background = "#ef4444";
              target.style.color = "white";
              target.style.transform = "translateY(-2px)";
              target.style.boxShadow = "0 8px 25px rgba(239, 68, 68, 0.4)";
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.style.background = "white";
              target.style.color = "#ef4444";
              target.style.transform = "translateY(0)";
              target.style.boxShadow = "none";
            }}
          >
            <LogoutDialog color="inherit" onLogout={handleLogoutClick} />
          </Button>
        </div>
      </Content>

      {/* Avatar Upload Modal */}
      <Modal
        title={
          <Text style={{ fontSize: "18px", fontWeight: 600, color: "#374151" }}>
            Update Profile Picture
          </Text>
        }
        open={showAvatarModal}
        onCancel={() => setShowAvatarModal(false)}
        footer={null}
        width={450}
        style={{
          borderRadius: "20px",
        }}
      >
        <div
          style={{
            textAlign: "center",
            padding: "30px 20px",
            background: "rgba(248, 250, 252, 0.8)",
            borderRadius: "16px",
            margin: "20px 0",
          }}
        >
          <div
            style={{
              width: "80px",
              height: "80px",
              margin: "0 auto 24px",
              borderRadius: "50%",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <CameraOutlined style={{ color: "white", fontSize: "32px" }} />
          </div>

          <Upload {...uploadProps}>
            <Button
              icon={<EditOutlined />}
              type="primary"
              size="large"
              loading={uploadingAvatar}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                borderRadius: "12px",
                height: "48px",
                paddingLeft: "32px",
                paddingRight: "32px",
                fontSize: "16px",
                fontWeight: 600,
                marginBottom: "16px",
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
              }}
            >
              {uploadingAvatar ? "Uploading..." : "Choose Image"}
            </Button>
          </Upload>

          <Text
            style={{
              display: "block",
              fontSize: "12px",
              color: "#6b7280",
              fontStyle: "italic",
            }}
          >
            📷 Supported formats: JPG, PNG, GIF (Max 5MB)
          </Text>
        </div>
      </Modal>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .profile-container {
          animation: fadeInUp 0.6s ease-out;
        }
      `}</style>
    </Layout>
  );
};

export default Profile;
