"use client";
import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  Typography,
  Select,
  Upload,
  Switch,
  Row,
  Col,
  message,
  Layout,
} from "antd";
import {
  UploadOutlined,
  UserAddOutlined,
  BuildOutlined,
} from "@ant-design/icons";
import { dataProviderInstance } from "@/providers/data-provider";
import { BackButton } from "@components/common";
const { Title, Text } = Typography;
const { Content } = Layout;
import { useNotification } from "@refinedev/core";
import { useRouter } from "next/navigation";
import { TenantFormValues } from "@types";
import { supabaseClient } from "@utils/supabase/client";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
const { TextArea } = Input;

export default function TenantCreatePage() {
  const [form] = Form.useForm();
  const [loginRequired, setLoginRequired] = useState(true);
  const { open } = useNotification();
  const router = useRouter();
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  // const party_id = localStorage.getItem("party_id");

  useEffect(() => {
    const fetchCode = async () => {
      try {
        console.log("📋 Fetching new tenant code...");
        const rolesResponse = await dataProviderInstance.getNextTenantCode();
        console.log("tenant code:", rolesResponse.data.data.tenant_code);
        const tenantCode = rolesResponse.data.data.tenant_code;

        // Set the form field value
        form.setFieldsValue({
          tenant_code: tenantCode,
        });
      } catch (error: unknown) {
        console.error("❌ Error in fetchData:", error);
        message.error(
          `Failed to load required data: ${
            (error as Error)?.message || "Unknown error"
          }`
        );
      } finally {
        console.log("🏁 fetchData completed");
      }
    };

    fetchCode();
  }, [form]);

  useEffect(() => {
    setLoginRequired(true);
  }, []);

  const handleFinish = async (values: TenantFormValues) => {
    let thumbnailUrl = uploadedImageUrl;
    if (imageFile) {
      const fileName = `tenants/${Date.now()}_${imageFile.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, imageFile, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) {
        open?.({
          type: "error",
          message: "Failed to upload image.",
        });
      }

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 157680000); // 1 hour expiry

      if (signedUrlData && signedUrlData.signedUrl) {
        thumbnailUrl = signedUrlData.signedUrl;
      } else {
        console.log(signedUrlError);
        throw new Error("Failed to retrieve signed URL for uploaded image.");
      }
    }

    try {
      const params = {
        name: values.name as string,
        mobile: `${values.countryCode} ${values.mobile}`,
        address: values.address as string,
        email: values.tenant_email as string,
        tenant_code: values.tenant_code as string,
        image_url: thumbnailUrl as string,
      };
      const addTenentResult = await dataProviderInstance.addTenent(params);
      if (addTenentResult) {
        open?.({
          type: "success",
          message: SUCCESS_MESSAGES.create_tenant,
        });
      }
      let authUserId = null;
      // if (loginRequired) {
      //   console.log(addTenentResult);
      //   try {
      //     const { data, error } = await supabaseClient.auth.signUp({
      //       email: values.email as string,
      //       password: values.password as string,
      //       options: {
      //         data: {
      //           first_name: values.firstName,
      //           last_name: values.lastName,
      //           phone_number: values.countryCode + " " + values.mobile,
      //           username: values.username,
      //           tenant_code: values.tenant_code,
      //           party_type_key: "TENANTADM",
      //           party_id: party_id,
      //         },
      //       },
      //     });
      //     if (data && !error) {
      //       open?.({
      //         type: "success",
      //         message: SUCCESS_MESSAGES.tenant_signup,
      //       });
      //       router.push("/tenants");
      //     }
      //     if (error) {
      //       open?.({
      //         type: "error",
      //         message: ERROR_MESSAGES.tenant_signup,
      //       });
      //     }
      //   } catch (err) {
      //     open?.({
      //       type: "error",
      //       message: "An error occurred during signup.",
      //     });
      //     console.error("Signup error:", err);
      //   }
      // }
      if (loginRequired) {
        console.log(addTenentResult);
        const signUpResponse = await dataProviderInstance.signUpUser({
          email: values.email!,
          password: values.password!,
          // first_name: values.firstName!,
          // last_name: values.lastName!,
          // phone_number: `${values.countryCode!} ${values.phone!}`,
          // username: values.username!,
          // tenant_code: values.tenant_code,
          // party_id: party_id!,
          // rolename: "ADMIN",
          // avatar_url: null,
          // party_type_key: "TENANTADM",
        });
        if (!signUpResponse.data?.user?.id) {
          throw new Error("Failed to create user. No user ID returned.");
        }
        authUserId = signUpResponse.data.user.id;
        const userDetails = JSON.parse(localStorage.getItem("user") || "{}");
        const userId = authUserId;
        const reqparams = {
          p_first_name: values.firstName as string,
          p_last_name: values.lastName as string,
          p_email: values.email as string,
          p_address: "",
          p_phone: values.countryCode + " " + values.phone,
          p_username: values.username as string,
          p_tenant_code: values.tenant_code as string,
          p_rolename: "ADMIN",
          p_avatar_url: "",
          p_party_type_key: "TENANTADM",
          p_party_id: userDetails.id as string,
          p_user_id: userId as string,
        };
        await dataProviderInstance.updateParty(reqparams);
        router.push("/tenants");
        console.log("Auth user created:", authUserId);
      }
    } catch (error) {
      open?.({
        type: "error",
        message: ERROR_MESSAGES.create_tenant,
      });
      console.error("Failed to create tenant:", error);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "150px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1200,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header Section */}
        <div
          style={{
            textAlign: "center",
            marginBottom: "48px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            padding: "40px 32px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "16px",
              marginBottom: "16px",
            }}
          >
            <BackButton to="/tenants" title="Back to Organizations" />
            <Title
              level={1}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                margin: 0,
                fontSize: "36px",
                letterSpacing: "-1px",
              }}
            >
              Create New Organization
            </Title>
          </div>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              lineHeight: "1.6",
            }}
          >
            Add a new organization with admin user credentials to your platform
          </Text>
        </div>

        {/* Main Form Container */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "40px",
          }}
        >
          <Form
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            onFinish={handleFinish}
          >
            {/* Tenant Details Section */}
            <div
              style={{
                marginBottom: 48,
                background: "rgba(248, 250, 252, 0.8)",
                borderRadius: "20px",
                padding: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  marginBottom: "32px",
                }}
              >
                <div
                  style={{
                    width: "48px",
                    height: "48px",
                    borderRadius: "12px",
                    background:
                      "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <BuildOutlined style={{ color: "white", fontSize: "20px" }} />
                </div>
                <Title
                  level={3}
                  style={{
                    margin: 0,
                    color: "#374151",
                    fontWeight: 700,
                    fontSize: "24px",
                  }}
                >
                  Organization Information
                </Title>
              </div>
              <Row gutter={32}>
                <Col xs={24} md={15}>
                  <Form.Item
                    name="name"
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                        }}
                      >
                        Name
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[{ required: true, message: "Name is required" }]}
                  >
                    <Input
                      size="large"
                      placeholder="Enter Name"
                      style={{
                        borderRadius: "12px",
                        height: "48px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow =
                          "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="tenant_email"
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                        }}
                      >
                        Email Address
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[
                      { required: true, message: "Email is required" },
                      {
                        type: "email",
                        message: "Please enter a valid email address",
                      },
                    ]}
                  >
                    <Input
                      size="large"
                      placeholder="Enter email address"
                      style={{
                        borderRadius: "12px",
                        height: "48px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow =
                          "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="address"
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                        }}
                      >
                        Address
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[{ required: true, message: "Address is required" }]}
                  >
                    <TextArea
                      placeholder="Enter complete address"
                      rows={6}
                      style={{
                        borderRadius: "12px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        resize: "none",
                        transition: "all 0.3s ease",
                      }}
                      onFocus={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.borderColor = "#667eea";
                        target.style.boxShadow =
                          "0 0 0 3px rgba(102, 126, 234, 0.1)";
                      }}
                      onBlur={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.borderColor = "#e5e7eb";
                        target.style.boxShadow = "none";
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col
                  xs={24}
                  md={8}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                    justifyContent: "space-between",
                  }}
                >
                  <div
                    style={{
                      background: "rgba(255, 255, 255, 0.8)",
                      borderRadius: "16px",
                      padding: "20px",
                      border: "2px dashed #e5e7eb",
                      textAlign: "center",
                      marginBottom: "16px",
                    }}
                  >
                    <div
                      style={{
                        width: "100%",
                        height: "280px",
                        borderRadius: "12px",
                        overflow: "hidden",
                        background: "#f8fafc",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginBottom: "16px",
                      }}
                    >
                      <img
                        src={uploadedImageUrl || "/assets/upload.png"}
                        alt="Tenant Profile Preview"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: uploadedImageUrl ? "cover" : "contain",
                          borderRadius: "12px",
                        }}
                      />
                    </div>
                    <Form.Item
                      name="profileImage"
                      label={
                        <Text
                          style={{
                            color: "#374151",
                            fontWeight: 600,
                            fontSize: "14px",
                          }}
                        >
                          Organization Profile Image
                        </Text>
                      }
                      style={{ marginBottom: 0 }}
                    >
                      <Upload {...uploadProps}>
                        <Button
                          icon={<UploadOutlined />}
                          style={{
                            width: "100%",
                            height: "44px",
                            borderRadius: "12px",
                            border: "2px solid #667eea",
                            color: "#667eea",
                            fontWeight: 600,
                            background: "rgba(102, 126, 234, 0.05)",
                            transition: "all 0.3s ease",
                          }}
                          onMouseEnter={(e) => {
                            const target = e.target as HTMLElement;
                            target.style.background = "#667eea";
                            target.style.color = "white";
                          }}
                          onMouseLeave={(e) => {
                            const target = e.target as HTMLElement;
                            target.style.background =
                              "rgba(102, 126, 234, 0.05)";
                            target.style.color = "#667eea";
                          }}
                        >
                          Choose Image
                        </Button>
                      </Upload>
                    </Form.Item>
                  </div>
                </Col>
                <Col xs={24} md={15}>
                  <Form.Item
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                        }}
                      >
                        Phone Number
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    required
                  >
                    <div style={{ display: "flex" }}>
                      <Form.Item
                        name="countryCode"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: "Country code is required",
                          },
                        ]}
                        initialValue="+91"
                      >
                        <Select
                          size="large"
                          style={{
                            width: 120,
                            borderRadius: "12px",
                            fontSize: 16,
                          }}
                        >
                          <Select.Option value="+1">🇺🇸 +1</Select.Option>
                          <Select.Option value="+44">🇬🇧 +44</Select.Option>
                          <Select.Option value="+91">🇮🇳 +91</Select.Option>
                          <Select.Option value="+61">🇦🇺 +61</Select.Option>
                          <Select.Option value="+81">🇯🇵 +81</Select.Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        name="mobile"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: "Phone number is required",
                          },
                          {
                            pattern: /^\d{7,14}$/,
                            message: "Enter a valid phone number (7-14 digits)",
                          },
                        ]}
                      >
                        <Input
                          size="large"
                          style={{
                            width: "calc(100% - 120px)",
                            borderRadius: "12px",
                            border: "2px solid #e5e7eb",
                            transition: "all 0.3s ease",
                          }}
                          placeholder="Enter phone number"
                          maxLength={14}
                          minLength={7}
                          type="tel"
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </div>
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="tenant_code"
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                        }}
                      >
                        Organization Code
                      </Text>
                    }
                    style={{ marginBottom: 24 }}
                    rules={[
                      {
                        required: true,
                        message: "Organization Code is required",
                      },
                      {
                        pattern: /^TN-\d{4}$/,
                        message:
                          "Organization Code must be in the format TN-0001",
                      },
                    ]}
                  >
                    <Input
                      type="text"
                      size="large"
                      disabled={true}
                      style={{
                        borderRadius: "12px",
                        height: "48px",
                        border: "2px solid #e5e7eb",
                        fontSize: "16px",
                        background: "rgba(248, 250, 252, 0.8)",
                        color: "#6b7280",
                        fontWeight: 600,
                      }}
                    />
                  </Form.Item>
                  <Text
                    style={{
                      color: "#6b7280",
                      fontSize: "12px",
                      fontStyle: "italic",
                    }}
                  >
                    Auto-generated organization code
                  </Text>
                </Col>
              </Row>
            </div>

            {/* Admin User Details Section */}
            <div
              style={{
                marginBottom: 48,
                background: "rgba(248, 250, 252, 0.8)",
                borderRadius: "20px",
                padding: "32px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: "32px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  <div
                    style={{
                      width: "48px",
                      height: "48px",
                      borderRadius: "12px",
                      background:
                        "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <UserAddOutlined
                      style={{ color: "white", fontSize: "20px" }}
                    />
                  </div>
                  <Title
                    level={3}
                    style={{
                      margin: 0,
                      color: "#374151",
                      fontWeight: 700,
                      fontSize: "24px",
                    }}
                  >
                    Admin User Details
                  </Title>
                </div>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "12px",
                    padding: "8px 16px",
                    background: "rgba(16, 185, 129, 0.1)",
                    borderRadius: "20px",
                  }}
                >
                  <Switch checked={true} disabled={true} size="small" />
                  <Text
                    style={{
                      color: "#10b981",
                      fontSize: "14px",
                      fontWeight: 600,
                    }}
                  >
                    Login Enabled
                  </Text>
                </div>
              </div>
              {loginRequired && (
                <div>
                  <Row gutter={32}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        name="firstName"
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                            }}
                          >
                            First Name
                          </Text>
                        }
                        style={{ marginBottom: 24 }}
                        rules={[
                          { required: true, message: "First name is required" },
                        ]}
                      >
                        <Input
                          size="large"
                          placeholder="Enter first name"
                          style={{
                            borderRadius: "12px",
                            height: "48px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        name="lastName"
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                            }}
                          >
                            Last Name
                          </Text>
                        }
                        style={{ marginBottom: 24 }}
                        rules={[
                          { required: true, message: "Last name is required" },
                        ]}
                      >
                        <Input
                          size="large"
                          placeholder="Enter last name"
                          style={{
                            borderRadius: "12px",
                            height: "48px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={32}>
                    <Col xs={24} md={8}>
                      <Form.Item
                        name="username"
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                            }}
                          >
                            Username
                          </Text>
                        }
                        style={{ marginBottom: 24 }}
                        rules={[
                          { required: true, message: "Username is required" },
                        ]}
                      >
                        <Input
                          size="large"
                          placeholder="Enter username"
                          style={{
                            borderRadius: "12px",
                            height: "48px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={8}>
                      <Form.Item
                        name="email"
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                            }}
                          >
                            Admin Email
                          </Text>
                        }
                        style={{ marginBottom: 24 }}
                        rules={[
                          { required: true, message: "Email is required" },
                          {
                            type: "email",
                            message: "Please enter a valid email address",
                          },
                        ]}
                      >
                        <Input
                          size="large"
                          placeholder="Enter admin email"
                          style={{
                            borderRadius: "12px",
                            height: "48px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={8}>
                      <Form.Item
                        name="password"
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                            }}
                          >
                            Password
                          </Text>
                        }
                        style={{ marginBottom: 24 }}
                        rules={[
                          { required: true, message: "Password is required" },
                        ]}
                      >
                        <Input.Password
                          size="large"
                          placeholder="Enter password"
                          style={{
                            borderRadius: "12px",
                            height: "48px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#667eea";
                            target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.style.borderColor = "#e5e7eb";
                            target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div
              style={{
                textAlign: "center",
                marginTop: 48,
                padding: "32px",
                background: "rgba(255, 255, 255, 0.8)",
                borderRadius: "20px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Row gutter={24} justify="center">
                <Col xs={24} sm={12} md={8}>
                  <Button
                    style={{
                      width: "100%",
                      height: "48px",
                      borderRadius: "12px",
                      border: "2px solid #e5e7eb",
                      background: "white",
                      color: "#6b7280",
                      fontSize: "16px",
                      fontWeight: 600,
                      transition: "all 0.3s ease",
                    }}
                    onClick={() => router.push("/tenants")}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.borderColor = "#ef4444";
                      target.style.color = "#ef4444";
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.borderColor = "#e5e7eb";
                      target.style.color = "#6b7280";
                    }}
                  >
                    Cancel
                  </Button>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Button
                    type="primary"
                    size="large"
                    htmlType="submit"
                    style={{
                      width: "100%",
                      background:
                        "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                      border: "none",
                      borderRadius: "12px",
                      height: "48px",
                      fontSize: "16px",
                      fontWeight: 600,
                      boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                      transition: "all 0.3s ease",
                    }}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.transform = "translateY(-2px)";
                      target.style.boxShadow =
                        "0 8px 25px rgba(102, 126, 234, 0.5)";
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      target.style.transform = "translateY(0)";
                      target.style.boxShadow =
                        "0 4px 15px rgba(102, 126, 234, 0.4)";
                    }}
                  >
                    Create Organization
                  </Button>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .form-section {
          animation: fadeInUp 0.6s ease-out;
        }
      `}</style>
    </Layout>
  );
}
