import React, { useState } from "react";

import { Tenant } from "@types";
import { Modal, message } from "antd";
import { dataProviderInstance } from "@providers/data-provider";
interface DeleteTenantModalProps {
  visible: boolean;
  onClose: () => void;
  tenant: Tenant | null;
  onSuccess?: () => void;
}

const DeleteTenantModal: React.FC<DeleteTenantModalProps> = ({
  visible,
  onClose,
  tenant,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!tenant) return;

    setLoading(true);
    try {
      const response = await dataProviderInstance.deleteTenant(
        String(tenant.id)
      );
      const result = response?.data;

      if (result?.success === false) {
        if (
          result.message &&
          result.message.includes("Caregivers exist under this organization")
        ) {
          message.error(
            "Cannot delete organization. Caregivers exist under this organization"
          );
        } else {
          message.error(result.message || "Failed to delete Organization.");
        }
        return;
      }

      message.success("Organization deleted successfully!");
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error("Error deleting tenant:", error);
      message.error("Something went wrong while deleting. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      open={visible}
      title="Delete Organization"
      onOk={handleDelete}
      onCancel={onClose}
      okText="Yes"
      cancelText="No"
      okButtonProps={{ danger: true, loading }}
    >
      <p>
        Are you sure you want to delete{" "}
        <strong>{tenant?.name ?? "this tenant"}</strong>?
      </p>
    </Modal>
  );
};

export default DeleteTenantModal;
