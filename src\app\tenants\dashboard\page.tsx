"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>ton, Layout, Typography, Row, Col, Spin, Card } from "antd";
import StatCard, { StatCardProps } from "@components/card/graphCards/StatCard";
import {
  caregiversData,
  gamesPlayedTodayData,
  playersData,
} from "@utils/supabase/constants";
import { useRouter } from "next/navigation";
import { supabaseClient } from "@utils/supabase/client";
import { User as SupabaseUser } from "@supabase/supabase-js";
import { RPC } from "../../../../apiConfig";

const { Content } = Layout;
const { Title, Text } = Typography;

interface DashboardData {
  overview: {
    total_players: {
      count: number;
    };
    total_caregivers: {
      count: number;
    };
    total_users?: {
      count: number;
    };
  };
}

interface ApiResponse {
  data: DashboardData;
  success: boolean;
}

interface StorageData {
  user?: SupabaseUser;
  id?: string;
  access_token?: string;
  [key: string]: unknown;
}

const TenantEngagementDashboard = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const getCurrentUser = async (): Promise<SupabaseUser | null> => {
    try {
      const {
        data: { user },
        error,
      } = await supabaseClient.auth.getUser();

      if (error) {
        throw error;
      }

      return user;
    } catch (error) {
      console.error("Error getting current user:", error);
      return null;
    }
  };

  const getUserFromStorage = (): string | null => {
    try {
      const possibleKeys = [
        "sb-fxueprucjyukchfjwfop-auth-token",
        "supabase.auth.token",
        "sb-auth-token",
        "user",
      ];

      for (const key of possibleKeys) {
        const userData =
          localStorage.getItem(key) || sessionStorage.getItem(key);

        if (userData) {
          try {
            const parsedData: StorageData = JSON.parse(userData);

            if (parsedData?.user?.id) {
              return parsedData.user.id;
            }
            if (parsedData?.id) {
              return parsedData.id;
            }
            if (parsedData?.access_token) {
              continue;
            }
          } catch {
            // Ignore parse errors and continue to next key
            continue;
          }
        }
      }

      return null;
    } catch (error) {
      console.error("Error getting user from storage:", error);
      return null;
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const user = await getCurrentUser();
      let tenantId = user?.id;

      if (!tenantId) {
        tenantId = getUserFromStorage() ?? undefined;
      }

      if (!tenantId) {
        throw new Error("User not found. Please login again.");
      }

      console.log("Using tenant_id:", tenantId);
      const tenant_code = localStorage.getItem("tenant_code");

      const { data, error: apiError } = (await supabaseClient.rpc(
        RPC.getTenantDashboardSummary,
        { tenant_code: tenant_code }
      )) as { data: ApiResponse; error: unknown };

      if (apiError) {
        throw apiError;
      }

      if (data && data.success) {
        setDashboardData(data.data);
      } else {
        throw new Error("API call was not successful");
      }
    } catch (err) {
      console.error("Error fetching dashboard data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch dashboard data"
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const createStatCards = (): StatCardProps[] => {
    if (!dashboardData?.overview) {
      return [];
    }

    return [
      {
        title: "Total Caregivers",
        value: dashboardData.overview.total_caregivers?.count || 0,
        percentage: 85,
        status: "down",
        color: "#ef4444",
        chartType: "line",
        chartData: caregiversData,
      },
      {
        title: "Total Players",
        value: dashboardData.overview.total_players?.count || 0,
        percentage: 92,
        status: "up",
        color: "#10b981",
        chartType: "line",
        chartData: playersData,
      },
      {
        title: "Total Users",
        value:
          dashboardData.overview.total_users?.count ||
          dashboardData.overview.total_players?.count ||
          0,
        percentage: 78,
        status: "up",
        color: "#6366f1",
        chartType: "progress",
        chartData: gamesPlayedTodayData,
      },
    ];
  };

  const statCards = createStatCards();

  if (loading) {
    return (
      <Layout
        style={{
          maxWidth: 1280,
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          margin: "0 auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background decorative elements */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "300px",
            background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
            borderRadius: "0 0 50% 50%",
            transform: "scale(1.5)",
            zIndex: 0,
          }}
        />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
            width: "100%",
            zIndex: 1,
            position: "relative",
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              padding: "40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "20px",
            }}
          >
            <Spin size="large" />
            <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
              Loading tenant dashboard...
            </Text>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout
        style={{
          maxWidth: 1280,
          minHeight: "100vh",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          margin: "0 auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background decorative elements */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "300px",
            background: "linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%)",
            borderRadius: "0 0 50% 50%",
            transform: "scale(1.5)",
            zIndex: 0,
          }}
        />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
            width: "100%",
            zIndex: 1,
            position: "relative",
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              padding: "40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "20px",
              textAlign: "center",
            }}
          >
            <Text type="danger" style={{ fontSize: "18px", fontWeight: 600 }}>
              Error loading dashboard: {error}
            </Text>
            <div style={{ display: "flex", gap: "12px", flexWrap: "wrap", justifyContent: "center" }}>
              <Button
                type="primary"
                onClick={fetchDashboardData}
                style={{
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                  borderRadius: "8px",
                  fontWeight: 600,
                }}
              >
                Retry
              </Button>
              {error.includes("User not found") && (
                <Button
                  onClick={() => router.push("/login")}
                  style={{
                    borderRadius: "8px",
                    fontWeight: 600,
                  }}
                >
                  Go to Login
                </Button>
              )}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          maxWidth: "800px",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            Tenant Dashboard
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Manage your caregivers, users, and game assignments with ease
          </Text>
        </div>

        {/* Overview Section */}
        <div style={{ marginBottom: "48px", width: "100%", maxWidth: "1200px" }}>

          {/* Metrics Cards */}
          <Row gutter={[32, 32]} style={{ marginBottom: "48px" }}>
            {statCards.map((card, index) => (
              <Col xs={24} sm={12} lg={8} key={index}>
                <div
                  style={{
                    animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`,
                  }}
                >
                  <StatCard {...card} />
                </div>
              </Col>
            ))}
          </Row>

          {/* Status Summary */}
          <Card
            style={{
              textAlign: "center",
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              borderRadius: "20px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              marginBottom: "64px",
              padding: "24px",
            }}
          >
            <Title level={4} style={{
              marginBottom: "24px",
              color: "#374151",
              fontWeight: 600,
            }}>
              Tenant Statistics
            </Title>
            <Row gutter={[32, 16]} justify="center">
              <Col xs={24} sm={12}>
                <div style={{
                  padding: "16px",
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                }}>
                  <Text style={{
                    color: "#374151",
                    fontSize: "16px",
                    fontWeight: 500,
                    display: "block",
                    marginBottom: "8px",
                  }}>
                    Total Platform Users
                  </Text>
                  <Text style={{
                    color: "#667eea",
                    fontSize: "24px",
                    fontWeight: 700,
                  }}>
                    {(dashboardData?.overview.total_caregivers?.count ?? 0) +
                     (dashboardData?.overview.total_players?.count ?? 0)}
                  </Text>
                  <Text style={{
                    color: "#6b7280",
                    fontSize: "14px",
                    display: "block",
                    marginTop: "4px",
                  }}>
                    Caregivers & Players
                  </Text>
                </div>
              </Col>
              <Col xs={24} sm={12}>
                <div style={{
                  padding: "16px",
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%)",
                }}>
                  <Text style={{
                    color: "#374151",
                    fontSize: "16px",
                    fontWeight: 500,
                    display: "block",
                    marginBottom: "8px",
                  }}>
                    System Status
                  </Text>
                  <Text style={{
                    color: "#10b981",
                    fontSize: "24px",
                    fontWeight: 700,
                  }}>
                    Active
                  </Text>
                  <Text style={{
                    color: "#6b7280",
                    fontSize: "14px",
                    display: "block",
                    marginTop: "4px",
                  }}>
                    All systems operational
                  </Text>
                </div>
              </Col>
            </Row>
          </Card>
        </div>

        {/* Quick Actions Section */}
        <div style={{
          textAlign: "center",
          width: "100%",
          maxWidth: "800px",
          background: "rgba(255, 255, 255, 0.6)",
          backdropFilter: "blur(10px)",
          borderRadius: "24px",
          padding: "48px 32px",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}>
          <Title
            level={2}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "32px",
              letterSpacing: "-0.5px",
            }}
          >
            Quick Actions
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "40px",
              lineHeight: "1.6",
            }}
          >
            Take immediate steps to enhance Caregiver, User, and game management
            effortlessly.
          </Text>
          <Row gutter={[24, 24]} justify="center">
            <Col xs={24} sm={8}>
              <Button
                type="primary"
                size="large"
                block
                style={{
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                  height: "56px",
                  fontSize: "16px",
                  fontWeight: 600,
                  borderRadius: "12px",
                  boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-2px)";
                  target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                }}
                onClick={() => {
                  router.push("/tenants/addPlayer");
                }}
              >
                Add User
              </Button>
            </Col>
            <Col xs={24} sm={8}>
              <Button
                size="large"
                block
                style={{
                  background: "rgba(255, 255, 255, 0.8)",
                  border: "2px solid #667eea",
                  color: "#667eea",
                  height: "56px",
                  fontSize: "16px",
                  fontWeight: 600,
                  borderRadius: "12px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#667eea";
                  target.style.color = "white";
                  target.style.transform = "translateY(-2px)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "rgba(255, 255, 255, 0.8)";
                  target.style.color = "#667eea";
                  target.style.transform = "translateY(0)";
                }}
                onClick={() => {
                  router.push("/tenants/viewUsers");
                }}
              >
                View Users
              </Button>
            </Col>
            <Col xs={24} sm={8}>
              <Button
                size="large"
                block
                style={{
                  background: "rgba(255, 255, 255, 0.8)",
                  border: "2px solid #10b981",
                  color: "#10b981",
                  height: "56px",
                  fontSize: "16px",
                  fontWeight: 600,
                  borderRadius: "12px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "#10b981";
                  target.style.color = "white";
                  target.style.transform = "translateY(-2px)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.background = "rgba(255, 255, 255, 0.8)";
                  target.style.color = "#10b981";
                  target.style.transform = "translateY(0)";
                }}
                onClick={() => {
                  router.push("/tenants/assignGames");
                }}
              >
                Assign Games
              </Button>
            </Col>
          </Row>
        </div>
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .dashboard-container {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>
    </Layout>
  );
};

export default TenantEngagementDashboard;
