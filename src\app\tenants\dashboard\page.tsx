"use client";
import React, { useState, useEffect } from "react";
import { Button, Layout, Space, Typography, Row, Col, Spin } from "antd";
import StatCard, { StatCardProps } from "@components/card/graphCards/StatCard";
import {
  caregiversData,
  gamesPlayedTodayData,
  playersData,
} from "@utils/supabase/constants";
import { useRouter } from "next/navigation";
import { supabaseClient } from "@utils/supabase/client";
import { User as SupabaseUser } from "@supabase/supabase-js";
import { RPC } from "../../../../apiConfig";

const { Content } = Layout;
const { Title, Text } = Typography;

interface DashboardData {
  overview: {
    total_players: {
      count: number;
    };
    total_caregivers: {
      count: number;
    };
    total_users?: {
      count: number;
    };
  };
}

interface ApiResponse {
  data: DashboardData;
  success: boolean;
}

interface StorageData {
  user?: SupabaseUser;
  id?: string;
  access_token?: string;
  [key: string]: unknown;
}

const TenantEngagementDashboard = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const getCurrentUser = async (): Promise<SupabaseUser | null> => {
    try {
      const {
        data: { user },
        error,
      } = await supabaseClient.auth.getUser();

      if (error) {
        throw error;
      }

      return user;
    } catch (error) {
      console.error("Error getting current user:", error);
      return null;
    }
  };

  const getUserFromStorage = (): string | null => {
    try {
      const possibleKeys = [
        "sb-fxueprucjyukchfjwfop-auth-token",
        "supabase.auth.token",
        "sb-auth-token",
        "user",
      ];

      for (const key of possibleKeys) {
        const userData =
          localStorage.getItem(key) || sessionStorage.getItem(key);

        if (userData) {
          try {
            const parsedData: StorageData = JSON.parse(userData);

            if (parsedData?.user?.id) {
              return parsedData.user.id;
            }
            if (parsedData?.id) {
              return parsedData.id;
            }
            if (parsedData?.access_token) {
              continue;
            }
          } catch {
            // Ignore parse errors and continue to next key
            continue;
          }
        }
      }

      return null;
    } catch (error) {
      console.error("Error getting user from storage:", error);
      return null;
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const user = await getCurrentUser();
      let tenantId = user?.id;

      if (!tenantId) {
        tenantId = getUserFromStorage() ?? undefined;
      }

      if (!tenantId) {
        throw new Error("User not found. Please login again.");
      }

      console.log("Using tenant_id:", tenantId);
      const tenant_code = localStorage.getItem("tenant_code");

      const { data, error: apiError } = (await supabaseClient.rpc(
        RPC.getTenantDashboardSummary,
        { tenant_code: tenant_code }
      )) as { data: ApiResponse; error: unknown };

      if (apiError) {
        throw apiError;
      }

      if (data && data.success) {
        setDashboardData(data.data);
      } else {
        throw new Error("API call was not successful");
      }
    } catch (err) {
      console.error("Error fetching dashboard data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch dashboard data"
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const createStatCards = (): StatCardProps[] => {
    if (!dashboardData?.overview) {
      return [];
    }

    return [
      {
        title: "Total Caregivers",
        value: dashboardData.overview.total_caregivers?.count || 0,
        percentage: 85,
        status: "down",
        color: "#ef4444",
        chartType: "line",
        chartData: caregiversData,
      },
      {
        title: "Total Players",
        value: dashboardData.overview.total_players?.count || 0,
        percentage: 92,
        status: "up",
        color: "#10b981",
        chartType: "line",
        chartData: playersData,
      },
      {
        title: "Total Users",
        value:
          dashboardData.overview.total_users?.count ||
          dashboardData.overview.total_players?.count ||
          0,
        percentage: 78,
        status: "up",
        color: "#6366f1",
        chartType: "progress",
        chartData: gamesPlayedTodayData,
      },
    ];
  };

  const statCards = createStatCards();

  if (loading) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          backgroundColor: "#f5f5f5",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <div style={{ textAlign: "center" }}>
          <Spin size="large" />
          <div style={{ marginTop: "16px" }}>
            <Text>Loading dashboard data...</Text>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout
        style={{
          minHeight: "100vh",
          backgroundColor: "#f5f5f5",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <div style={{ textAlign: "center" }}>
          <Text type="danger" style={{ fontSize: "16px" }}>
            Error loading dashboard: {error}
          </Text>
          <br />
          <Button
            type="primary"
            onClick={fetchDashboardData}
            style={{ marginTop: "16px" }}
          >
            Retry
          </Button>
          {error.includes("User not found") && (
            <Button
              type="default"
              onClick={() => router.push("/login")}
              style={{ marginTop: "8px", marginLeft: "8px" }}
            >
              Go to Login
            </Button>
          )}
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      style={{
        minHeight: "100vh",
        backgroundColor: "#f5f5f5",
        maxWidth: 1280,
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Content
        style={{
          padding: "32px 100px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <div style={{ marginBottom: "48px", width: "100%" }}>
          <Title
            level={2}
            style={{
              marginBottom: "24px",
              color: "#1f2937",
              textAlign: "center",
            }}
          >
            Overview
          </Title>

          <Row
            gutter={[24, 24]}
            style={{ marginBottom: "32px", justifyContent: "center" }}
          >
            {statCards.map((card, index) => (
              <Col xs={24} md={8} key={index}>
                <StatCard {...card} />
              </Col>
            ))}
          </Row>
        </div>

        <div
          style={{
            textAlign: "center",
            backgroundColor: "#f9fafb",
            padding: "32px",
            borderRadius: "12px",
            width: "100%",
            maxWidth: 700,
            margin: "0 auto",
          }}
        >
          <Title
            level={1}
            style={{
              color: "#6366f1",
              marginBottom: "16px",
              textAlign: "center",
            }}
          >
            Quick Actions
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "16px",
              display: "block",
              marginBottom: "32px",
              maxWidth: "600px",
              margin: "0 auto 32px auto",
              textAlign: "center",
            }}
          >
            Take immediate steps to enhance Caregiver, User, and game management
            effortlessly.
          </Text>
          <Space size="large">
            {/* <Button
              type="primary"
              size="large"
              style={{
                backgroundColor: "#6366f1",
                borderColor: "#6366f1",
                height: "48px",
                paddingLeft: "32px",
                paddingRight: "32px",
                fontSize: "16px",
                borderRadius: "8px",
              }}
              onClick={() => {
                router.push("/tenants/addCaregiver");
              }}
            >
              Add Caregiver
            </Button> */}
            <Button
              type="primary"
              size="large"
              style={{
                backgroundColor: "#6366f1",
                borderColor: "#6366f1",
                height: "48px",
                paddingLeft: "32px",
                paddingRight: "32px",
                fontSize: "16px",
                borderRadius: "8px",
              }}
              onClick={() => {
                router.push("/tenants/addPlayer");
              }}
            >
              Add User
            </Button>
            <Button
              type="primary"
              size="large"
              style={{
                backgroundColor: "#6366f1",
                borderColor: "#6366f1",
                height: "48px",
                paddingLeft: "32px",
                paddingRight: "32px",
                fontSize: "16px",
                borderRadius: "8px",
              }}
              onClick={() => {
                router.push("/tenants/assignGames");
              }}
            >
              Assign Games
            </Button>
          </Space>
        </div>
      </Content>
    </Layout>
  );
};

export default TenantEngagementDashboard;
