"use client";
import React, { useState, useEffect } from "react";
import { Row, Col, Button, Input, Spin, message, Layout, Typography } from "antd";
import UserCard from "@components/card/userCards/userCard";

import { dataProviderInstance } from "@providers/data-provider";
import { PLAYER } from "@utils/supabase/constants";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import StatCard from "@components/card/graphCards/StatCard";

const { Content } = Layout;
const { Title, Text } = Typography;

export type GetUserInfoResponse = {
  data: {
    tenant_code: string | null;
    id: string;
    party_type_key: string;
    parent_party_id: string;
  };
  success: boolean;
};

type Player = {
  id: string;
  name: string;
  email?: string | null;
  avatar_url?: string | null;
  type: string;
};

type DashboardData = {
  overview: {
    total_users: { count: number };
    exercises_completed: { count: number };
    total_caregivers: { count: number };
    rewards_completed:{count: number};
  };
};

const Dashboard: React.FC = () => {
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [userData, setUserData] = useState<GetUserInfoResponse | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );

  useEffect(() => {
    const fetchPlayers = async () => {
      try {
        setLoading(true);
        const party_id = localStorage.getItem("party_id") || "";
        //const party_type_key = localStorage.getItem("role") || "";
        const tenant_code = localStorage.getItem("tenant_code") || "";
        const inputPara = {
          party_id: party_id,
          tenant_code: tenant_code,
          party_type_key: "CAREGIVER",
        };
        const response = await dataProviderInstance.listTenantUsers(inputPara);
        const playersList: Player[] = (response.data.data.players || []).map(
          (p: Player) => ({
            id: p.id,
            name: p.name,
            email: p.email,
            avatar_url: p.avatar_url,
            type: PLAYER,
          })
        );
        setPlayers(playersList);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
      } finally {
        setLoading(false);
      }
    };
    fetchPlayers();
  }, []);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const { data: user } = await dataProviderInstance.getUser();
        if (user?.id) {
          setCurrentUserId(user.id);
        }
      } catch (error) {
        console.error("Error getting current user:", error);
      }
    };
    fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (!currentUserId) return;
    const fetchUserInfo = async () => {
      try {
        const { data } = await dataProviderInstance.getUserInfo(currentUserId);
        if (data) {
          setUserData(data);
        }
      } catch (error) {
        console.error("Error fetching user info:", error);
        message.error("Failed to load user info");
      }
    };
    fetchUserInfo();
  }, [currentUserId]);

  useEffect(() => {
    if (!userData) return;
    console.log("userdata");
    console.log(userData);
    const fetchDashboardData = async () => {
      try {
        setDashboardLoading(true);
        const response = await dataProviderInstance.getUserDashboard({
          tenant_id: userData.data.parent_party_id,
          party_id: userData.data.id,
          party_type_key: userData.data.party_type_key,
        });
        setDashboardData(response.data.data);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        message.error("Failed to load dashboard statistics");
      } finally {
        setDashboardLoading(false);
      }
    };
    fetchDashboardData();
  }, [userData]);

  const filteredPlayers = players.filter(
    (player) =>
      player.name?.toLowerCase().includes(search.toLowerCase()) ||
      player.email?.toLowerCase().includes(search.toLowerCase())
  );

  const getStatCards = () => {
    const overview = dashboardData?.overview;

    const totalUsers = overview?.total_users?.count;
    const totalExercises = overview?.exercises_completed?.count;
    const totalCaregivers = overview?.total_caregivers?.count;
    const totalRewards = overview?.rewards_completed?.count;


    console.log("=== StatCard Debug Info ===");
    console.log("totalUsers count:", totalUsers);
    console.log("totalPlayers count:", totalExercises);
    console.log("totalCaregivers count:", totalCaregivers);
    console.log("Raw overview data:", overview);

    return [
      {
        title: "Active users",
        value: totalUsers,
        percentage: 12,
        status: "up" as const,
        color: "#10b981",
        chartType: "progress" as const,
        unit: "",
      },
      {
        title: "Exercises Completed",
        value: totalExercises,
        percentage: 8,
        status: "up" as const,
        color: "#3b82f6",
        chartType: "line" as const,
        unit: "",
      },
      {
        title: "Rewards Achieved",
        value: totalRewards,
        percentage: 5,
        status: "up" as const,
        color: "#f59e0b",
        chartType: "progress" as const,
        unit: "",
      },
    ];
  };

  const statCards = getStatCards();

  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "40px 20px",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Welcome Header */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          maxWidth: "800px",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "48px",
              letterSpacing: "-1px",
            }}
          >
            Wellness Facilitator Dashboard
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
            }}
          >
            Manage your players and track their progress
          </Text>
        </div>

        {/* Action Bar */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "32px",
            width: "100%",
            maxWidth: "1200px",
            background: "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
            borderRadius: "16px",
            padding: "20px 24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          <Button
            type="primary"
            size="large"
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              height: "48px",
              fontSize: "16px",
              fontWeight: 600,
              borderRadius: "12px",
              boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
              transition: "all 0.3s ease",
              padding: "0 32px",
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.style.transform = "translateY(-2px)";
              target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.style.transform = "translateY(0)";
              target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
            }}
            onClick={() => (window.location.href = "/caregiver/userManagement")}
          >
            Create New User
          </Button>
          <Input.Search
            placeholder="Search by name or email..."
            allowClear
            size="large"
            style={{
              width: 300,
              borderRadius: "12px",
              background: "rgba(255, 255, 255, 0.9)",
              border: "1px solid rgba(255, 255, 255, 0.3)",
            }}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>

        {/* Players Section */}
        {loading ? (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              height: "40vh",
              width: "100%",
              zIndex: 1,
              position: "relative",
            }}
          >
            <div
              style={{
                background: "rgba(255, 255, 255, 0.9)",
                backdropFilter: "blur(10px)",
                borderRadius: "20px",
                padding: "40px",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "20px",
              }}
            >
              <Spin size="large" />
              <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
                Loading players...
              </Text>
            </div>
          </div>
        ) : filteredPlayers.length === 0 ? (
          <div
            style={{
              textAlign: "center",
              padding: "60px 0",
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              margin: "20px",
              minWidth: "300px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            }}
          >
            <Text style={{ color: "#6b7280", fontSize: "18px", fontWeight: 500 }}>
              {search
                ? "No players found matching your search."
                : "No players available."}
            </Text>
          </div>
        ) : (
          <div style={{ width: "100%", maxWidth: "1200px", marginBottom: "48px" }}>
            <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
              {filteredPlayers.map((player, index) => (
                <Col xs={24} sm={12} md={8} lg={8} xl={8} key={player.id}>
                  <div
                    style={{
                      animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`,
                    }}
                  >
                    <UserCard
                      id={player.id}
                      name={player.name || "No Name"}
                      title={player.email || "No Email"}
                      location=""
                      avatar={player.avatar_url || undefined}
                    />
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        )}

        {/* Statistics Section */}
        <div style={{ width: "100%", maxWidth: "1200px" }}>
          <div style={{
            textAlign: "center",
            marginBottom: "32px",
          }}>
            <Title
              level={2}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                marginBottom: "16px",
                fontSize: "32px",
                letterSpacing: "-0.5px",
              }}
            >
              Performance Statistics
            </Title>
            <Text
              style={{
                color: "#6b7280",
                fontSize: "16px",
                fontWeight: 500,
                display: "block",
              }}
            >
              Track your caregiving impact and player progress
            </Text>
          </div>

          {dashboardLoading ? (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "30vh",
                width: "100%",
              }}
            >
              <div
                style={{
                  background: "rgba(255, 255, 255, 0.9)",
                  backdropFilter: "blur(10px)",
                  borderRadius: "20px",
                  padding: "40px",
                  boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: "20px",
                }}
              >
                <Spin size="large" />
                <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
                  Loading statistics...
                </Text>
              </div>
            </div>
          ) : (
            <Row gutter={[32, 32]}>
              {statCards.map((card, index) => (
                <Col xs={24} sm={12} lg={8} key={`${card.title}-${index}`}>
                  <div
                    style={{
                      animation: `fadeInUp 0.6s ease-out ${(index + 3) * 0.1}s both`,
                    }}
                  >
                    <StatCard {...card} value={card.value ?? 0} />
                  </div>
                </Col>
              ))}
            </Row>
          )}
        </div>
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .dashboard-container {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>
    </Layout>
  );
};

export default Dashboard;
