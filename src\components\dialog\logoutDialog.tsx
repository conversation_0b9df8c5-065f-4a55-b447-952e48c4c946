import { Button, Modal, Typography } from "antd";
import { useState } from "react";
import { PoweroffOutlined, ExclamationCircleOutlined } from "@ant-design/icons";

const { Text, Title } = Typography;

interface LogoutDialogProps {
  onLogout: () => void;
  color?: string;
  size?: "small" | "medium" | "large";
  showText?: boolean;
}

const LogoutDialog: React.FC<LogoutDialogProps> = ({
  onLogout,
  color = "#ef4444",
  size = "medium",
  showText = true,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    onLogout();
    localStorage.clear();
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // Size configurations for the logout button
  const sizeConfig = {
    small: { height: "32px", fontSize: "12px", padding: "0 12px" },
    medium: { height: "40px", fontSize: "14px", padding: "0 16px" },
    large: { height: "48px", fontSize: "16px", padding: "0 20px" },
  };

  const buttonStyle = {
    color: "#fff",
    border: `1px solid transparent`,
    background: color,
    borderRadius: "8px",
    height: sizeConfig[size].height,

    fontSize: "15px",
    padding: sizeConfig[size].padding,
    fontWeight: 500,
    transition: "all 0.3s ease",
    display: "flex",
    alignItems: "center",
    gap: "6px",
  };

  return (
    <>
      <Button
        type="text"
        icon={<PoweroffOutlined />}
        onClick={showModal}
        style={buttonStyle}
      >
        {showText && "Logout"}
      </Button>

      <Modal
        title={null}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
        centered
        width={450}
        style={{
          borderRadius: "20px",
          overflow: "hidden",
        }}
        styles={{
          content: {
            padding: 0,
            background: "rgba(255, 255, 255, 0.98)",
            backdropFilter: "blur(20px)",
            borderRadius: "20px",
            border: "1px solid rgba(255, 255, 255, 0.3)",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
          },
          mask: {
            backdropFilter: "blur(8px)",
            background: "rgba(248, 250, 252, 0.8)",
          },
        }}
      >
        <div
          style={{
            padding: "40px 32px",
            textAlign: "center",
            position: "relative",
          }}
        >
          {/* Decorative background element */}
          <div
            style={{
              position: "absolute",
              top: "20px",
              left: "50%",
              transform: "translateX(-50%)",
              width: "100px",
              height: "100px",
              background: `linear-gradient(135deg, ${color}20 0%, ${color}10 100%)`,
              borderRadius: "50%",
              zIndex: 0,
            }}
          />

          {/* Icon */}
          <div
            style={{
              width: "60px",
              height: "60px",
              margin: "0 auto 24px",
              borderRadius: "50%",
              background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              position: "relative",
              zIndex: 1,
              boxShadow: `0 8px 24px ${color}40`,
            }}
          >
            <ExclamationCircleOutlined
              style={{
                fontSize: "26px",
                color: "white",
              }}
            />
          </div>

          {/* Title */}
          <Title
            level={3}
            style={{
              background: "linear-gradient(135deg, #374151 0%, #1f2937 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "12px",
              fontSize: "22px",
              letterSpacing: "-0.5px",
            }}
          >
            Confirm Logout
          </Title>

          {/* Description */}
          <Text
            style={{
              color: "#6b7280",
              fontSize: "15px",
              fontWeight: 400,
              display: "block",
              marginBottom: "32px",
              lineHeight: "1.6",
            }}
          >
            Are you sure you want to logout? You will need to sign in again to
            access your account.
          </Text>

          {/* Action Buttons */}
          <div
            style={{
              display: "flex",
              gap: "12px",
              justifyContent: "center",
            }}
          >
            <Button
              onClick={handleCancel}
              style={{
                background: "rgba(255, 255, 255, 0.8)",
                border: "2px solid #e5e7eb",
                color: "#6b7280",
                height: "46px",
                fontSize: "15px",
                fontWeight: 600,
                borderRadius: "12px",
                padding: "0 24px",
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.background = "#f3f4f6";
                target.style.borderColor = "#d1d5db";
                target.style.transform = "translateY(-1px)";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.background = "rgba(255, 255, 255, 0.8)";
                target.style.borderColor = "#e5e7eb";
                target.style.transform = "translateY(0)";
              }}
            >
              Cancel
            </Button>

            <Button
              type="primary"
              onClick={handleOk}
              style={{
                background: `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`,
                border: "none",
                height: "46px",
                fontSize: "15px",
                fontWeight: 600,
                borderRadius: "12px",
                padding: "0 24px",
                boxShadow: `0 4px 15px ${color}40`,
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(-2px)";
                target.style.boxShadow = `0 8px 25px ${color}50`;
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(0)";
                target.style.boxShadow = `0 4px 15px ${color}40`;
              }}
            >
              Yes, Logout
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default LogoutDialog;
