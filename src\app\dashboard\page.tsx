"use client";
import React, { useState, useEffect } from "react";
import {
  Card,
  Button,
  Layout,
  Typography,
  Row,
  Col,
  Spin,
} from "antd";
import StatCard, { StatCardProps } from "@components/card/graphCards/StatCard";
import { caregiverData, tenantData, userData } from "@utils/supabase/constants";
import { dataProviderInstance } from "@providers/data-provider";
import { DashboardOverviewResponse } from "@types";

const { Content } = Layout;
const { Title, Text } = Typography;
const TenantEngagementDashboard = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<StatCardProps[]>([]);
  const [dashboardOverview, setDashboardOverview] =
    useState<DashboardOverviewResponse>();
  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data } = await dataProviderInstance.getAdminDashboardData();
        setIsLoading(false);
        if (data) {
          setDashboardOverview(data);
          const detailsArray: StatCardProps[] = [
            {
              title: "Total Tenants",
              value: data.data.overview.total_tenants?.count ?? 0,
              percentage: 92,
              status: "up",
              color: "#10b981",
              chartType: "line",
              chartData: caregiverData,
            },
            {
              title: "Total Caregivers",
              value: data.data.overview.total_caregivers?.count ?? 0,
              percentage: 0,
              status: "up",
              color: "#6366f1",
              chartType: "progress",
              chartData: userData,
            },
            {
              title: "Total Users",
              value: data.data.overview.total_users?.count ?? 0,
              percentage: 85,
              status: "down",
              color: "#ef4444",
              chartType: "line",
              chartData: tenantData,
            },
          ];

          setDashboardData(detailsArray);
        }
      } catch (e) {
        console.log(e);
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);
  return (
    <Layout
      style={{
        maxWidth: 1280,
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        margin: "0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background: "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      {isLoading ? (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
            width: "100%",
            zIndex: 1,
            position: "relative",
          }}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.9)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              padding: "40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "20px",
            }}
          >
            <Spin size="large" />
            <Text style={{ color: "#6b7280", fontSize: "16px", fontWeight: 500 }}>
              Loading dashboard...
            </Text>
          </div>
        </div>
      ) : (
        <Content
          style={{
            padding: "40px 20px",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            position: "relative",
            zIndex: 1,
          }}
        >
          {/* Welcome Header */}
          <div style={{
            textAlign: "center",
            marginBottom: "48px",
            maxWidth: "800px",
          }}>
            <Title
              level={1}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                marginBottom: "16px",
                fontSize: "48px",
                letterSpacing: "-1px",
              }}
            >
              Dashboard Overview
            </Title>
            <Text
              style={{
                color: "#6b7280",
                fontSize: "18px",
                fontWeight: 500,
                display: "block",
                marginBottom: "32px",
              }}
            >
              Monitor your platform's performance and key metrics at a glance
            </Text>
          </div>

          {/* Overview Section */}
          <div style={{ marginBottom: "48px", width: "100%", maxWidth: "1200px" }}>

            {/* Metrics Cards */}
            <Row gutter={[32, 32]} style={{ marginBottom: "48px" }}>
              {dashboardData.map((card, index) => (
                <Col xs={24} sm={12} lg={8} key={index}>
                  <div
                    style={{
                      animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`,
                    }}
                  >
                    <StatCard {...card} />
                  </div>
                </Col>
              ))}
            </Row>

            {/* Status Summary */}
            <Card
              style={{
                textAlign: "center",
                background: "rgba(255, 255, 255, 0.8)",
                backdropFilter: "blur(10px)",
                boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                borderRadius: "20px",
                border: "1px solid rgba(255, 255, 255, 0.2)",
                marginBottom: "64px",
                padding: "24px",
              }}
            >
              <Title level={4} style={{
                marginBottom: "24px",
                color: "#374151",
                fontWeight: 600,
              }}>
                Platform Statistics
              </Title>
              <Row gutter={[32, 16]} justify="center">
                <Col xs={24} sm={12}>
                  <div style={{
                    padding: "16px",
                    borderRadius: "12px",
                    background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                  }}>
                    <Text style={{
                      color: "#374151",
                      fontSize: "16px",
                      fontWeight: 500,
                      display: "block",
                      marginBottom: "8px",
                    }}>
                      Total Platform Users
                    </Text>
                    <Text style={{
                      color: "#667eea",
                      fontSize: "24px",
                      fontWeight: 700,
                    }}>
                      {(dashboardOverview?.data.overview.total_tenants?.count ?? 0) +
                       (dashboardOverview?.data.overview.total_caregivers?.count ?? 0)}
                    </Text>
                    <Text style={{
                      color: "#6b7280",
                      fontSize: "14px",
                      display: "block",
                      marginTop: "4px",
                    }}>
                      Tenants & Caregivers
                    </Text>
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div style={{
                    padding: "16px",
                    borderRadius: "12px",
                    background: "linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%)",
                  }}>
                    <Text style={{
                      color: "#374151",
                      fontSize: "16px",
                      fontWeight: 500,
                      display: "block",
                      marginBottom: "8px",
                    }}>
                      System Status
                    </Text>
                    <Text style={{
                      color: "#10b981",
                      fontSize: "24px",
                      fontWeight: 700,
                    }}>
                      Active
                    </Text>
                    <Text style={{
                      color: "#6b7280",
                      fontSize: "14px",
                      display: "block",
                      marginTop: "4px",
                    }}>
                      All systems operational
                    </Text>
                  </div>
                </Col>
              </Row>
            </Card>
          </div>

          {/* Quick Actions Section */}
          <div style={{
            textAlign: "center",
            width: "100%",
            maxWidth: "800px",
            background: "rgba(255, 255, 255, 0.6)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            padding: "48px 32px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}>
            <Title
              level={2}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                marginBottom: "16px",
                fontSize: "32px",
                letterSpacing: "-0.5px",
              }}
            >
              Quick Actions
            </Title>
            <Text
              style={{
                color: "#6b7280",
                fontSize: "18px",
                fontWeight: 500,
                display: "block",
                marginBottom: "40px",
                lineHeight: "1.6",
              }}
            >
              Take immediate steps to enhance tenant, game, and reward
              management effortlessly.
            </Text>
            <Row gutter={[24, 24]} justify="center">
              <Col xs={24} sm={8}>
                <Button
                  type="primary"
                  size="large"
                  block
                  style={{
                    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    border: "none",
                    height: "56px",
                    fontSize: "16px",
                    fontWeight: 600,
                    borderRadius: "12px",
                    boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(-2px)";
                    target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.transform = "translateY(0)";
                    target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                  }}
                >
                  Manage Tenants
                </Button>
              </Col>
              <Col xs={24} sm={8}>
                <Button
                  size="large"
                  block
                  style={{
                    background: "rgba(255, 255, 255, 0.8)",
                    border: "2px solid #667eea",
                    color: "#667eea",
                    height: "56px",
                    fontSize: "16px",
                    fontWeight: 600,
                    borderRadius: "12px",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.background = "#667eea";
                    target.style.color = "white";
                    target.style.transform = "translateY(-2px)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.background = "rgba(255, 255, 255, 0.8)";
                    target.style.color = "#667eea";
                    target.style.transform = "translateY(0)";
                  }}
                >
                  View Games
                </Button>
              </Col>
              <Col xs={24} sm={8}>
                <Button
                  size="large"
                  block
                  style={{
                    background: "rgba(255, 255, 255, 0.8)",
                    border: "2px solid #10b981",
                    color: "#10b981",
                    height: "56px",
                    fontSize: "16px",
                    fontWeight: 600,
                    borderRadius: "12px",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.background = "#10b981";
                    target.style.color = "white";
                    target.style.transform = "translateY(-2px)";
                  }}
                  onMouseLeave={(e) => {
                    const target = e.target as HTMLElement;
                    target.style.background = "rgba(255, 255, 255, 0.8)";
                    target.style.color = "#10b981";
                    target.style.transform = "translateY(0)";
                  }}
                >
                  Manage Rewards
                </Button>
              </Col>
            </Row>
          </div>
        </Content>
      )}

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .dashboard-container {
          animation: fadeInUp 0.8s ease-out;
        }
      `}</style>
    </Layout>
  );
};

export default TenantEngagementDashboard;
