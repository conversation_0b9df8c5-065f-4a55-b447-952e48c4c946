"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Modal,
  Progress,
  Row,
  Space,
  Spin,
  Typography,
  Upload,
  message,
} from "antd";
import {
  CameraOutlined,
  EditOutlined,
  MailOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Line, LineChart, ResponsiveContainer } from "recharts";
import { dataProviderInstance } from "@providers/data-provider";
import { authProviderClient } from "@providers/auth-provider/auth-provider.client";
import LogoutDialog from "@components/dialog/logoutDialog";
import { supabaseClient } from "@utils/supabase/client";

const { Title, Text } = Typography;

interface Profile {
  name: string;
  email?: string;
  bio: string | null;
  last_name: string;
  avatar_url: string | null;
  first_name: string;
  games_played_count: number;
  assigned_games_count: number;
}

const ProfileById = () => {
  const router = useRouter();
  const { id } = useParams();
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  useEffect(() => {
    if (!id) return;

    const fetchProfile = async () => {
      try {
        const res = await dataProviderInstance.getProfileDetails({
          party_id: id as string,
        });
        setProfile(res.data.data);

        const userResponse = await dataProviderInstance.getUser();
        const userId = userResponse.data?.id;

        if (userId && userId === id) {
          setIsOwnProfile(true);
        }
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  const handleContactClick = () => {
    // custom logic for contacting user
  };

  const handleLogoutClick = async () => {
    try {
      authProviderClient.logout({});
      localStorage.clear();
      sessionStorage.clear();
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      router.push("/login");
    }
  };

  const handleAvatarUpload = async (file: File) => {
    try {
      setUploadingAvatar(true);

      const fileName = `avatars/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 24 * 365);

      if (signedUrlError) throw signedUrlError;

      const avatarUrl = signedUrlData?.signedUrl;

      if (!avatarUrl) {
        throw new Error("Failed to get avatar URL");
      }

      setProfile((prev) => (prev ? { ...prev, avatar_url: avatarUrl } : null));

      message.success("Avatar updated successfully!");
      setShowAvatarModal(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update avatar";
      message.error(errorMessage);
    } finally {
      setUploadingAvatar(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        return false;
      }

      handleAvatarUpload(file);
      return false;
    },
    showUploadList: false,
  };

  const assignedGamesChartData = [
    { value: 20 },
    { value: 15 },
    { value: 10 },
    { value: 5 },
    { value: 12 },
    { value: 10 },
    { value: 12 },
  ];

  const stats = [
    {
      title: "Assigned Exercises",
      value: profile?.assigned_games_count || 0,
      change: -12.5,
      chartType: "line",
      data: assignedGamesChartData,
    },
    {
      title: "Exercises Played",
      value: profile?.games_played_count || 0,
      change: 78,
      chartType: "progress",
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress",
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress",
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: "center", marginTop: 100 }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!profile) {
    return <Text>Profile not found.</Text>;
  }

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 12,
        boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
        padding: 32,
      }}
    >
      <Card
        style={{ borderRadius: 16 }}
        cover={
          <img
            alt="banner"
            src="/assets/profilebanner.jpeg"
            style={{
              height: 160,
              objectFit: "cover",
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
            }}
          />
        }
      >
        <div
          style={{ textAlign: "center", marginTop: -64, position: "relative" }}
        >
          <div style={{ position: "relative", display: "inline-block" }}>
            <Avatar
              size={186}
              src={profile.avatar_url}
              style={{ backgroundColor: "#ff7f50" }}
              icon={!profile.avatar_url && <UserOutlined />}
            />
            {isOwnProfile && (
              <Button
                shape="circle"
                icon={<CameraOutlined />}
                size="small"
                style={{
                  position: "absolute",
                  bottom: 8,
                  right: 8,
                  background: "#ffffff",
                  boxShadow: "0 2px 6px rgba(0,0,0,0.2)",
                  border: "2px solid #fff",
                }}
                onClick={() => setShowAvatarModal(true)}
              />
            )}
          </div>

          <Title level={3} style={{ marginTop: 12 }}>
            {profile.name || `${profile.first_name} ${profile.last_name}`}
          </Title>

          <Button
            icon={<MailOutlined />}
            type="primary"
            block
            style={{ marginTop: 24 }}
            onClick={handleContactClick}
          >
            Contact
          </Button>
        </div>

        <Divider />

        <Row gutter={16}>
          {stats.map((item, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <Card>
                <Space direction="vertical" style={{ width: "100%" }}>
                  <Text type="secondary">{item.title}</Text>
                  <Title level={3} style={{ margin: 0 }}>
                    {item.value}
                  </Title>

                  {item.chartType === "progress" ? (
                    <Progress
                      type="dashboard"
                      percent={
                        item.title === "Completion Rate"
                          ? item.value
                          : Math.abs(item.change)
                      }
                      strokeColor={item.change < 0 ? "red" : "green"}
                      size={60}
                    />
                  ) : (
                    <>
                      <Text type="danger" style={{ fontSize: 14 }}>
                        ▼ {Math.abs(item.change)}%
                      </Text>
                      <div style={{ height: 40 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={item.data}>
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="red"
                              strokeWidth={2}
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </>
                  )}
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
        {isOwnProfile && (
          <Button type="primary" block style={{ marginTop: 32, color: "#fff" }}>
            <LogoutDialog color="#fff" onLogout={handleLogoutClick} />
          </Button>
        )}
      </Card>

      <Modal
        title="Update Profile Picture"
        open={showAvatarModal}
        onCancel={() => setShowAvatarModal(false)}
        footer={null}
        width={400}
      >
        <div style={{ textAlign: "center", padding: "20px 0" }}>
          <Upload {...uploadProps}>
            <Button
              icon={<EditOutlined />}
              type="primary"
              size="large"
              loading={uploadingAvatar}
              style={{ marginBottom: 16 }}
            >
              {uploadingAvatar ? "Uploading..." : "Choose Image"}
            </Button>
          </Upload>
          <Text type="secondary" style={{ display: "block", fontSize: 12 }}>
            Supported formats: JPG, PNG, GIF (Max 5MB)
          </Text>
        </div>
      </Modal>
    </div>
  );
};

export default ProfileById;
