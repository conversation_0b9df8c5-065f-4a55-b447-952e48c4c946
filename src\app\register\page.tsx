"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Alert,
  Checkbox,
  Card,
  Typography,
  Layout,
  Row,
  Col,
} from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";
import { supabaseClient } from "@/utils/supabase/client";
import { dataProviderInstance } from "@providers/data-provider";

const { Title, Text, Link } = Typography;
const { Content } = Layout;

export default function CustomRegister() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const router = useRouter();

  const onFinish = async (values: {
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
    terms: boolean;
    phone?: string;
    countryCode?: string;
    tenantCode: string;
  }) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Check if passwords match
    if (values.password !== values.confirmPassword) {
      setError("Passwords do not match");
      setLoading(false);
      return;
    }

    // Check if terms are accepted
    if (!values.terms) {
      setError("Please accept the terms and conditions");
      setLoading(false);
      return;
    }

    const { data, error } = await supabaseClient.auth.signUp({
      email: values.email,
      password: values.password,

      // options: {
      //   data: {
      //     first_name: values.firstName,
      //     last_name: values.lastName,
      //     phone: values.countryCode+ ' '+ values.phone,
      //     username: values.username,
      //     tenant_code: values.tenantCode,
      //     avatar_url: null,
      //     party_type_key: "PLAYER",
      //    }
      // }
    });

    const userId = data?.user?.id;
    const reqparams = {
      p_first_name: values.firstName,
      p_last_name: values.lastName,
      p_email: values.email,
      p_address: "",
      p_phone: values.countryCode + " " + values.phone,
      p_username: values.username,
      p_tenant_code: values.tenantCode,
      p_rolename: "PLAYER",
      p_avatar_url: "",
      p_party_type_key: "PLAYER",
      p_party_id: userId as string,
      p_user_id: userId as string,
    };
    await dataProviderInstance.updateParty(reqparams);
    setLoading(false);

    if (error) {
      setError(error.message);
    } else if (data?.user) {
      setSuccess("Registration successful! ");

      setTimeout(() => {
        router.push("/login");
      }, 3000);
    }
  };

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "300px",
          background:
            "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.5)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-50px",
          right: "-50px",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "-100px",
          left: "-100px",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(118, 75, 162, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          padding: "24px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          zIndex: 1,
        }}
      >
        <Card
          style={{
            width: "100%",
            maxWidth: "500px",
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "8px",
          }}
          styles={{ body: { padding: "32px" } }}
        >
          {/* Brand Header */}
          <div
            style={{
              textAlign: "center",
              marginBottom: "32px",
            }}
          >
            {/* Use next/image for optimization */}
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src="/assets/logo1.png"
              alt="Logo"
              style={{
                width: "120px",
                height: "120px",
                marginBottom: "16px",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                borderRadius: "50%",
                filter: "drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))",
              }}
            />
            <Title
              level={2}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                margin: "0 0 8px 0",
                fontSize: "32px",
                letterSpacing: "-0.5px",
              }}
            >
              Create Account
            </Title>
            <Text
              style={{
                color: "#6b7280",
                fontSize: "16px",
                fontWeight: 500,
              }}
            >
              Join us and start your journey
            </Text>
          </div>

          {/* Register Form */}
          <Form
            name="register"
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
            style={{ marginBottom: 0 }}
          >
            {/* Personal Information Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "24px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Title
                level={5}
                style={{
                  marginBottom: "20px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Personal Information
              </Title>

              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        First Name
                      </Text>
                    }
                    name="firstName"
                    rules={[
                      {
                        required: true,
                        message: "Please input your first name!",
                      },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <Input
                      prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
                      placeholder="Enter first name"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Last Name
                      </Text>
                    }
                    name="lastName"
                    rules={[
                      {
                        required: true,
                        message: "Please input your last name!",
                      },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <Input
                      prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
                      placeholder="Enter last name"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label={
                  <Text style={{ fontWeight: 500, color: "#374151" }}>
                    Username
                  </Text>
                }
                name="username"
                rules={[
                  { required: true, message: "Please input your username!" },
                ]}
                style={{ marginBottom: 0 }}
              >
                <Input
                  prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
                  placeholder="Choose a username"
                  style={{
                    borderRadius: "12px",
                    border: "1px solid rgba(209, 213, 219, 0.8)",
                    background: "rgba(255, 255, 255, 0.9)",
                    height: "44px",
                  }}
                />
              </Form.Item>
            </div>

            {/* Contact Information Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "24px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Title
                level={5}
                style={{
                  marginBottom: "20px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Contact Information
              </Title>

              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Country Code
                      </Text>
                    }
                    name="countryCode"
                    initialValue="+91"
                    rules={[
                      { required: true, message: "Country code is required" },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <select
                      style={{
                        width: "100%",
                        height: 44,
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        borderRadius: "12px",
                        fontSize: 16,
                        padding: "0 12px",
                        background: "rgba(255, 255, 255, 0.9)",
                        color: "#374151",
                      }}
                    >
                      <option value="+1">+1 (US)</option>
                      <option value="+44">+44 (UK)</option>
                      <option value="+91">+91 (IN)</option>
                    </select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={16}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Phone Number
                      </Text>
                    }
                    name="phone"
                    rules={[
                      { required: true, message: "Phone required" },
                      { pattern: /^\d{7,14}$/, message: "7-14 digits" },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <Input
                      placeholder="Enter phone number"
                      maxLength={14}
                      minLength={7}
                      type="tel"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Organization Code
                      </Text>
                    }
                    name="tenantCode"
                    rules={[
                      {
                        required: true,
                        message: "Please input organization code!",
                      },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <Input
                      prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
                      placeholder="Enter organization code"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Email Address
                      </Text>
                    }
                    name="email"
                    rules={[
                      { required: true, message: "Please input your email!" },
                      { type: "email", message: "Please enter a valid email!" },
                    ]}
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      prefix={<MailOutlined style={{ color: "#9ca3af" }} />}
                      placeholder="Enter email address"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* Security Section */}
            <div
              style={{
                background: "rgba(248, 250, 252, 0.6)",
                borderRadius: "16px",
                padding: "24px",
                marginBottom: "24px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <Title
                level={5}
                style={{
                  marginBottom: "20px",
                  color: "#374151",
                  fontWeight: 600,
                }}
              >
                Security
              </Title>

              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Password
                      </Text>
                    }
                    name="password"
                    rules={[
                      {
                        required: true,
                        message: "Please input your password!",
                      },
                      { min: 6, message: "At least 6 characters!" },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <Input.Password
                      prefix={<LockOutlined style={{ color: "#9ca3af" }} />}
                      placeholder="Enter password"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <Text style={{ fontWeight: 500, color: "#374151" }}>
                        Confirm Password
                      </Text>
                    }
                    name="confirmPassword"
                    rules={[
                      {
                        required: true,
                        message: "Please confirm your password!",
                      },
                    ]}
                    style={{ marginBottom: 16 }}
                  >
                    <Input.Password
                      prefix={<LockOutlined style={{ color: "#9ca3af" }} />}
                      placeholder="Confirm password"
                      style={{
                        borderRadius: "12px",
                        border: "1px solid rgba(209, 213, 219, 0.8)",
                        background: "rgba(255, 255, 255, 0.9)",
                        height: "44px",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="terms"
                valuePropName="checked"
                style={{ marginBottom: 0 }}
              >
                <Checkbox
                  style={{
                    fontSize: "14px",
                    color: "#374151",
                  }}
                >
                  I agree to the&nbsp;
                  <Link
                    href="/terms"
                    style={{
                      color: "#667eea",
                      fontWeight: 500,
                      textDecoration: "none",
                    }}
                  >
                    Terms of Service
                  </Link>
                  &nbsp;and&nbsp;
                  <Link
                    href="/privacy"
                    style={{
                      color: "#667eea",
                      fontWeight: 500,
                      textDecoration: "none",
                    }}
                  >
                    Privacy Policy
                  </Link>
                </Checkbox>
              </Form.Item>
            </div>

            {/* Alerts */}
            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                style={{
                  borderRadius: "12px",
                  marginBottom: "24px",
                  border: "1px solid rgba(239, 68, 68, 0.2)",
                  background: "rgba(254, 242, 242, 0.8)",
                }}
              />
            )}

            {success && (
              <Alert
                message={success}
                type="success"
                showIcon
                style={{
                  borderRadius: "12px",
                  marginBottom: "24px",
                  border: "1px solid rgba(34, 197, 94, 0.2)",
                  background: "rgba(240, 253, 244, 0.8)",
                }}
              />
            )}

            {/* Submit Button */}
            <Form.Item style={{ marginBottom: "32px" }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
                style={{
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  borderColor: "transparent",
                  borderRadius: "12px",
                  height: "48px",
                  fontSize: "16px",
                  fontWeight: 600,
                  boxShadow: "0 4px 12px rgba(102, 126, 234, 0.4)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-2px)";
                  target.style.boxShadow =
                    "0 6px 20px rgba(102, 126, 234, 0.6)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow =
                    "0 4px 12px rgba(102, 126, 234, 0.4)";
                }}
              >
                {loading ? "Creating Account..." : "Create Account"}
              </Button>
            </Form.Item>
          </Form>

          {/* Footer */}
          <div
            style={{
              textAlign: "center",
              paddingTop: "24px",
              borderTop: "1px solid rgba(226, 232, 240, 0.5)",
            }}
          >
            <Text
              style={{
                color: "#6b7280",
                fontSize: "14px",
                marginBottom: "12px",
                display: "block",
              }}
            >
              Already have an account?
            </Text>
            <Button
              type="link"
              style={{
                color: "#667eea",
                fontSize: "15px",
                fontWeight: 600,
                padding: 0,
                height: "auto",
                textDecoration: "none",
              }}
              onClick={() => router.push("/login")}
            >
              Sign in here
            </Button>
          </div>
        </Card>
      </Content>
    </Layout>
  );
}
