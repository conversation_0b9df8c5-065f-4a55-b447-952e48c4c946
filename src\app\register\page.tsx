"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Form, 
  Input, 
  Button, 
  Alert, 
  Checkbox, 
  Card, 
  Typography, 
  Space,
  Divider 
} from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";
import { supabaseClient } from "@/utils/supabase/client";

const { Title, Text, Link } = Typography;

export default function CustomRegister() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const router = useRouter();

  const onFinish = async (values: { 
    firstName: string; 
    lastName: string; 
    username: string;
    email: string; 
    password: string; 
    confirmPassword: string;
    terms: boolean;
    phone?: string;
    countryCode?: string;
    tenantCode: string;
  }) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Check if passwords match
    if (values.password !== values.confirmPassword) {
      setError("Passwords do not match");
      setLoading(false);
      return;
    }

    // Check if terms are accepted
    if (!values.terms) {
      setError("Please accept the terms and conditions");
      setLoading(false);
      return;
    }

    const { data, error } = await supabaseClient.auth.signUp({
      email: values.email,
      password: values.password,
      
      options: {
        data: {
          first_name: values.firstName,
          last_name: values.lastName,
          phone: values.countryCode+ ' '+ values.phone,
          username: values.username,
          tenant_code: values.tenantCode,
          avatar_url: null,
          party_type_key: "PLAYER",
         }
      }
    });
    
    setLoading(false);

    if (error) {
      setError(error.message);
    } else if (data?.user) {
      setSuccess("Registration successful! Please check your email to verify your account.");
      // Optionally redirect after a delay
      setTimeout(() => {
        router.push("/login");
      }, 3000);
    }
  };

  return (
    <div
      style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#f5f5f5',
      padding: '24px'
      }}
    >
      <Card
      style={{
        width: '100%',
        maxWidth: '400px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      }}
      styles={{ body: { padding: '24px 16px' } }}
      >
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        {/* Brand Header */}
        <div style={{ textAlign: 'center', marginBottom: 8 }}>
        {/* Use next/image for optimization */}
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src="/assets/logo1.png"
          alt="Logo"
          style={{ width: 64, height: 64 }}
        />
        <Title level={4} style={{ color: '#1890ff', fontWeight: 600, margin: 0 }}>
          {/* recallloop */}
        </Title>
        <Text type="secondary" style={{ fontSize: '14px' }}>
          Create your account
        </Text>
        </div>

        {/* Register Form */}
        <Form
        name="register"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        size="middle"
        style={{ marginBottom: 0 }}
        >
        <Form.Item
          label="First Name"
          name="firstName"
          rules={[{ required: true, message: 'Please input your first name!' }]}
          style={{ marginBottom: 8 }}
        >
          <Input
          prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="First name"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>

        <Form.Item
          label="Last Name"
          name="lastName"
          rules={[{ required: true, message: 'Please input your last name!' }]}
          style={{ marginBottom: 8 }}
        >
          <Input
          prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="Last name"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>

        <Form.Item
          label="Username"
          name="username"
          rules={[{ required: true, message: 'Please input your username!' }]}
          style={{ marginBottom: 8 }}
        >
          <Input
          prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="Username"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>

        <Form.Item
          label="Phone"
          style={{ marginBottom: 8 }}
          required
        >
          <Input.Group compact>
          <Form.Item
            name="countryCode"
            noStyle
            initialValue="+91"
            rules={[{ required: true, message: 'Country code is required' }]}
          >
            <select
            style={{
              width: 70,
              height: 32,
              border: '1px solid #d9d9d9',
              borderRadius: '6px 0 0 6px',
              fontSize: 14,
            }}
            >
            <option value="+1">+1</option>
            <option value="+44">+44</option>
            <option value="+91">+91</option>
            </select>
          </Form.Item>
          <Form.Item
            name="phone"
            noStyle
            rules={[
            { required: true, message: 'Phone required' },
            { pattern: /^\d{7,14}$/, message: '7-14 digits' },
            ]}
          >
            <Input
            style={{ width: 'calc(100% - 70px)', borderRadius: '0 6px 6px 0' }}
            placeholder="Phone"
            maxLength={14}
            minLength={7}
            type="tel"
            />
          </Form.Item>
          </Input.Group>
        </Form.Item>

        <Form.Item
          label="Tenant Code"
          name="tenantCode"
          rules={[{ required: true, message: 'Please input tenant code!' }]}
          style={{ marginBottom: 8 }}
        >
          <Input
          prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="Tenant Code"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>
        <Form.Item
          label="Email"
          name="email"
          rules={[
          { required: true, message: 'Please input your email!' },
          { type: 'email', message: 'Please enter a valid email!' }
          ]}
          style={{ marginBottom: 8 }}
        >
          <Input
          prefix={<MailOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="<EMAIL>"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>

        <Form.Item
          label="Password"
          name="password"
          rules={[
          { required: true, message: 'Please input your password!' },
          { min: 6, message: 'At least 6 characters!' }
          ]}
          style={{ marginBottom: 8 }}
        >
          <Input.Password
          prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="••••••••"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>

        <Form.Item
          label="Confirm"
          name="confirmPassword"
          rules={[{ required: true, message: 'Please confirm your password!' }]}
          style={{ marginBottom: 8 }}
        >
          <Input.Password
          prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
          placeholder="••••••••"
          style={{ borderRadius: '6px' }}
          />
        </Form.Item>

        <Form.Item
          name="terms"
          valuePropName="checked"
          style={{ marginBottom: 8 }}
        >
          <Checkbox>
          I agree to&nbsp;
          <Link href="/terms" style={{ color: '#1890ff' }}>
            Terms
          </Link>
          &nbsp;and&nbsp;
          <Link href="/privacy" style={{ color: '#1890ff' }}>
            Privacy
          </Link>
          </Checkbox>
        </Form.Item>

        {error && (
          <Form.Item>
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ borderRadius: '6px' }}
          />
          </Form.Item>
        )}

        {success && (
          <Form.Item>
          <Alert
            message={success}
            type="success"
            showIcon
            style={{ borderRadius: '6px' }}
          />
          </Form.Item>
        )}

        <Form.Item style={{ marginBottom: 0 }}>
          <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
          style={{
            height: '38px',
            borderRadius: '6px',
            fontSize: '15px',
            fontWeight: 500
          }}
          >
          Create Account
          </Button>
        </Form.Item>
        </Form>

        <Divider plain style={{ margin: '8px 0 0 0' }}>
        <Text type="secondary" style={{ fontSize: '13px' }}>
          Already have an account?
        </Text>
        </Divider>

        <div style={{ textAlign: 'center', marginTop: 0 }}>
        <Button
          type="link"
          style={{ fontSize: '13px', fontWeight: 500, padding: 0 }}
          onClick={() => router.push("/login")}
        >
          Sign in
        </Button>
        </div>
      </Space>
      </Card>
    </div>
  );
}