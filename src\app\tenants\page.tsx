"use client";
import React, { useEffect, useState } from "react";
import { Input, Button, Card, Spin, Modal, Tag, Layout, Typography, Row, Col, Avatar } from "antd";
import { PlusOutlined, SearchOutlined, UserOutlined, TeamOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import { ListTenantResponse, Tenant } from "@types";
import { getColumns } from "./column";
import { CommonTable } from "@components/common-table";

const { Content } = Layout;
const { Title, Text } = Typography;

const PAGE_SIZE = 7;

export default function TenantsPage() {
  const [filter, setFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [data, setData] = useState<ListTenantResponse>([]);
  const [isTenantModalVisible, setIsTenantModalVisible] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);

  useEffect(() => {
    setLoading(true);
    dataProviderInstance
      .listTenants()
      .then((dat) => {
        setData(dat.data.data);
      })
      .catch(() => {
        setData([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const filteredData: Tenant[] = data
    ?.filter((item) =>
      item.tenant_name.toLowerCase().includes(filter.toLowerCase())
    )
    .map((item) => ({
      id: item.id,
      name: item.tenant_name,
      email: item.email,
      avatar_url: item.avatar_url as string,
      status: item.status,
      caretakers: item.caregiver_count,
      players: item.player_count,
      phone: item.phone_number,
      tenantCode: item.tenant_code,
      createOn: item.created_at
        ? new Date(item.created_at).toISOString().slice(2, 10).replace(/-/g, "")
        : "",
    }));

  const handleViewTenant = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setIsTenantModalVisible(true);
  };

  const columns = getColumns(currentPage, PAGE_SIZE, handleViewTenant);

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "150px",
          height: "150px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1400,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header Section */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",
          borderRadius: "24px",
          padding: "40px 32px",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "42px",
              letterSpacing: "-1px",
            }}
          >
            Tenant Management
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
              lineHeight: "1.6",
            }}
          >
            Manage tenants, caregivers, and players across your platform
          </Text>
        </div>

        {/* Main Content Card */}
        <Card
          style={{
            background: "rgba(255, 255, 255, 0.9)",
            backdropFilter: "blur(10px)",
            borderRadius: "24px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            padding: "32px",
          }}
          bodyStyle={{ padding: 0 }}
        >
          {/* Controls Section */}
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "space-between",
              alignItems: "center",
              gap: 24,
              marginBottom: 32,
              padding: "24px",
              background: "rgba(248, 250, 252, 0.8)",
              borderRadius: "16px",
              border: "1px solid rgba(226, 232, 240, 0.5)",
            }}
          >
            <div style={{
              flex: 1,
              minWidth: 280,
              display: "flex",
              alignItems: "center",
              gap: 12,
            }}>
              <Text style={{
                color: "#374151",
                fontSize: "16px",
                fontWeight: 600,
                minWidth: "fit-content",
              }}>
                Search Tenants:
              </Text>
              <Input
                placeholder="Search by tenant name..."
                prefix={<SearchOutlined style={{ color: "#9ca3af" }} />}
                style={{
                  borderRadius: "12px",
                  height: "44px",
                  border: "2px solid #e5e7eb",
                  fontSize: "14px",
                  transition: "all 0.3s ease",
                }}
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                onFocus={(e) => {
                  const target = e.target as HTMLInputElement;
                  target.style.borderColor = "#667eea";
                  target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                }}
                onBlur={(e) => {
                  const target = e.target as HTMLInputElement;
                  target.style.borderColor = "#e5e7eb";
                  target.style.boxShadow = "none";
                }}
              />
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                height: "48px",
                paddingLeft: "24px",
                paddingRight: "24px",
                fontSize: "16px",
                fontWeight: 600,
                borderRadius: "12px",
                boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                transition: "all 0.3s ease",
                minWidth: 160,
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(-2px)";
                target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLElement;
                target.style.transform = "translateY(0)";
                target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
              }}
              onClick={() => router.push("/tenants/create")}
            >
              Add New Tenant
            </Button>
          </div>
          {/* Table Section */}
          <div style={{
            background: "rgba(255, 255, 255, 0.8)",
            borderRadius: "16px",
            padding: "24px",
            border: "1px solid rgba(226, 232, 240, 0.5)",
          }}>
            <div style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "24px",
            }}>
              <Title level={4} style={{
                color: "#374151",
                margin: 0,
                fontSize: "20px",
                fontWeight: 600,
              }}>
                Tenants List ({filteredData.length})
              </Title>
              {filteredData.length > 0 && (
                <div style={{
                  display: "flex",
                  gap: "16px",
                  alignItems: "center",
                }}>
                  <div style={{
                    padding: "8px 16px",
                    background: "rgba(102, 126, 234, 0.1)",
                    borderRadius: "20px",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                  }}>
                    <TeamOutlined style={{ color: "#667eea" }} />
                    <Text style={{
                      color: "#667eea",
                      fontSize: "14px",
                      fontWeight: 600,
                    }}>
                      Total Caregivers: {filteredData.reduce((sum, tenant) => sum + (tenant.caretakers || 0), 0)}
                    </Text>
                  </div>
                  <div style={{
                    padding: "8px 16px",
                    background: "rgba(16, 185, 129, 0.1)",
                    borderRadius: "20px",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                  }}>
                    <UserOutlined style={{ color: "#10b981" }} />
                    <Text style={{
                      color: "#10b981",
                      fontSize: "14px",
                      fontWeight: 600,
                    }}>
                      Total Players: {filteredData.reduce((sum, tenant) => sum + (tenant.players || 0), 0)}
                    </Text>
                  </div>
                </div>
              )}
            </div>

            <Spin spinning={loading}>
              <div style={{
                overflowX: "auto",
                borderRadius: "12px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}>
                <CommonTable
                  key={currentPage}
                  columns={columns}
                  dataSource={filteredData}
                  rowKey="id"
                  pagination={{
                    pageSize: PAGE_SIZE,
                    current: currentPage,
                    onChange: (page: number) => setCurrentPage(page),
                    showSizeChanger: false,
                    style: {
                      padding: "16px 24px",
                      background: "rgba(248, 250, 252, 0.8)",
                    }
                  }}
                  style={{
                    background: "white",
                  }}
                />
              </div>
            </Spin>
          </div>
        </Card>
      </Content>

      {/* Enhanced Modal */}
      <Modal
        title={
          <div style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            fontSize: "20px",
            fontWeight: 700,
          }}>
            Tenant Details
          </div>
        }
        open={isTenantModalVisible}
        onCancel={() => setIsTenantModalVisible(false)}
        footer={null}
        width={500}
        style={{
          borderRadius: "16px",
        }}
        styles={{
          content: {
            borderRadius: "16px",
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(10px)",
          }
        }}
      >
        {selectedTenant && (
          <div style={{ padding: "20px 0" }}>
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "16px",
              marginBottom: "24px",
              padding: "16px",
              background: "rgba(102, 126, 234, 0.05)",
              borderRadius: "12px",
            }}>
              <Avatar
                src={selectedTenant.avatar_url}
                size={64}
                style={{
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                }}
                onError={() => true}
              >
                {selectedTenant.name.charAt(0).toUpperCase()}
              </Avatar>
              <div>
                <Title level={4} style={{
                  margin: 0,
                  color: "#374151",
                  fontSize: "18px",
                }}>
                  {selectedTenant.name}
                </Title>
                <Tag color={selectedTenant.status ? "green" : "red"} style={{
                  marginTop: "4px",
                  borderRadius: "12px",
                  padding: "2px 8px",
                }}>
                  {selectedTenant.status ? "Active" : "Inactive"}
                </Tag>
              </div>
            </div>

            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div style={{
                  padding: "16px",
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "12px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}>
                  <Text style={{
                    color: "#6b7280",
                    fontSize: "12px",
                    fontWeight: 600,
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    display: "block",
                    marginBottom: "4px",
                  }}>
                    Email Address
                  </Text>
                  <Text style={{
                    color: "#374151",
                    fontSize: "16px",
                    fontWeight: 500,
                  }}>
                    {selectedTenant.email || "Not provided"}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{
                  padding: "16px",
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "12px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}>
                  <Text style={{
                    color: "#6b7280",
                    fontSize: "12px",
                    fontWeight: 600,
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    display: "block",
                    marginBottom: "4px",
                  }}>
                    Phone Number
                  </Text>
                  <Text style={{
                    color: "#374151",
                    fontSize: "16px",
                    fontWeight: 500,
                  }}>
                    {selectedTenant.phone || "Not provided"}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{
                  padding: "16px",
                  background: "rgba(248, 250, 252, 0.8)",
                  borderRadius: "12px",
                  border: "1px solid rgba(226, 232, 240, 0.5)",
                }}>
                  <Text style={{
                    color: "#6b7280",
                    fontSize: "12px",
                    fontWeight: 600,
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    display: "block",
                    marginBottom: "4px",
                  }}>
                    Tenant Code
                  </Text>
                  <Text style={{
                    color: "#374151",
                    fontSize: "16px",
                    fontWeight: 500,
                  }}>
                    {selectedTenant.tenantCode}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{
                  padding: "16px",
                  background: "rgba(102, 126, 234, 0.1)",
                  borderRadius: "12px",
                  border: "1px solid rgba(102, 126, 234, 0.2)",
                }}>
                  <Text style={{
                    color: "#667eea",
                    fontSize: "12px",
                    fontWeight: 600,
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    display: "block",
                    marginBottom: "4px",
                  }}>
                    Caregivers
                  </Text>
                  <Text style={{
                    color: "#667eea",
                    fontSize: "20px",
                    fontWeight: 700,
                  }}>
                    {selectedTenant.caretakers || 0}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{
                  padding: "16px",
                  background: "rgba(16, 185, 129, 0.1)",
                  borderRadius: "12px",
                  border: "1px solid rgba(16, 185, 129, 0.2)",
                }}>
                  <Text style={{
                    color: "#10b981",
                    fontSize: "12px",
                    fontWeight: 600,
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    display: "block",
                    marginBottom: "4px",
                  }}>
                    Players
                  </Text>
                  <Text style={{
                    color: "#10b981",
                    fontSize: "20px",
                    fontWeight: 700,
                  }}>
                    {selectedTenant.players || 0}
                  </Text>
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .tenant-row {
          transition: all 0.3s ease;
        }

        .tenant-row:hover {
          background: rgba(102, 126, 234, 0.05) !important;
          transform: translateY(-1px);
        }
      `}</style>

      <style jsx global>{`
        @media (max-width: 600px) {
          .ant-modal {
            width: 95vw !important;
            max-width: 95vw !important;
          }
        }

        .ant-table-thead > tr > th {
          background: rgba(102, 126, 234, 0.1) !important;
          color: #374151 !important;
          font-weight: 600 !important;
          border-bottom: 2px solid rgba(102, 126, 234, 0.2) !important;
        }

        .ant-table-tbody > tr > td {
          border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
        }

        .ant-table-tbody > tr:hover > td {
          background: rgba(102, 126, 234, 0.05) !important;
        }
      `}</style>
    </Layout>
  );
}
