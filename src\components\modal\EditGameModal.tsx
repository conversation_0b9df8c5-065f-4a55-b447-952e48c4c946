import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  DatePicker,
  Spin,
  message,
  Upload,
  Table,
  InputNumber,
} from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { TableColumnsType } from "antd";
import {
  EditOutlined,
  PlayCircleOutlined,
  PictureOutlined,
} from "@ant-design/icons";
import RichTextEditor from "@components/RichTextEditor";
import {
  CategoryItem,
  CategoryOption,
  EditGameModalProps,
  FormValues,
  Game,
} from "@types";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { dataProviderInstance } from "@providers/data-provider";
import { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { supabaseClient } from "@utils/supabase/client";
const { Title, Text } = Typography;
interface LevelInput {
   level_title?: string;
  level_order?: number;
  time_limit?: number;
  score_required?: number;
  duration?: number;
  difficulty_level?: string;
  badge_id?: string;
  errors?: {
    [field: string]: string;
  };
}

const EditGameModal: React.FC<EditGameModalProps> = ({
  visible,
  onClose,
  gameId,
  onSuccess,
  gameDetails,
}) => {
  const [form] = Form.useForm();
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loading, setLoading] = useState(false);
const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [gameData, setGameData] = useState<Game | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [levelsData, setLevelsData] = useState<LevelInput[]>([]);
  const [levelCount, setLevelCount] = useState(0);
  const [loadingBadges, setLoadingBadges] = useState(true);
  const [badgeOptions, setBadgeOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const difficultyOptions = [
    { label: "Easy", value: "easy" },
    { label: "Medium", value: "medium" },
    { label: "Hard", value: "hard" },
  ];
  const levelColumns: TableColumnsType<LevelInput> = [
    {
      title: "Level No",
      dataIndex: "level_no",
      render: (_: unknown, __: LevelInput, index: number) => index + 1,
    },
    {
      title: "Level Title",
      dataIndex: "level_title",
      width: 130,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Input
          placeholder="Enter title"
          value={record.level_title}
          onChange={(e) =>
            handleLevelChange(index, "level_title", e.target.value)
          }
          status={!record.level_title ? "error" : undefined}
        />
      ),
    },
    {
      title: "Level Order",
      dataIndex: "level_order",
      width: 90,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Input
          type="number"
          placeholder="Order"
          value={record.level_order}
          onChange={(e) =>
            handleLevelChange(index, "level_order", Number(e.target.value))
          }
           status={!record.level_order ? "error" : undefined}
        />
      ),
    },
    {
      title: "Duration",
      dataIndex: "duration",
      key: "duration",
      width: 90,
      responsive: ["md"],
      render: (_: unknown, record: LevelInput, index: number) => (
        <InputNumber
          min={0}
          value={record.duration}
            status={
            !record.duration 
              ? "error"
              : undefined
          }
          onChange={(value) => handleLevelChange(index, "duration", value || 0)}
        />
      ),
    },
    {
      title: "Difficulty Level",
      dataIndex: "difficulty_level",
      key: "difficulty_level",
      width: 130,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Select
          options={difficultyOptions}
          value={record.difficulty_level}
            status={!record.difficulty_level ? "error" : undefined}
          onChange={(value) =>
            handleLevelChange(index, "difficulty_level", value)
          }
        />
      ),
    },
    {
      title: "Score Required",
      dataIndex: "score_required",
      width: 100,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Input
          type="number"
          placeholder="Score"
          value={record.score_required}
           status={!record.score_required ? "error" : undefined}
          onChange={(e) =>
            handleLevelChange(index, "score_required", Number(e.target.value))
          }
        />
      ),
    },
    {
      title: "Badge",
      dataIndex: "badge_id",
      key: "badge_id",
      width: 160,
      render: (_: unknown, record: LevelInput, index: number) => (
        <Select
          placeholder="Select badge"
          options={badgeOptions}
          value={record.badge_id}
          loading={loadingBadges}
          status={!record.badge_id ? "error" : undefined}
          onChange={(value) => handleLevelChange(index, "badge_id", value)}
          style={{
            borderRadius: 12,
            fontSize: 16,
            width: "100%",
          }}
        />
      ),
    },
    {
      title: "",
      key: "action",
      width: 50, // Optional: limits the column width
      render: (_: unknown, __: LevelInput, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeLevel(index)}
        />
      ),
    },
  ];
  useEffect(() => {
    if (visible) {
      loadCategories();
      loadBadges();
    }
  }, [visible]);
  const loadBadges = async () => {
    setLoadingBadges(true);
    try {
      const response: { data: { id: string; name: string }[] } =
        await dataProviderInstance.listBadges();
      if (response && response.data) {
        setBadgeOptions(
          response.data.map((reward) => ({
            label: reward.name,
            value: reward.id,
          }))
        );
      }
    } catch (error) {
      console.error("Error fetching badges:", error);
      message.error(ERROR_MESSAGES.load_badges);
      setBadgeOptions([]);
    } finally {
      setLoadingBadges(false);
    }
  };
  useEffect(() => {
    if (visible && gameId) {
      if (gameDetails) {
        setGameData(gameDetails);
      }
    }
  }, [visible, gameId, gameDetails]);

  useEffect(() => {
    if (visible && gameData && categoryOptions.length > 0) {
      prefillFormData(gameData);
    }
  }, [visible, gameData, categoryOptions]);

  const loadCategories = async () => {
    setLoadingCategories(true);
    try {
      const response: { data: CategoryItem[] } =
        await dataProviderInstance.listGameCategories();
      if (response && response.data) {
        const formatted: CategoryOption[] = response.data.map(
          (item: CategoryItem) => ({
            label: item.name,
            value: item.id,
          })
        );
        setCategoryOptions(formatted);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      message.error(ERROR_MESSAGES.load_categories);
    } finally {
      setLoadingCategories(false);
    }
  };

  const prefillFormData = (game: Game) => {
    console.log("Prefilling form with game data:", game);
    console.log("Available properties:", Object.keys(game));

    setUploadedImageUrl(game.thumbnail_url || null);

    let categoryId: string | undefined = game.category_id;

    if (!categoryId && game.category_name) {
      const category = categoryOptions.find(
        (opt) => opt.label === game.category_name
      );
      categoryId = category?.value;
    }

    const formValues = {
      title: game.game_name,
      description: game.description,
      category: categoryId,
      no_of_levels: game.no_of_levels,
      duration: game.estimated_duration_sec || "",
      difficulty: game.difficulty_level || "",
      rules: game.rules || "",
      valid_from: game.valid_from ? dayjs(game.valid_from) : dayjs(),
      valid_upto: game.valid_to
        ? dayjs(game.valid_to)
        : dayjs().add(1, "month"),
    };
    if (game.levels && Array.isArray(game.levels)) {
      const mappedLevels = game.levels.map((level, index) => ({
        level_title: level.title || "",
        level_order: level.level_order ?? index + 1,
        duration: level.time_limit ?? 0,
        difficulty_level: level.difficulty_level || "easy",
        badge_id: level.badge_id || "",
        score_required: level.target_score ?? 0,
        time_limit: level.time_limit,
      }));
      setLevelsData(mappedLevels);
      setLevelCount(mappedLevels.length);
    }
    console.log("Setting form values:", formValues);
    form.setFieldsValue(formValues);
  };

  const handleClose = () => {
    form.resetFields();
    setLevelsData([]);
    setUploadedImageUrl("");
    setSelectedFile(null);
    setGameData(null);
    onClose();
  };

  const handleSubmit = async (values: FormValues) => {
    if (!gameId) {
      message.error("Game ID is required for update");
      return;
    }

    setLoading(true);
    try {
      let thumbnailUrl = uploadedImageUrl;

      if (selectedFile) {
        const fileName = `games/${Date.now()}_${selectedFile.name}`;
        const { error: uploadError } = await supabaseClient.storage
          .from("image-bucket")
          .upload(fileName, selectedFile, {
            cacheControl: "3600",
            upsert: true,
          });

        if (uploadError) throw uploadError;

        const { data: signedUrlData, error: signedUrlError } =
          await supabaseClient.storage
            .from("image-bucket")
            .createSignedUrl(fileName, 60 * 60 * 157680000);

        if (signedUrlError) throw signedUrlError;

        thumbnailUrl = signedUrlData?.signedUrl;
      }

      const updateParams = {
        game_id: gameId,
        category_id: values.category,
        name: values.title,
        description: values.description,
        thumbnail_url: thumbnailUrl,
        // no_levels: parseInt(values.no_of_levels),
        // estimated_duration_sec: values.duration || 0,
        //difficulty_level: values.difficulty,
        rules: values.rules,
        levels: levelsData.map((level, idx) => ({
          level_number: idx + 1,
          title: level.level_title,
       level_order: parseInt(level.level_order?.toString() ?? "0", 10),
          duration: parseInt(level.duration?.toString() ?? "0", 10),
          difficulty_level: level.difficulty_level,
          badge_id: level.badge_id,
          target_score: parseInt(level.score_required?.toString() ?? "0", 10),
        })),
        valid_from: values.valid_from?.toISOString(),
        valid_to: values.valid_upto?.toISOString(),
      };
      const result = await dataProviderInstance.updateGame(updateParams);

      if (result && result.data) {
        message.success("Game updated successfully!");
        onSuccess?.();
        handleClose();
      }
    } catch (error) {
      console.error("Error updating game:", error);
      message.error("Failed to update game. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setSelectedFile(file);

      return false;
    },
    showUploadList: false,
  };

  const modalStyles = {
    content: {
      borderRadius: "20px",
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      padding: "2px",
      border: "none",
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
    },
    header: {
      borderBottom: "none",
      padding: "0",
      background: "transparent",
    },
    body: {
      padding: "0",
      background: "white",
      borderRadius: "18px",
      margin: "0",
    },
  };
  const handleLevelChange = <K extends keyof LevelInput>(
    index: number,
    field: K,
    value: LevelInput[K]
  ) => {
    const updated = [...levelsData];
    updated[index][field] = value;
    setLevelsData(updated);
  };

  const removeLevel = (index: number) => {
    const updated = [...levelsData];
    updated.splice(index, 1);
    setLevelsData(updated);
    setLevelCount(updated.length);
  };
  const addNewLevel = () => {
    setLevelsData([
      ...levelsData,
      {
        level_title: "",
        level_order: levelCount + 1,
        time_limit: 0,
        score_required: 0,
        duration: 0,
        difficulty_level: "Easy",
        badge_id: "",
      },
    ]);
    setLevelCount(levelCount + 1);
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={900}
      centered
      styles={modalStyles}
      destroyOnClose
    >
      <div
        style={{
          background: "white",
          borderRadius: "18px",
          overflow: "hidden",
          minHeight: "480px",
        }}
      >
        {/* Header */}
        <div
          style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            padding: "16px 24px",
            color: "white",
            display: "flex",
            alignItems: "center",
            gap: "12px",
          }}
        >
          <div
            style={{
              width: "36px",
              height: "36px",
              borderRadius: "10px",
              background: "rgba(255, 255, 255, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backdropFilter: "blur(10px)",
            }}
          >
            <EditOutlined style={{ fontSize: "18px", color: "white" }} />
          </div>
          <div>
            <Title
              level={3}
              style={{
                margin: 0,
                color: "white",
                fontWeight: 700,
                fontSize: "28px",
              }}
            >
              Edit Exercise
            </Title>
            <Text
              style={{
                color: "rgba(255, 255, 255, 0.8)",
                fontSize: "16px",
              }}
            >
              Update exercise details and configuration
            </Text>
          </div>
        </div>

        {/* Content */}
        <div style={{ padding: "20px" }}>
          {/* Form */}
          <Form
            form={form}
            layout="vertical"
            size="large"
            onFinish={handleSubmit}
            requiredMark={false}
          >
            <div
              style={{
                marginBottom: 20,
                background: "rgba(248, 250, 252, 0.8)",
                borderRadius: "20px",
                padding: "20px",
                border: "1px solid rgba(226, 232, 240, 0.5)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  marginBottom: "20px",
                }}
              >
                <div
                  style={{
                    width: "48px",
                    height: "48px",
                    borderRadius: "12px",
                    background:
                      "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <PlayCircleOutlined
                    style={{ color: "white", fontSize: "20px" }}
                  />
                </div>
                <Title
                  level={3}
                  style={{
                    margin: 0,
                    color: "#374151",
                    fontWeight: 700,
                    fontSize: "24px",
                  }}
                >
                  Exercise Information
                </Title>
              </div>

              <Row gutter={32}>
                <Col xs={24} lg={14}>
                  <Row gutter={[24, 8]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                              marginBottom: "8px",
                              display: "block",
                            }}
                          >
                            Select Category *
                          </Text>
                        }
                        name="category"
                        style={{ marginBottom: 12 }}
                        rules={[
                          { required: true, message: "Please select category" },
                        ]}
                      >
                        <Select
                          placeholder="Select exercise category"
                          options={categoryOptions}
                          loading={loadingCategories}
                          style={{
                            borderRadius: "12px",
                            height: "50px",
                            fontSize: "16px",
                          }}
                          notFoundContent={
                            loadingCategories ? (
                              <Spin size="small" />
                            ) : (
                              "No categories found"
                            )
                          }
                        />
                      </Form.Item>
                    </Col>

                    <Col xs={24} md={12}>
                      <Form.Item
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                              marginBottom: "8px",
                              display: "block",
                            }}
                          >
                            Title *
                          </Text>
                        }
                        name="title"
                        style={{ marginBottom: 12 }}
                        rules={[
                          {
                            required: true,
                            message: "Please enter exercise title",
                          },
                        ]}
                      >
                        <Input
                          placeholder="Enter exercise title"
                          style={{
                            borderRadius: "12px",
                            height: "50px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = "#667eea";
                            e.target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = "#e5e7eb";
                            e.target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[24, 8]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                              marginBottom: "8px",
                              display: "block",
                            }}
                          >
                            Valid From Date *
                          </Text>
                        }
                        name="valid_from"
                        style={{ marginBottom: 12 }}
                        rules={[
                          {
                            required: true,
                            message: "Please select valid from date",
                          },
                        ]}
                      >
                        <DatePicker
                          placeholder="Select valid from date"
                          style={{
                            width: "100%",
                            height: "50px",
                            borderRadius: "12px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                          }}
                          format="DD/MM/YYYY"
                          onChange={() => form.validateFields(["valid_upto"])}
                        />
                      </Form.Item>
                    </Col>

                    <Col xs={24} md={12}>
                      <Form.Item
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                              marginBottom: "8px",
                              display: "block",
                            }}
                          >
                            Valid Until Date *
                          </Text>
                        }
                        name="valid_upto"
                        style={{ marginBottom: 12 }}
                        rules={[
                          {
                            required: true,
                            message: "Please select valid upto date",
                          },
                          ({ getFieldValue }) => ({
                            validator(_, value: Dayjs) {
                              const validFrom: Dayjs =
                                getFieldValue("valid_from");
                              if (
                                !value ||
                                !validFrom ||
                                value.isAfter(validFrom)
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(
                                  "Valid upto date must be after valid from date"
                                )
                              );
                            },
                          }),
                        ]}
                      >
                        <DatePicker
                          placeholder="Select valid until date"
                          style={{
                            width: "100%",
                            height: "50px",
                            borderRadius: "12px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                          }}
                          format="DD/MM/YYYY"
                          disabledDate={(current) => {
                            const validFrom: Dayjs =
                              form.getFieldValue("valid_from");
                            return (
                              validFrom &&
                              current &&
                              current.isBefore(validFrom, "day")
                            );
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col xs={24}>
                      <Form.Item
                        label={
                          <Text
                            style={{
                              color: "#374151",
                              fontWeight: 600,
                              fontSize: "14px",
                              marginBottom: "8px",
                              display: "block",
                            }}
                          >
                            Description *
                          </Text>
                        }
                        name="description"
                        style={{ marginBottom: 12 }}
                        rules={[
                          {
                            required: true,
                            message: "Please enter exercise description",
                          },
                        ]}
                      >
                        <Input.TextArea
                          placeholder="Enter detailed exercise description"
                          rows={4}
                          style={{
                            width: "100%",
                            borderRadius: "12px",
                            border: "2px solid #e5e7eb",
                            fontSize: "16px",
                            resize: "none",
                            transition: "all 0.3s ease",
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = "#667eea";
                            e.target.style.boxShadow =
                              "0 0 0 3px rgba(102, 126, 234, 0.1)";
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = "#e5e7eb";
                            e.target.style.boxShadow = "none";
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>

                <Col xs={24} lg={10}>
                  <div
                    style={{
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                      borderRadius: "16px",
                      padding: "20px",
                      border: "1px solid rgba(226, 232, 240, 0.8)",
                      boxShadow: "0 12px 24px rgba(0, 0, 0, 0.04)",
                      position: "relative",
                      overflow: "hidden",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "12px",
                        marginBottom: "6px",
                        position: "relative",
                        zIndex: 1,
                      }}
                    >
                      <div
                        style={{
                          width: "38px",
                          height: "38px",
                          borderRadius: "10px",
                          background:
                            "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 8px 16px rgba(102, 126, 234, 0.2)",
                        }}
                      >
                        <PictureOutlined
                          style={{ color: "white", fontSize: "16px" }}
                        />
                      </div>
                      <div>
                        <Title
                          level={5}
                          style={{
                            margin: 0,
                            color: "#1f2937",
                            fontWeight: 700,
                            fontSize: "16px",
                            marginBottom: "2px",
                          }}
                        >
                          Exercise Thumbnail
                        </Title>
                        <Text
                          style={{
                            color: "#6b7280",
                            fontSize: "13px",
                            fontWeight: 500,
                          }}
                        >
                          Upload an engaging preview image
                        </Text>
                      </div>
                    </div>

                    <div
                      style={{
                        height: 180,
                        borderRadius: "12px",
                        position: "relative",
                        background: uploadedImageUrl
                          ? "transparent"
                          : "linear-gradient(135deg, #f8fafc, #f1f5f9)",
                        overflow: "hidden",
                        border: uploadedImageUrl
                          ? "2px solid #10b981"
                          : "2px dashed #cbd5e1",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        zIndex: 1,
                        marginBottom: "12px",
                      }}
                    >
                      {uploadedImageUrl ? (
                        <>
                          <img
                            src={uploadedImageUrl}
                            alt="Exercise Thumbnail"
                            style={{
                              width: "100%",
                              height: "100%",
                              objectFit: "cover",
                            }}
                          />
                        </>
                      ) : (
                        <div
                          style={{
                            textAlign: "center",
                            color: "#64748b",
                          }}
                        >
                          <div
                            style={{
                              width: "50px",
                              height: "50px",
                              background:
                                "linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)",
                              borderRadius: "50%",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              margin: "0 auto 12px",
                            }}
                          >
                            <PictureOutlined
                              style={{ fontSize: "20px", color: "#94a3b8" }}
                            />
                          </div>
                          <Text
                            style={{
                              color: "#475569",
                              fontSize: "14px",
                              fontWeight: 600,
                              display: "block",
                              marginBottom: "4px",
                            }}
                          >
                            Drop your image here
                          </Text>
                          <Text
                            style={{
                              color: "#94a3b8",
                              fontSize: "12px",
                            }}
                          >
                            or click to browse
                          </Text>
                        </div>
                      )}

                      <Upload {...uploadProps}>
                        <Button
                          icon={<EditOutlined />}
                          style={{
                            position: "absolute",
                            top: "12px",
                            right: "12px",
                            background: "rgba(255, 255, 255, 0.95)",
                            backdropFilter: "blur(10px)",
                            border: "1px solid rgba(102, 126, 234, 0.3)",
                            color: "#667eea",
                            borderRadius: "12px",
                            height: "32px",
                            fontWeight: 600,
                            fontSize: "12px",
                            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                            zIndex: 10,
                            transition: "all 0.3s ease",
                          }}
                          onMouseEnter={(e) => {
                            const target = e.currentTarget;
                            target.style.background = "#667eea";
                            target.style.color = "white";
                            target.style.transform = "translateY(-1px)";
                          }}
                          onMouseLeave={(e) => {
                            const target = e.currentTarget;
                            target.style.background =
                              "rgba(255, 255, 255, 0.95)";
                            target.style.color = "#667eea";
                            target.style.transform = "translateY(0)";
                          }}
                        ></Button>
                      </Upload>
                    </div>

                    <div
                      style={{
                        padding: "12px",
                        background: "rgba(59, 130, 246, 0.05)",
                        borderRadius: "12px",
                        border: "1px solid rgba(59, 130, 246, 0.1)",
                        position: "relative",
                        zIndex: 1,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "6px",
                          marginBottom: "6px",
                        }}
                      >
                        <div
                          style={{
                            width: "4px",
                            height: "4px",
                            background: "#3b82f6",
                            borderRadius: "50%",
                          }}
                        />
                        <Text
                          style={{
                            color: "#1e40af",
                            fontSize: "12px",
                            fontWeight: 600,
                            margin: 0,
                          }}
                        >
                          Recommended: 1200×600px
                        </Text>
                      </div>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "6px",
                        }}
                      >
                        <div
                          style={{
                            width: "4px",
                            height: "4px",
                            background: "#10b981",
                            borderRadius: "50%",
                          }}
                        />
                        <Text
                          style={{
                            color: "#059669",
                            fontSize: "12px",
                            fontWeight: 600,
                            margin: 0,
                          }}
                        >
                          Max size: 5MB • JPG, PNG, WebP
                        </Text>
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
              <Row>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          marginBottom: "8px",
                          display: "block",
                        }}
                      >
                        Exercise Rules & Instructions *
                      </Text>
                    }
                    name="rules"
                    style={{ marginBottom: 12 }}
                    rules={[
                      {
                        required: true,
                        message: "Please enter exercise rules and instructions",
                      },
                    ]}
                  >
                    <RichTextEditor
                      placeholder="Enter detailed rules, instructions, and guidelines for this exercise..."
                      height={180}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col xs={24} md={24}>
                  <Form.Item
                    label={
                      <Text
                        style={{
                          color: "#374151",
                          fontWeight: 600,
                          fontSize: "14px",
                          marginBottom: "8px",
                          display: "block",
                        }}
                      >
                        Game Levels
                      </Text>
                    }
                  >
                    {levelsData.length === 0 && (
                      <Button
                        type="dashed"
                        onClick={addNewLevel}
                        block
                        icon="+"
                      >
                        Add Level
                      </Button>
                    )}
                    {levelsData.length > 0 && (
                      <Table
                        dataSource={levelsData}
                        columns={levelColumns}
                        pagination={false}
                        rowKey={(_, index) => index!.toString()}
                        style={{ marginTop: 16, minWidth: 200 }}
                        //scroll={{ x: "max-content" }}
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>

            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: "12px",
                paddingTop: "4px",
              }}
            >
              <Button
                size="large"
                onClick={handleClose}
                style={{
                  borderRadius: "8px",
                  height: "44px",
                  paddingLeft: "24px",
                  paddingRight: "24px",
                }}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                style={{
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                  borderRadius: "8px",
                  height: "44px",
                  paddingLeft: "32px",
                  paddingRight: "32px",
                  boxShadow: "0 4px 14px 0 rgba(102, 126, 234, 0.4)",
                }}
              >
                {loading ? "Updating..." : "Update Exercise"}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default EditGameModal;
