// Example usage of the dynamic GameAssets class

void main() {
  // Example 1: Word Game Assets
  final wordGameAssets = GameAssets({
    'words': ['apple', 'banana', 'cherry'],
    'difficulty': 'easy',
    'word_length_range': [3, 8],
  });

  // Example 2: Math Game Assets
  final mathGameAssets = GameAssets({
    'problems': [
      {'question': '2 + 2', 'answer': 4},
      {'question': '5 - 3', 'answer': 2},
    ],
    'operation_types': ['addition', 'subtraction'],
    'number_range': [1, 10],
  });

  // Example 3: Memory Game Assets
  final memoryGameAssets = GameAssets({
    'sequence': [1, 2, 3, 4],
    'sequence_length': 4,
    'memorization_time': 3000,
    'grid_size': '4x4',
    'colors': ['red', 'blue', 'green', 'yellow'],
  });

  // Example 4: Pattern Game Assets
  final patternGameAssets = GameAssets({
    'patterns': [
      {'type': 'circle', 'color': 'red', 'size': 'large'},
      {'type': 'square', 'color': 'blue', 'size': 'small'},
    ],
    'target_shapes': [
      {'type': 'triangle', 'color': 'green', 'size': 'medium'}
    ],
    'distractor_shapes': [
      {'type': 'circle', 'color': 'yellow', 'size': 'large'}
    ],
  });

  // Example 5: Custom Game Assets (completely new game type)
  final customGameAssets = GameAssets({
    'story_elements': ['character', 'setting', 'conflict'],
    'narrative_branches': [
      {'choice': 'go left', 'outcome': 'forest'},
      {'choice': 'go right', 'outcome': 'mountain'},
    ],
    'character_stats': {
      'health': 100,
      'energy': 50,
      'items': ['sword', 'potion']
    },
    'background_music': 'adventure_theme.mp3',
    'sound_effects': ['footsteps.wav', 'sword_clash.wav'],
  });

  // Usage examples:

  // 1. Using generic get/set methods
  print('Word game words: ${wordGameAssets.get<List<dynamic>>('words')}');
  print('Math game difficulty: ${mathGameAssets.get<String>('difficulty') ?? 'Not set'}');
  
  // Set new properties dynamically
  wordGameAssets.set('time_limit', 60);
  mathGameAssets.set('hints_enabled', true);

  // 2. Using convenience getters (for backward compatibility)
  print('Memory sequence: ${memoryGameAssets.sequence}');
  print('Pattern colors: ${patternGameAssets.colors}');

  // 3. Check if properties exist
  if (customGameAssets.has('story_elements')) {
    print('This is a story-based game');
  }

  // 4. Get all available properties
  print('Custom game properties: ${customGameAssets.keys.toList()}');

  // 5. Working with nested objects
  final characterStats = customGameAssets.get<Map<String, dynamic>>('character_stats');
  if (characterStats != null) {
    print('Character health: ${characterStats['health']}');
    print('Character items: ${characterStats['items']}');
  }

  // 6. Creating GameConfig with dynamic assets
  final gameConfig = GameConfig(
    id: 'custom_adventure_001',
    title: 'Adventure Quest',
    category: 'Adventure',
    level: 1,
    timer: 300,
    instructions: 'Choose your path wisely!',
    assets: customGameAssets,
    logic: GameLogic(minCorrect: 3, nextGameUnlocked: true),
    rewards: GameRewards(points: 100, badge: 'Explorer'),
  );

  // 7. JSON serialization/deserialization still works
  final json = gameConfig.toJson();
  final recreatedConfig = GameConfig.fromJson(json);
  
  print('Recreated game title: ${recreatedConfig.title}');
  print('Recreated assets keys: ${recreatedConfig.assets.keys.toList()}');
}

// Helper extension for type-safe asset access
extension GameAssetsExtension on GameAssets {
  // Add game-specific getters as needed
  
  // For word games
  List<String>? getWordList() => get<List<dynamic>>('words')?.cast<String>();
  int? getWordLengthMin() => get<List<dynamic>>('word_length_range')?.first;
  int? getWordLengthMax() => get<List<dynamic>>('word_length_range')?.last;
  
  // For math games
  List<String>? getOperationTypes() => get<List<dynamic>>('operation_types')?.cast<String>();
  int? getNumberRangeMin() => get<List<dynamic>>('number_range')?.first;
  int? getNumberRangeMax() => get<List<dynamic>>('number_range')?.last;
  
  // For story games
  List<String>? getStoryElements() => get<List<dynamic>>('story_elements')?.cast<String>();
  Map<String, dynamic>? getCharacterStats() => get<Map<String, dynamic>>('character_stats');
  
  // Generic list getter with type casting
  List<T>? getTypedList<T>(String key) => get<List<dynamic>>(key)?.cast<T>();
  
  // Generic map getter
  Map<String, T>? getTypedMap<T>(String key) => get<Map<String, dynamic>>(key)?.cast<String, T>();
}
