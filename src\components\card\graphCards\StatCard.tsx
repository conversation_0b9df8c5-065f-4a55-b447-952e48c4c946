"use client";
import React from "react";
import { Card, Typography, Progress } from "antd";
import { ArrowUpOutlined, ArrowDownOutlined } from "@ant-design/icons";

const { Text } = Typography;

export type StatCardProps = {
  title: string;
  value: number;
  percentage: number;
  status: "up" | "down";
  color: string;
  chartType: "line" | "progress";
  chartData?: { month: string; value: number }[];
  progressPercent?: number;
};

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  percentage,
  status,
  color,
  progressPercent = 75,
}) => {
  const ArrowIcon = status === "up" ? ArrowUpOutlined : ArrowDownOutlined;

  return (
    <Card
      style={{
        borderRadius: "20px",
        boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
        border: "1px solid rgba(255, 255, 255, 0.2)",
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(10px)",
        transition: "all 0.3s ease",
        height: "160px",
        cursor: "default",
      }}
      hoverable
      onMouseEnter={(e) => {
        const target = e.currentTarget as HTMLElement;
        target.style.transform = "translateY(-4px)";
        target.style.boxShadow = "0 12px 40px rgba(0,0,0,0.15)";
      }}
      onMouseLeave={(e) => {
        const target = e.currentTarget as HTMLElement;
        target.style.transform = "translateY(0)";
        target.style.boxShadow = "0 8px 32px rgba(0,0,0,0.1)";
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
          height: "100%",
        }}
      >
        <div style={{ flex: 1 }}>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "14px",
              fontWeight: 500,
              textTransform: "uppercase",
              letterSpacing: "0.5px",
              marginBottom: "8px",
              display: "block",
            }}
          >
            {title}
          </Text>
          <div
            style={{
              fontSize: "36px",
              fontWeight: 700,
              color: "#1f2937",
              marginBottom: "12px",
              lineHeight: 1,
            }}
          >
            {value.toLocaleString()}
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "6px",
              padding: "4px 8px",
              borderRadius: "8px",
              background:
                status === "up"
                  ? "rgba(16, 185, 129, 0.1)"
                  : "rgba(239, 68, 68, 0.1)",
              width: "fit-content",
            }}
          >
            <ArrowIcon
              style={{
                color,
                fontSize: "14px",
              }}
            />
            <Text
              style={{
                color,
                fontSize: "14px",
                fontWeight: 600,
              }}
            >
              {percentage}%
            </Text>
          </div>
        </div>
        <div
          style={{
            width: "90px",
            height: "90px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Progress
            type="circle"
            percent={progressPercent}
            width={80}
            strokeColor={{
              "0%": color,
              "100%": color + "80",
            }}
            strokeWidth={8}
            format={(percent) => (
              <span
                style={{
                  fontSize: "16px",
                  fontWeight: 600,
                  color: color,
                }}
              >
                {percent}%
              </span>
            )}
          />
        </div>
      </div>
    </Card>
  );
};

export default StatCard;
