{"name": "refine-project", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "cross-env NODE_OPTIONS=--max_old_space_size=4096 refine dev", "build": "refine build", "start": "refine start", "lint": "next lint", "refine": "refine"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/nextjs-registry": "^1.0.0", "@refinedev/antd": "^5.44.0", "@refinedev/cli": "^2.16.21", "@refinedev/core": "^4.47.1", "@refinedev/devtools": "^1.1.32", "@refinedev/kbar": "^1.3.6", "@refinedev/nextjs-router": "^6.0.0", "@refinedev/react-router-v6": "^4.6.2", "@refinedev/supabase": "^5.7.4", "@supabase/ssr": "^0.3.0", "antd": "^5.17.0", "js-cookie": "^3.0.5", "next": "^14.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "recharts": "^2.15.3"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^18.16.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/recharts": "^2.0.1", "@typescript-eslint/parser": "^6.21.0", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "^14.1.0", "typescript": "^5.4.2"}, "refine": {"projectId": "wfNw1D-Vg8iDN-YbOEp7"}}